# 🚀 生产环境部署检查清单

## 📋 部署前准备

### ☑️ 服务器准备
- [ ] 云服务器已购买并配置
- [ ] 服务器规格满足要求（2核4G+）
- [ ] 操作系统为Ubuntu 20.04+
- [ ] 已获取服务器IP地址
- [ ] SSH访问权限已配置
- [ ] 服务器安全组/防火墙已配置

### ☑️ 域名和证书（可选）
- [ ] 域名已购买
- [ ] DNS解析已配置
- [ ] SSL证书已准备
- [ ] 域名备案已完成（中国大陆）

### ☑️ 项目文件检查
- [ ] 智能体数据文件存在（data/agents.json）
- [ ] 智能体数量正确（20个）
- [ ] Docker配置文件完整
- [ ] 部署脚本权限正确
- [ ] 环境变量已配置

## 🔧 部署执行步骤

### 1️⃣ 服务器环境配置
```bash
# 检查项目文件
ls -la server-setup.sh deploy-production.sh

# 上传环境配置脚本
scp server-setup.sh ubuntu@YOUR_SERVER_IP:~/

# 执行环境配置
ssh ubuntu@YOUR_SERVER_IP 'chmod +x server-setup.sh && ./server-setup.sh'
```
- [ ] 环境配置脚本执行成功
- [ ] Docker安装完成
- [ ] Docker Compose安装完成
- [ ] 防火墙配置完成
- [ ] 用户权限配置完成

### 2️⃣ 应用部署
```bash
# 执行部署脚本
./deploy-production.sh YOUR_SERVER_IP ubuntu
```
- [ ] 部署包创建成功
- [ ] 文件上传成功
- [ ] Docker镜像构建成功
- [ ] 容器启动成功
- [ ] 健康检查通过

### 3️⃣ 部署验证
```bash
# 检查服务状态
curl http://YOUR_SERVER_IP/api/health

# 访问主要页面
curl -I http://YOUR_SERVER_IP
curl -I http://YOUR_SERVER_IP/chat
curl -I http://YOUR_SERVER_IP/agent-library
```
- [ ] 健康检查API响应正常
- [ ] 主页访问正常
- [ ] 智能对话页面正常
- [ ] 智能体管理页面正常
- [ ] 所有20个智能体加载正常

## 🌐 域名配置（可选）

### SSL证书配置
```bash
# Let's Encrypt证书申请
sudo certbot certonly --standalone -d your-domain.com

# 证书文件复制
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem /opt/finance-ai-demo/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem /opt/finance-ai-demo/ssl/key.pem
```
- [ ] SSL证书申请成功
- [ ] 证书文件复制完成
- [ ] Nginx配置更新
- [ ] HTTPS访问正常

## 📊 性能和监控检查

### 系统资源检查
```bash
# 内存使用
free -h

# 磁盘使用
df -h

# CPU使用
top

# Docker容器状态
docker stats
```
- [ ] 内存使用率 < 80%
- [ ] 磁盘使用率 < 80%
- [ ] CPU使用率正常
- [ ] 容器资源使用正常

### 应用功能检查
- [ ] 智能体列表加载正常
- [ ] 智能体切换功能正常
- [ ] AI对话功能正常
- [ ] 流式响应正常
- [ ] 数据可视化正常
- [ ] 所有页面导航正常

## 🛡️ 安全检查

### 网络安全
```bash
# 检查开放端口
sudo netstat -tulpn | grep LISTEN

# 检查防火墙状态
sudo ufw status
```
- [ ] 只开放必要端口（22, 80, 443）
- [ ] 防火墙规则正确
- [ ] SSH配置安全
- [ ] 应用端口不直接暴露

### 数据安全
- [ ] 智能体数据文件权限正确
- [ ] API密钥配置安全
- [ ] 日志文件权限正确
- [ ] 备份策略已配置

## 📋 运维配置

### 日志管理
```bash
# 查看应用日志
docker-compose -f docker-compose.prod.yml logs

# 查看系统日志
sudo journalctl -u docker
```
- [ ] 应用日志正常
- [ ] 系统日志正常
- [ ] 日志轮转配置
- [ ] 错误监控配置

### 备份配置
```bash
# 创建备份脚本
cat backup.sh

# 设置定时任务
crontab -l
```
- [ ] 备份脚本创建
- [ ] 定时备份配置
- [ ] 备份存储路径正确
- [ ] 备份恢复测试

## ✅ 最终验证

### 功能完整性测试
1. **主页访问**: http://YOUR_SERVER_IP
   - [ ] 页面加载正常
   - [ ] 导航菜单正常
   - [ ] 样式显示正确

2. **智能对话测试**: http://YOUR_SERVER_IP/chat
   - [ ] 智能体选择器正常
   - [ ] 发送消息功能正常
   - [ ] AI回复正常
   - [ ] 流式响应正常

3. **智能体管理**: http://YOUR_SERVER_IP/agent-library
   - [ ] 智能体列表显示
   - [ ] 智能体详情查看
   - [ ] 智能体配置功能

4. **数据可视化**: http://YOUR_SERVER_IP/dashboard
   - [ ] 图表加载正常
   - [ ] 数据显示正确
   - [ ] 交互功能正常

### 性能测试
```bash
# 并发访问测试
ab -n 100 -c 10 http://YOUR_SERVER_IP/

# 响应时间测试
curl -w "@curl-format.txt" -o /dev/null -s http://YOUR_SERVER_IP/
```
- [ ] 并发处理能力正常
- [ ] 响应时间 < 2秒
- [ ] 内存使用稳定
- [ ] 无内存泄漏

## 📞 部署完成确认

### 交付清单
- [ ] 应用部署成功
- [ ] 所有功能测试通过
- [ ] 性能指标达标
- [ ] 安全配置完成
- [ ] 监控配置完成
- [ ] 备份策略配置
- [ ] 运维文档交付
- [ ] 用户培训完成

### 联系信息
- **技术支持**: 开发团队
- **服务器管理**: 运维团队
- **应用访问**: http://YOUR_SERVER_IP
- **管理后台**: http://YOUR_SERVER_IP/agent-library

---

**部署完成时间**: ___________  
**部署负责人**: ___________  
**验收负责人**: ___________  
**签字确认**: ___________
