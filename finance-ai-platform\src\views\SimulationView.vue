<template>
  <div class="min-h-screen bg-slate-50">
    <!-- 顶部工具栏 -->
    <header class="bg-white shadow-sm border-b border-slate-200">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <router-link to="/dashboard" class="text-slate-400 hover:text-slate-600">
              <ArrowLeftOutlined class="w-5 h-5" />
            </router-link>
            <div>
              <h1 class="text-xl font-bold text-slate-900">模拟游乐场</h1>
              <p class="text-sm text-slate-500">Simulation Playground - 财务数字孪生</p>
            </div>
          </div>
          
          <div class="flex items-center space-x-3">
            <a-button @click="saveSnapshot">
              <CameraOutlined />
              保存快照
            </a-button>
            <a-button @click="loadSnapshot">
              <FolderOpenOutlined />
              加载快照
            </a-button>
            <a-button type="primary" @click="optimizeCashFlow">
              <ThunderboltOutlined />
              智能优化
            </a-button>
          </div>
        </div>
      </div>
    </header>

    <div class="flex h-screen">
      <!-- 左侧参数控制面板 -->
      <aside class="w-80 bg-white shadow-sm border-r border-slate-200">
        <div class="p-4 border-b border-slate-200">
          <h2 class="text-lg font-bold text-slate-900">参数控制台</h2>
          <p class="text-sm text-slate-500">调整参数观察影响</p>
        </div>
        
        <div class="p-4 space-y-6 overflow-y-auto">
          <!-- 市场环境参数 -->
          <div>
            <h3 class="text-md font-medium text-slate-900 mb-3">市场环境</h3>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-slate-700 mb-2">
                  利率水平: {{ interestRate.toFixed(2) }}%
                </label>
                <a-slider
                  v-model:value="interestRate"
                  :min="1"
                  :max="10"
                  :step="0.1"
                  @change="updateSimulation"
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-slate-700 mb-2">
                  钢材价格指数: {{ steelPriceIndex }}
                </label>
                <a-slider
                  v-model:value="steelPriceIndex"
                  :min="80"
                  :max="150"
                  :step="1"
                  @change="updateSimulation"
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-slate-700 mb-2">
                  汇率波动: {{ exchangeRate.toFixed(3) }}
                </label>
                <a-slider
                  v-model:value="exchangeRate"
                  :min="6.5"
                  :max="7.5"
                  :step="0.01"
                  @change="updateSimulation"
                />
              </div>
            </div>
          </div>

          <!-- 经营参数 -->
          <div>
            <h3 class="text-md font-medium text-slate-900 mb-3">经营参数</h3>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-slate-700 mb-2">
                  项目完工进度: {{ projectProgress }}%
                </label>
                <a-slider
                  v-model:value="projectProgress"
                  :min="0"
                  :max="100"
                  :step="5"
                  @change="updateSimulation"
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-slate-700 mb-2">
                  应收账款周转天数: {{ receivableDays }}天
                </label>
                <a-slider
                  v-model:value="receivableDays"
                  :min="30"
                  :max="120"
                  :step="5"
                  @change="updateSimulation"
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-slate-700 mb-2">
                  成本控制效率: {{ costEfficiency }}%
                </label>
                <a-slider
                  v-model:value="costEfficiency"
                  :min="70"
                  :max="100"
                  :step="1"
                  @change="updateSimulation"
                />
              </div>
            </div>
          </div>

          <!-- 财务策略 -->
          <div>
            <h3 class="text-md font-medium text-slate-900 mb-3">财务策略</h3>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-slate-700 mb-2">债务偿还策略</label>
                <a-select v-model:value="debtStrategy" class="w-full" @change="updateSimulation">
                  <a-select-option value="conservative">保守型</a-select-option>
                  <a-select-option value="balanced">平衡型</a-select-option>
                  <a-select-option value="aggressive">激进型</a-select-option>
                </a-select>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-slate-700 mb-2">投资倾向</label>
                <a-select v-model:value="investmentStyle" class="w-full" @change="updateSimulation">
                  <a-select-option value="risk-averse">风险规避</a-select-option>
                  <a-select-option value="moderate">稳健增长</a-select-option>
                  <a-select-option value="growth">增长导向</a-select-option>
                </a-select>
              </div>
            </div>
          </div>

          <!-- 情景分析 -->
          <div>
            <h3 class="text-md font-medium text-slate-900 mb-3">情景分析</h3>
            <div class="space-y-2">
              <a-button block @click="applyScenario('optimistic')">
                <TrendingUpOutlined />
                乐观情景
              </a-button>
              <a-button block @click="applyScenario('pessimistic')">
                <TrendingDownOutlined />
                悲观情景
              </a-button>
              <a-button block @click="applyScenario('crisis')">
                <AlertOutlined />
                危机情景
              </a-button>
              <a-button block @click="resetParameters">
                <ReloadOutlined />
                重置参数
              </a-button>
            </div>
          </div>
        </div>
      </aside>

      <!-- 主可视化区域 -->
      <main class="flex-1 p-6">
        <!-- 关键指标卡片 -->
        <div class="grid grid-cols-4 gap-4 mb-6">
          <div
            v-for="metric in keyMetrics"
            :key="metric.name"
            class="bg-white rounded-lg p-4 shadow-sm border border-slate-200"
          >
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-slate-500">{{ metric.name }}</p>
                <p class="text-2xl font-bold" :class="metric.color">{{ metric.value }}</p>
                <p class="text-sm" :class="metric.changeColor">{{ metric.change }}</p>
              </div>
              <div class="w-12 h-12 rounded-lg flex items-center justify-center" :class="metric.bgColor">
                <component :is="metric.icon" class="w-6 h-6" :class="metric.iconColor" />
              </div>
            </div>
          </div>
        </div>

        <!-- 3D Sankey图表区域 -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6 mb-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-bold text-slate-900">资金流向分析</h2>
            <div class="flex space-x-2">
              <a-button size="small" @click="toggleFlowAnimation">
                {{ flowAnimationEnabled ? '暂停动画' : '启动动画' }}
              </a-button>
              <a-button size="small" @click="changeFlowView">
                切换视角
              </a-button>
            </div>
          </div>
          
          <!-- 3D可视化容器 -->
          <div ref="sankeyContainer" class="w-full h-96 bg-slate-50 rounded-lg relative overflow-hidden">
            <!-- 模拟3D Sankey图 -->
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BarChartOutlined class="w-8 h-8 text-blue-600" />
                </div>
                <h3 class="text-lg font-medium text-slate-900 mb-2">3D资金流向图</h3>
                <p class="text-sm text-slate-500">实时展示资金在各业务单元间的流动</p>
              </div>
            </div>
            
            <!-- 流动效果模拟 -->
            <div v-if="flowAnimationEnabled" class="absolute inset-0">
              <div
                v-for="flow in activeFlows"
                :key="flow.id"
                class="absolute w-2 h-2 bg-blue-500 rounded-full animate-pulse"
                :style="{
                  left: flow.x + '%',
                  top: flow.y + '%',
                  animationDelay: flow.delay + 's'
                }"
              ></div>
            </div>
          </div>
        </div>

        <!-- 优化建议和结果 -->
        <div class="grid grid-cols-2 gap-6">
          <!-- 优化建议 -->
          <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <h3 class="text-lg font-bold text-slate-900 mb-4">AI优化建议</h3>
            <div class="space-y-3">
              <div
                v-for="suggestion in optimizationSuggestions"
                :key="suggestion.id"
                class="border-l-4 pl-4"
                :class="suggestion.priority === 'high' ? 'border-red-500' : 
                        suggestion.priority === 'medium' ? 'border-yellow-500' : 'border-green-500'"
              >
                <h4 class="font-medium text-slate-900">{{ suggestion.title }}</h4>
                <p class="text-sm text-slate-600">{{ suggestion.description }}</p>
                <p class="text-xs text-slate-500 mt-1">预期影响: {{ suggestion.impact }}</p>
              </div>
            </div>
          </div>

          <!-- 敏感性分析 -->
          <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <h3 class="text-lg font-bold text-slate-900 mb-4">敏感性分析</h3>
            <div class="space-y-4">
              <div
                v-for="sensitivity in sensitivityAnalysis"
                :key="sensitivity.factor"
                class="flex items-center justify-between"
              >
                <span class="text-sm font-medium text-slate-700">{{ sensitivity.factor }}</span>
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-slate-200 rounded-full h-2">
                    <div
                      class="h-2 rounded-full"
                      :class="sensitivity.impact > 0.7 ? 'bg-red-500' : 
                              sensitivity.impact > 0.4 ? 'bg-yellow-500' : 'bg-green-500'"
                      :style="{ width: (sensitivity.impact * 100) + '%' }"
                    ></div>
                  </div>
                  <span class="text-sm text-slate-600">{{ (sensitivity.impact * 100).toFixed(0) }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <!-- 右侧快照管理 -->
      <aside class="w-64 bg-white shadow-sm border-l border-slate-200">
        <div class="p-4 border-b border-slate-200">
          <h2 class="text-lg font-bold text-slate-900">快照管理</h2>
          <p class="text-sm text-slate-500">保存和分享场景</p>
        </div>
        
        <div class="p-4">
          <div class="space-y-3">
            <div
              v-for="snapshot in snapshots"
              :key="snapshot.id"
              class="border border-slate-200 rounded-lg p-3 cursor-pointer hover:border-blue-300 transition-colors"
              @click="loadSnapshotData(snapshot)"
            >
              <h4 class="font-medium text-slate-900 text-sm">{{ snapshot.name }}</h4>
              <p class="text-xs text-slate-500 mt-1">{{ formatTime(snapshot.timestamp) }}</p>
              <div class="flex items-center justify-between mt-2">
                <span class="text-xs" :class="snapshot.performance > 0 ? 'text-green-600' : 'text-red-600'">
                  {{ snapshot.performance > 0 ? '+' : '' }}{{ snapshot.performance.toFixed(1) }}%
                </span>
                <a-button size="small" @click.stop="deleteSnapshot(snapshot.id)">
                  <DeleteOutlined />
                </a-button>
              </div>
            </div>
          </div>
          
          <a-button block class="mt-4" @click="createSnapshot">
            <PlusOutlined />
            创建快照
          </a-button>
        </div>
      </aside>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  CameraOutlined,
  FolderOpenOutlined,
  ThunderboltOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined,
  AlertOutlined,
  ReloadOutlined,
  BarChartOutlined,
  DollarOutlined,
  PieChartOutlined,
  LineChartOutlined,
  DeleteOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const sankeyContainer = ref<HTMLDivElement>()
const interestRate = ref(4.5)
const steelPriceIndex = ref(100)
const exchangeRate = ref(7.2)
const projectProgress = ref(75)
const receivableDays = ref(60)
const costEfficiency = ref(85)
const debtStrategy = ref('balanced')
const investmentStyle = ref('moderate')
const flowAnimationEnabled = ref(true)
const snapshots = ref<any[]>([])

// 计算属性
const keyMetrics = computed(() => [
  {
    name: '净现值',
    value: '¥' + (12.5 + (projectProgress.value - 75) * 0.1 + (100 - steelPriceIndex.value) * 0.05).toFixed(1) + '亿',
    change: '+8.3%',
    color: 'text-slate-900',
    changeColor: 'text-green-600',
    icon: DollarOutlined,
    bgColor: 'bg-green-100',
    iconColor: 'text-green-600'
  },
  {
    name: '流动比率',
    value: (1.35 + (costEfficiency.value - 85) * 0.01).toFixed(2),
    change: receivableDays.value > 70 ? '-0.05' : '+0.02',
    color: 'text-slate-900',
    changeColor: receivableDays.value > 70 ? 'text-red-600' : 'text-green-600',
    icon: BarChartOutlined,
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-600'
  },
  {
    name: 'ROE',
    value: (15.8 + (projectProgress.value - 75) * 0.05).toFixed(1) + '%',
    change: '+1.2%',
    color: 'text-slate-900',
    changeColor: 'text-green-600',
    icon: PieChartOutlined,
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600'
  },
  {
    name: '风险评分',
    value: Math.max(1, Math.min(10, 7 - (interestRate.value - 4.5) * 0.5 - (receivableDays.value - 60) * 0.02)).toFixed(1),
    change: '稳定',
    color: 'text-slate-900',
    changeColor: 'text-green-600',
    icon: LineChartOutlined,
    bgColor: 'bg-emerald-100',
    iconColor: 'text-emerald-600'
  }
])

const optimizationSuggestions = computed(() => [
  {
    id: 1,
    title: '优化应收账款管理',
    description: `当前周转天数${receivableDays.value}天，建议加强催收`,
    impact: '提升现金流15%',
    priority: receivableDays.value > 80 ? 'high' : 'medium'
  },
  {
    id: 2,
    title: '调整债务结构',
    description: `在当前利率${interestRate.value}%下，建议${interestRate.value > 6 ? '减少' : '适度增加'}负债`,
    impact: '降低财务成本8%',
    priority: 'medium'
  },
  {
    id: 3,
    title: '成本控制优化',
    description: `当前效率${costEfficiency.value}%，仍有提升空间`,
    impact: '提升利润率3%',
    priority: costEfficiency.value < 90 ? 'high' : 'low'
  }
])

const sensitivityAnalysis = computed(() => [
  { factor: '利率变动', impact: Math.abs(interestRate.value - 4.5) / 5.5 },
  { factor: '钢材价格', impact: Math.abs(steelPriceIndex.value - 100) / 50 },
  { factor: '汇率波动', impact: Math.abs(exchangeRate.value - 7.0) / 1.0 },
  { factor: '项目进度', impact: Math.abs(projectProgress.value - 75) / 25 },
  { factor: '应收账款', impact: Math.abs(receivableDays.value - 60) / 60 }
])

const activeFlows = computed(() => {
  if (!flowAnimationEnabled.value) return []
  
  return Array.from({ length: 8 }, (_, i) => ({
    id: i,
    x: 10 + i * 10,
    y: 20 + Math.sin(i) * 30,
    delay: i * 0.5
  }))
})

// 方法
const updateSimulation = () => {
  // 触发重新计算
  message.info('参数已更新，正在重新计算...', 1)
}

const applyScenario = (scenario: string) => {
  switch (scenario) {
    case 'optimistic':
      interestRate.value = 3.5
      steelPriceIndex.value = 90
      projectProgress.value = 90
      receivableDays.value = 45
      costEfficiency.value = 95
      break
    case 'pessimistic':
      interestRate.value = 7.0
      steelPriceIndex.value = 130
      projectProgress.value = 60
      receivableDays.value = 90
      costEfficiency.value = 75
      break
    case 'crisis':
      interestRate.value = 9.0
      steelPriceIndex.value = 150
      projectProgress.value = 50
      receivableDays.value = 120
      costEfficiency.value = 70
      break
  }
  updateSimulation()
  message.success(`已应用${scenario === 'optimistic' ? '乐观' : scenario === 'pessimistic' ? '悲观' : '危机'}情景`)
}

const resetParameters = () => {
  interestRate.value = 4.5
  steelPriceIndex.value = 100
  exchangeRate.value = 7.2
  projectProgress.value = 75
  receivableDays.value = 60
  costEfficiency.value = 85
  debtStrategy.value = 'balanced'
  investmentStyle.value = 'moderate'
  updateSimulation()
  message.success('参数已重置')
}

const optimizeCashFlow = () => {
  message.loading('AI正在分析最优参数组合...', 2)
  
  setTimeout(() => {
    // 模拟AI优化结果
    receivableDays.value = Math.max(30, receivableDays.value - 10)
    costEfficiency.value = Math.min(100, costEfficiency.value + 5)
    
    message.success('优化完成！现金流预计提升12%')
    updateSimulation()
  }, 2000)
}

const toggleFlowAnimation = () => {
  flowAnimationEnabled.value = !flowAnimationEnabled.value
  message.info(flowAnimationEnabled.value ? '动画已启动' : '动画已暂停')
}

const changeFlowView = () => {
  message.info('视角已切换')
}

const saveSnapshot = () => {
  const snapshot = {
    id: Date.now(),
    name: `场景_${new Date().toLocaleString('zh-CN')}`,
    timestamp: new Date(),
    parameters: {
      interestRate: interestRate.value,
      steelPriceIndex: steelPriceIndex.value,
      exchangeRate: exchangeRate.value,
      projectProgress: projectProgress.value,
      receivableDays: receivableDays.value,
      costEfficiency: costEfficiency.value,
      debtStrategy: debtStrategy.value,
      investmentStyle: investmentStyle.value
    },
    performance: Math.random() * 20 - 10 // 模拟性能变化
  }
  
  snapshots.value.unshift(snapshot)
  message.success('快照已保存')
}

const loadSnapshot = () => {
  if (snapshots.value.length === 0) {
    message.warning('没有可用的快照')
    return
  }
  
  message.info('请从右侧选择要加载的快照')
}

const loadSnapshotData = (snapshot: any) => {
  const params = snapshot.parameters
  interestRate.value = params.interestRate
  steelPriceIndex.value = params.steelPriceIndex
  exchangeRate.value = params.exchangeRate
  projectProgress.value = params.projectProgress
  receivableDays.value = params.receivableDays
  costEfficiency.value = params.costEfficiency
  debtStrategy.value = params.debtStrategy
  investmentStyle.value = params.investmentStyle
  
  updateSimulation()
  message.success(`已加载快照: ${snapshot.name}`)
}

const createSnapshot = () => {
  saveSnapshot()
}

const deleteSnapshot = (id: number) => {
  snapshots.value = snapshots.value.filter(s => s.id !== id)
  message.success('快照已删除')
}

const formatTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  // 初始化一些示例快照
  snapshots.value = [
    {
      id: 1,
      name: '基准场景',
      timestamp: new Date(Date.now() - 86400000),
      parameters: {
        interestRate: 4.5,
        steelPriceIndex: 100,
        exchangeRate: 7.2,
        projectProgress: 75,
        receivableDays: 60,
        costEfficiency: 85,
        debtStrategy: 'balanced',
        investmentStyle: 'moderate'
      },
      performance: 0
    },
    {
      id: 2,
      name: '优化场景',
      timestamp: new Date(Date.now() - 43200000),
      parameters: {
        interestRate: 3.8,
        steelPriceIndex: 95,
        exchangeRate: 7.0,
        projectProgress: 85,
        receivableDays: 50,
        costEfficiency: 92,
        debtStrategy: 'balanced',
        investmentStyle: 'growth'
      },
      performance: 12.5
    }
  ]
})
</script>

<style scoped>
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style>
