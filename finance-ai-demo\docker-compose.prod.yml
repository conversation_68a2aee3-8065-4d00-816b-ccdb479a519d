version: '3.8'

services:
  finance-ai-demo:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: finance-ai-demo-prod
    ports:
      - "5666:3000"    # 生产环境使用5666端口
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
      # Dify API配置
      - DIFY_API_URL=https://ai.hongtai-idi.com/v1
      - DIFY_API_KEY=app-xuReGCaYZmkWMn0JGAAxqBvg
    volumes:
      # 数据持久化
      - ./data:/app/data
      # 日志持久化
      - ./logs:/app/logs
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - finance-ai-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: finance-ai-nginx
    ports:
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - finance-ai-demo
    restart: always
    networks:
      - finance-ai-network

networks:
  finance-ai-network:
    driver: bridge

volumes:
  finance_data:
    driver: local
