'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Input, 
  Button, 
  Card, 
  Avatar, 
  Badge, 
  Progress,
  Select,
  Checkbox
} from 'antd'
import {
  SendOutlined,
  RobotOutlined,
  UserOutlined,
  BulbOutlined,
  LoadingOutlined
} from '@ant-design/icons'
import DashboardLayout from '@/components/DashboardLayout'
import { difyApi } from '@/services/difyApi'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'
import 'highlight.js/styles/github.css'
import '@/styles/markdown.css'

const { TextArea } = Input
const { Option } = Select

// 处理thinking内容的函数
const processThinkingContent = (content: string) => {
  // 检查是否包含完整的thinking标签
  if (content.includes('<details') && content.includes('Thinking...') && content.includes('</details>')) {
    // 分离thinking部分和正常内容
    const thinkingMatch = content.match(/<details[^>]*>([\s\S]*?)<\/details>([\s\S]*)/);
    if (thinkingMatch) {
      const thinkingContent = thinkingMatch[1].replace(/<summary[^>]*>.*?<\/summary>/g, '').trim();
      const normalContent = thinkingMatch[2].trim();

      return {
        hasThinking: true,
        isThinkingComplete: true,
        thinkingContent,
        normalContent
      };
    }
  }

  // 检查是否正在输出thinking（开始了但还没结束）
  if (content.includes('<details') && content.includes('Thinking...') && !content.includes('</details>')) {
    const thinkingStartMatch = content.match(/<details[^>]*>[\s\S]*?<summary[^>]*>.*?Thinking.*?<\/summary>([\s\S]*)/);
    if (thinkingStartMatch) {
      return {
        hasThinking: true,
        isThinkingComplete: false,
        thinkingContent: thinkingStartMatch[1],
        normalContent: ''
      };
    }
  }

  return {
    hasThinking: false,
    isThinkingComplete: false,
    thinkingContent: '',
    normalContent: content
  };
};

export default function CreateInsightPage() {
  const [messages, setMessages] = useState<any[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isStreaming, setIsStreaming] = useState(false)
  const [currentThinkingMessageId, setCurrentThinkingMessageId] = useState<number | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  // 配置状态
  const [selectedInsightType, setSelectedInsightType] = useState('financial-trend')
  const [selectedDataSources, setSelectedDataSources] = useState(['financial-reports', 'bank-statements'])
  const [timeRange, setTimeRange] = useState('6-months')
  const [analysisDepth, setAnalysisDepth] = useState('standard')
  const [outputFormats, setOutputFormats] = useState(['charts', 'summary'])

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage = {
      id: Date.now(),
      type: 'user' as const,
      content: inputValue,
      timestamp: new Date().toLocaleTimeString()
    }

    const currentInput = inputValue
    setInputValue('')
    setIsLoading(true)
    setMessages(prev => [...prev, userMessage])

    try {
      // 构建包含配置信息的完整提示
      const configInfo = `
当前洞察配置：
- 洞察类型：${getInsightTypeName(selectedInsightType)}
- 数据源：${selectedDataSources.map(ds => getDataSourceName(ds)).join('、')}
- 时间范围：${getTimeRangeName(timeRange)}
- 分析深度：${getAnalysisDepthName(analysisDepth)}
- 输出格式：${outputFormats.map(of => getOutputFormatName(of)).join('、')}

用户问题：${currentInput}

请基于以上配置信息，为用户提供专业的财务洞察分析建议。`

      const initialAssistantMessage = {
        id: Date.now() + 1,
        type: 'assistant' as const,
        content: '',
        timestamp: new Date().toLocaleTimeString(),
        isStreaming: true
      }

      setMessages(prev => [...prev, initialAssistantMessage])

      // 尝试使用流式API
      try {
        setIsStreaming(true)
        await difyApi.sendMessageStream(
          configInfo,
          'demo_user',
          // 处理流式数据块
          (chunk) => {
            console.log('📨 收到流式数据块:', chunk)
            if (chunk.event === 'message' && chunk.answer) {
              setMessages(prev => prev.map(msg => {
                if (msg.id === initialAssistantMessage.id) {
                  const newContent = msg.content + chunk.answer;

                  // 检查是否开始thinking
                  if (newContent.includes('<details') && newContent.includes('Thinking...') && !currentThinkingMessageId) {
                    setCurrentThinkingMessageId(initialAssistantMessage.id);
                  }

                  // 检查thinking是否结束
                  if (currentThinkingMessageId === initialAssistantMessage.id && newContent.includes('</details>')) {
                    setCurrentThinkingMessageId(null);
                  }

                  return { ...msg, content: newContent };
                }
                return msg;
              }))
            }
          },
          // 处理完成
          (finalData) => {
            setIsStreaming(false)
            setIsLoading(false)
            setCurrentThinkingMessageId(null)
            setMessages(prev => prev.map(msg => 
              msg.id === initialAssistantMessage.id 
                ? { ...msg, isStreaming: false }
                : msg
            ))
          }
        )
      } catch (streamError) {
        console.warn('流式API失败，尝试阻塞模式:', streamError)
        setIsStreaming(false)
        setCurrentThinkingMessageId(null)

        // 回退到阻塞模式
        try {
          const response = await difyApi.sendMessage(configInfo)
          
          // 模拟流式效果显示阻塞响应
          setIsStreaming(true)
          const text = response.answer
          let currentIndex = 0

          const typeWriter = () => {
            if (currentIndex < text.length) {
              setMessages(prev => prev.map(msg => 
                msg.id === initialAssistantMessage.id 
                  ? { ...msg, content: text.substring(0, currentIndex + 1) }
                  : msg
              ))
              currentIndex++
              setTimeout(typeWriter, 30)
            } else {
              setIsStreaming(false)
              setIsLoading(false)
              setCurrentThinkingMessageId(null)
              setMessages(prev => prev.map(msg => 
                msg.id === initialAssistantMessage.id 
                  ? { ...msg, isStreaming: false }
                  : msg
              ))
            }
          }

          typeWriter()
        } catch (blockingError) {
          console.error('阻塞模式也失败:', blockingError)
          setIsStreaming(false)
          setCurrentThinkingMessageId(null)
          throw blockingError
        }
      }
    } catch (error) {
      console.error('发送消息失败:', error)
      setIsStreaming(false)
      setCurrentThinkingMessageId(null)

      const errorMessage = {
        id: Date.now() + 1,
        type: 'assistant' as const,
        content: '抱歉，我遇到了一些技术问题。请稍后再试，或者检查网络连接。',
        timestamp: new Date().toLocaleTimeString(),
        isError: true
      }

      setMessages(prev => [...prev.slice(0, -1), errorMessage])
      setIsLoading(false)
    }
  }

  // 辅助函数
  const getInsightTypeName = (type: string) => {
    const types = {
      'financial-trend': '财务趋势洞察',
      'risk-warning': '风险预警洞察',
      'investment-opportunity': '投资机会洞察',
      'cost-optimization': '成本优化洞察'
    }
    return types[type as keyof typeof types] || type
  }

  const getDataSourceName = (source: string) => {
    const sources = {
      'financial-reports': '财务报表数据',
      'bank-statements': '银行流水数据',
      'business-systems': '业务系统数据',
      'market-data': '外部市场数据'
    }
    return sources[source as keyof typeof sources] || source
  }

  const getTimeRangeName = (range: string) => {
    const ranges = {
      '3-months': '最近3个月',
      '6-months': '最近6个月',
      '1-year': '最近1年',
      '2-years': '最近2年'
    }
    return ranges[range as keyof typeof ranges] || range
  }

  const getAnalysisDepthName = (depth: string) => {
    const depths = {
      'quick': '快速分析 (5分钟)',
      'standard': '标准分析 (15分钟)',
      'deep': '深度分析 (30分钟)'
    }
    return depths[depth as keyof typeof depths] || depth
  }

  const getOutputFormatName = (format: string) => {
    const formats = {
      'charts': '图表报告',
      'summary': '文字总结',
      'ppt': 'PPT演示',
      'all': '全部格式'
    }
    return formats[format as keyof typeof formats] || format
  }

  return (
    <DashboardLayout>
      <div className="h-screen flex bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        {/* 左侧配置面板 */}
        <div className="w-1/3 bg-white shadow-lg border-r border-gray-200 overflow-y-auto">
          <div className="p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <BulbOutlined className="text-white text-lg" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-800">创建洞察</h1>
                <p className="text-sm text-gray-600">AI驱动的智能财务洞察生成</p>
              </div>
            </div>

            {/* 洞察类型选择 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-700 mb-3">洞察类型</h3>
              <Select
                value={selectedInsightType}
                onChange={setSelectedInsightType}
                className="w-full"
                size="large"
              >
                <Option value="financial-trend">📈 财务趋势洞察</Option>
                <Option value="risk-warning">⚠️ 风险预警洞察</Option>
                <Option value="investment-opportunity">💰 投资机会洞察</Option>
                <Option value="cost-optimization">⚡ 成本优化洞察</Option>
              </Select>
            </div>

            {/* 数据源配置 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-700 mb-3">数据源</h3>
              <Checkbox.Group
                value={selectedDataSources}
                onChange={setSelectedDataSources}
                className="w-full"
              >
                <div className="space-y-2">
                  <div><Checkbox value="financial-reports">财务报表数据</Checkbox></div>
                  <div><Checkbox value="bank-statements">银行流水数据</Checkbox></div>
                  <div><Checkbox value="business-systems">业务系统数据</Checkbox></div>
                  <div><Checkbox value="market-data">外部市场数据</Checkbox></div>
                </div>
              </Checkbox.Group>
            </div>

            {/* 时间范围 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-700 mb-3">时间范围</h3>
              <Select
                value={timeRange}
                onChange={setTimeRange}
                className="w-full"
                size="large"
              >
                <Option value="3-months">最近3个月</Option>
                <Option value="6-months">最近6个月</Option>
                <Option value="1-year">最近1年</Option>
                <Option value="2-years">最近2年</Option>
              </Select>
            </div>

            {/* 分析深度 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-700 mb-3">分析深度</h3>
              <Select
                value={analysisDepth}
                onChange={setAnalysisDepth}
                className="w-full"
                size="large"
              >
                <Option value="quick">快速分析 (5分钟)</Option>
                <Option value="standard">标准分析 (15分钟)</Option>
                <Option value="deep">深度分析 (30分钟)</Option>
              </Select>
            </div>

            {/* 输出格式 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-700 mb-3">输出格式</h3>
              <Checkbox.Group
                value={outputFormats}
                onChange={setOutputFormats}
                className="w-full"
              >
                <div className="space-y-2">
                  <div><Checkbox value="charts">图表报告</Checkbox></div>
                  <div><Checkbox value="summary">文字总结</Checkbox></div>
                  <div><Checkbox value="ppt">PPT演示</Checkbox></div>
                  <div><Checkbox value="all">全部格式</Checkbox></div>
                </div>
              </Checkbox.Group>
            </div>
          </div>
        </div>

        {/* 右侧聊天面板 */}
        <div className="flex-1 flex flex-col">
          {/* 聊天头部 */}
          <div className="bg-white border-b border-gray-200 p-4">
            <div className="flex items-center space-x-3">
              <Avatar 
                icon={<RobotOutlined />} 
                className="bg-gradient-to-r from-blue-500 to-purple-600"
              />
              <div>
                <h2 className="font-semibold text-gray-800">洞察生成助手</h2>
                <p className="text-sm text-gray-600">基于您的配置生成专业财务洞察</p>
              </div>
              <Badge status="processing" text="在线" className="ml-auto" />
            </div>
          </div>

          {/* 聊天消息区域 */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            <AnimatePresence>
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                    <div className={`flex items-start space-x-2 ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                      <Avatar 
                        icon={message.type === 'user' ? <UserOutlined /> : <RobotOutlined />}
                        className={message.type === 'user' ? 'bg-blue-500' : 'bg-gradient-to-r from-blue-500 to-purple-600'}
                      />
                      <div className={`rounded-2xl px-4 py-3 ${
                        message.type === 'user' 
                          ? 'bg-blue-500 text-white' 
                          : 'bg-white border border-gray-200 text-gray-800'
                      }`}>
                        {message.type === 'assistant' && (
                          <div className="markdown-content">
                            {(() => {
                              const processed = processThinkingContent(message.content);
                              const isCurrentlyThinking = currentThinkingMessageId === message.id;

                              // 如果正在thinking但还没有完整的details标签，特殊处理
                              if (isCurrentlyThinking && !processed.isThinkingComplete) {
                                // 提取thinking内容（去掉HTML标签）
                                const thinkingContent = message.content
                                  .replace(/<details[^>]*>/g, '')
                                  .replace(/<summary[^>]*>.*?<\/summary>/g, '')
                                  .trim();

                                return (
                                  <details
                                    className="thinking-section"
                                    style={{
                                      color: '#6b7280',
                                      backgroundColor: '#f8f9fa',
                                      padding: '12px',
                                      borderRadius: '8px',
                                      margin: '8px 0',
                                      border: '1px solid #e5e7eb'
                                    }}
                                    open
                                  >
                                    <summary
                                      style={{
                                        fontWeight: '600',
                                        color: '#4b5563',
                                        cursor: 'pointer',
                                        marginBottom: '8px'
                                      }}
                                    >
                                      🤔 AI思考过程
                                    </summary>
                                    <div
                                      style={{
                                        color: '#6b7280',
                                        fontSize: '14px',
                                        lineHeight: '1.6',
                                        whiteSpace: 'pre-wrap'
                                      }}
                                    >
                                      {thinkingContent}
                                    </div>
                                  </details>
                                );
                              }

                              return (
                                <>
                                  {/* 渲染thinking部分 */}
                                  {processed.hasThinking && processed.isThinkingComplete && (
                                    <details
                                      className="thinking-section"
                                      style={{
                                        color: '#6b7280',
                                        backgroundColor: '#f8f9fa',
                                        padding: '12px',
                                        borderRadius: '8px',
                                        margin: '8px 0',
                                        border: '1px solid #e5e7eb'
                                      }}
                                      open
                                    >
                                      <summary
                                        style={{
                                          fontWeight: '600',
                                          color: '#4b5563',
                                          cursor: 'pointer',
                                          marginBottom: '8px'
                                        }}
                                      >
                                        🤔 AI思考过程
                                      </summary>
                                      <div
                                        style={{
                                          color: '#6b7280',
                                          fontSize: '14px',
                                          lineHeight: '1.6',
                                          whiteSpace: 'pre-wrap'
                                        }}
                                      >
                                        {processed.thinkingContent}
                                      </div>
                                    </details>
                                  )}

                                  {/* 渲染正常内容 */}
                                  {(processed.normalContent || (!processed.hasThinking && message.content)) && (
                                    <ReactMarkdown
                                      remarkPlugins={[remarkGfm]}
                                      rehypePlugins={[rehypeHighlight]}
                                      components={{
                                        h1: ({children}) => <h1 className="text-xl font-bold mb-3 text-gray-900">{children}</h1>,
                                        h2: ({children}) => <h2 className="text-lg font-bold mb-2 text-gray-900">{children}</h2>,
                                        h3: ({children}) => <h3 className="text-base font-bold mb-2 text-gray-900">{children}</h3>,
                                        p: ({children}) => <p className="mb-2 text-gray-900">{children}</p>,
                                        ul: ({children}) => <ul className="list-disc list-inside mb-2 text-gray-900">{children}</ul>,
                                        ol: ({children}) => <ol className="list-decimal list-inside mb-2 text-gray-900">{children}</ol>,
                                        li: ({children}) => <li className="mb-1 text-gray-900">{children}</li>,
                                        strong: ({children}) => <strong className="font-bold text-gray-900">{children}</strong>,
                                        em: ({children}) => <em className="italic text-gray-900">{children}</em>,
                                        code: ({children}) => <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono text-gray-900">{children}</code>,
                                        pre: ({children}) => <pre className="bg-gray-100 p-3 rounded-lg overflow-x-auto mb-2">{children}</pre>,
                                        blockquote: ({children}) => <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-700 mb-2">{children}</blockquote>,
                                        table: ({children}) => <table className="border-collapse border border-gray-300 mb-2">{children}</table>,
                                        th: ({children}) => <th className="border border-gray-300 px-2 py-1 bg-gray-100 font-bold text-gray-900">{children}</th>,
                                        td: ({children}) => <td className="border border-gray-300 px-2 py-1 text-gray-900">{children}</td>,
                                      }}
                                    >
                                      {processed.normalContent || (!processed.hasThinking ? message.content : '')}
                                    </ReactMarkdown>
                                  )}
                                </>
                              );
                            })()}
                          </div>
                        )}
                        
                        {message.type === 'user' && (
                          <div className="text-white">{message.content}</div>
                        )}
                      </div>
                    </div>
                    <div className={`text-xs text-gray-500 mt-1 ${message.type === 'user' ? 'text-right' : 'text-left'}`}>
                      {message.timestamp}
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
            
            {/* 加载指示器 */}
            {isLoading && !isStreaming && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex justify-start"
              >
                <div className="flex items-center space-x-2 bg-white border border-gray-200 rounded-2xl px-4 py-3">
                  <LoadingOutlined className="text-blue-500" />
                  <span className="text-gray-600">AI正在思考...</span>
                </div>
              </motion.div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* 输入区域 */}
          <div className="bg-white border-t border-gray-200 p-4">
            <div className="flex space-x-3">
              <TextArea
                ref={inputRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="请描述您需要的财务洞察，我会基于您的配置生成专业分析..."
                autoSize={{ minRows: 1, maxRows: 4 }}
                onPressEnter={(e) => {
                  if (!e.shiftKey) {
                    e.preventDefault()
                    handleSendMessage()
                  }
                }}
                className="resize-none text-gray-900"
                styles={{
                  textarea: {
                    color: '#111827 !important',
                    fontSize: '16px',
                    backgroundColor: 'transparent'
                  }
                }}
              />
              <Button 
                type="primary"
                icon={<SendOutlined />}
                onClick={handleSendMessage}
                loading={isLoading}
                disabled={!inputValue.trim()}
                className="bg-blue-500 hover:bg-blue-600"
              >
                发送
              </Button>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
