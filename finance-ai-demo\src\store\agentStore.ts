'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// API调用函数
const saveAgentsToServer = async (agents: Agent[]): Promise<void> => {
  try {
    const response = await fetch('/api/agents', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ agents })
    })

    if (!response.ok) {
      throw new Error(`保存失败: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ 智能体数据已保存到服务器:', result.count, '个')
  } catch (error) {
    console.error('❌ 保存智能体数据到服务器失败:', error)
    throw error
  }
}

const loadAgentsFromServer = async (): Promise<Agent[]> => {
  try {
    const response = await fetch('/api/agents', {
      method: 'GET',
    })

    if (!response.ok) {
      throw new Error(`加载失败: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ 从服务器加载了', result.count, '个智能体')
    return result.data || []
  } catch (error) {
    console.error('❌ 从服务器加载智能体数据失败:', error)
    return []
  }
}

// 智能体数据结构定义
export interface Agent {
  id: string
  name: string
  description: string // 功能描述
  department: string
  dataScope: string
  permissionLimit: string
  apiKey: string
  enabled: boolean
  createTime: string
  updateTime: string
}

// 智能体状态管理接口
interface AgentState {
  agents: Agent[]
  currentAgent: Agent | null

  // 操作方法
  setAgents: (agents: Agent[]) => void
  addAgent: (agent: Omit<Agent, 'id' | 'createTime' | 'updateTime'>) => Promise<void>
  updateAgent: (id: string, updates: Partial<Agent>) => Promise<void>
  deleteAgent: (id: string) => Promise<void>
  toggleAgent: (id: string) => Promise<void>
  setCurrentAgent: (agent: Agent) => void
  getCurrentAgent: () => Agent | null
  getEnabledAgents: () => Agent[]
  initializeAgents: () => Promise<void>
  syncToServer: () => Promise<void>
}

// 默认智能体数据
const defaultAgents: Agent[] = [
  {
    id: '1',
    name: '财务智能助手',
    description: '专业的财务数据分析和报告生成，支持多维度财务指标解读',
    department: '财务部',
    dataScope: '全部财务数据',
    permissionLimit: '无限制',
    apiKey: 'app-xuReGCaYZmkWMn0JGAAxqBvg',
    enabled: true,
    createTime: '2025-01-01 10:00:00',
    updateTime: '2025-01-01 10:00:00'
  },
  {
    id: '2',
    name: '风险评估专家',
    description: '智能识别财务风险点，提供风险预警和防控建议',
    department: '风控部',
    dataScope: '风险相关数据',
    permissionLimit: '仅查看',
    apiKey: 'app-risk-assessment-key',
    enabled: true,
    createTime: '2025-01-01 10:00:00',
    updateTime: '2025-01-01 10:00:00'
  },
  {
    id: '3',
    name: '合规检查助手',
    description: '自动化合规审核，确保财务操作符合法规要求',
    department: '合规部',
    dataScope: '合规审计数据',
    permissionLimit: '审计权限',
    apiKey: 'app-compliance-check-key',
    enabled: false,
    createTime: '2025-01-01 10:00:00',
    updateTime: '2025-01-01 10:00:00'
  },
  {
    id: '4',
    name: '投资分析师',
    description: '深度投资机会分析，提供数据驱动的投资决策支持',
    department: '投资部',
    dataScope: '投资决策数据',
    permissionLimit: '分析权限',
    apiKey: 'app-investment-analysis-key',
    enabled: true,
    createTime: '2025-01-01 11:00:00',
    updateTime: '2025-01-01 11:00:00'
  },
  {
    id: '5',
    name: '审计专员',
    description: '全面的财务审计支持，发现异常并提供整改建议',
    department: '审计部',
    dataScope: '全部财务数据',
    permissionLimit: '审计权限',
    apiKey: 'app-audit-specialist-key',
    enabled: true,
    createTime: '2025-01-01 12:00:00',
    updateTime: '2025-01-01 12:00:00'
  }
]

// 创建智能体状态管理store
export const useAgentStore = create<AgentState>()(
  persist(
    (set, get) => ({
      agents: [],
      currentAgent: null,

      // 设置智能体列表
      setAgents: (agents) => set({ agents }),

      // 添加新智能体
      addAgent: async (agentData) => {
        const now = new Date().toLocaleString()
        const newAgent: Agent = {
          ...agentData,
          id: Date.now().toString(),
          enabled: true,
          createTime: now,
          updateTime: now
        }

        const newAgents = [...get().agents, newAgent]
        set({ agents: newAgents })

        // 保存到服务器
        await saveAgentsToServer(newAgents)
      },

      // 更新智能体
      updateAgent: async (id, updates) => {
        const now = new Date().toLocaleString()
        const newAgents = get().agents.map(agent =>
          agent.id === id
            ? { ...agent, ...updates, updateTime: now }
            : agent
        )

        set({ agents: newAgents })

        // 保存到服务器
        await saveAgentsToServer(newAgents)
      },

      // 删除智能体
      deleteAgent: async (id) => {
        const state = get()
        const newAgents = state.agents.filter(agent => agent.id !== id)

        // 如果删除的是当前智能体，切换到第一个启用的智能体
        let newCurrentAgent = state.currentAgent
        if (state.currentAgent?.id === id) {
          const remainingAgents = newAgents.filter(a => a.enabled)
          newCurrentAgent = remainingAgents.length > 0 ? remainingAgents[0] : null
        }

        set({
          agents: newAgents,
          currentAgent: newCurrentAgent
        })

        // 保存到服务器
        await saveAgentsToServer(newAgents)
      },

      // 切换智能体启用状态
      toggleAgent: async (id) => {
        const state = get()
        const agent = state.agents.find(a => a.id === id)
        if (!agent) return

        const now = new Date().toLocaleString()
        const updatedAgents = state.agents.map(a =>
          a.id === id
            ? { ...a, enabled: !a.enabled, updateTime: now }
            : a
        )

        // 如果停用的是当前智能体，切换到第一个启用的智能体
        let newCurrentAgent = state.currentAgent
        if (state.currentAgent?.id === id && agent.enabled) {
          const enabledAgents = updatedAgents.filter(a => a.enabled)
          newCurrentAgent = enabledAgents.length > 0 ? enabledAgents[0] : null
        }

        set({
          agents: updatedAgents,
          currentAgent: newCurrentAgent
        })

        // 保存到服务器
        await saveAgentsToServer(updatedAgents)
      },

      // 设置当前智能体
      setCurrentAgent: (agent) => set({ currentAgent: agent }),

      // 获取当前智能体
      getCurrentAgent: () => get().currentAgent,

      // 获取启用的智能体列表
      getEnabledAgents: () => get().agents.filter(agent => agent.enabled),

      // 初始化智能体数据
      initializeAgents: async () => {
        const state = get()

        // 如果本地没有数据，从服务器加载
        if (state.agents.length === 0) {
          try {
            const serverAgents = await loadAgentsFromServer()
            if (serverAgents.length > 0) {
              set({
                agents: serverAgents,
                currentAgent: serverAgents.find(agent => agent.enabled) || null
              })
              console.log('✅ 从服务器初始化了', serverAgents.length, '个智能体')
            } else {
              // 如果服务器也没有数据，使用默认数据
              set({
                agents: defaultAgents,
                currentAgent: defaultAgents.find(agent => agent.enabled) || null
              })
              await saveAgentsToServer(defaultAgents)
              console.log('✅ 使用默认数据初始化智能体')
            }
          } catch (error) {
            console.error('❌ 从服务器加载智能体失败，使用默认数据:', error)
            set({
              agents: defaultAgents,
              currentAgent: defaultAgents.find(agent => agent.enabled) || null
            })
          }
        } else if (!state.currentAgent) {
          const enabledAgent = state.agents.find(agent => agent.enabled)
          if (enabledAgent) {
            set({ currentAgent: enabledAgent })
          }
        }
      },

      // 手动同步到服务器
      syncToServer: async () => {
        const state = get()
        await saveAgentsToServer(state.agents)
      }
    }),
    {
      name: 'agent-storage', // localStorage key
      partialize: (state) => ({
        agents: state.agents,
        currentAgent: state.currentAgent
      })
    }
  )
)
