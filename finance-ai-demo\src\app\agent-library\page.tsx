'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Button, 
  Card, 
  Avatar, 
  Tag, 
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Space,
  Popconfirm,
  message,
  Row,
  Col,
  Statistic,
  Divider
} from 'antd'
import {
  RobotOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SettingOutlined,
  ArrowLeftOutlined,
  UserOutlined,
  KeyOutlined,
  SafetyOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  StopOutlined,
  SyncOutlined
} from '@ant-design/icons'
import DashboardLayout from '@/components/DashboardLayout'
import { useRouter } from 'next/navigation'
import { useAgentStore, type Agent } from '@/store/agentStore'

const { Option } = Select

// 输入框文字颜色样式
const inputTextStyles = `
  .text-black .ant-input {
    color: #000000 !important;
  }

  .text-black .ant-input::placeholder {
    color: #666666 !important;
  }

  .text-black .ant-select-selection-item {
    color: #000000 !important;
  }

  .text-black .ant-select-selection-placeholder {
    color: #666666 !important;
  }

  .text-black .ant-input-password input {
    color: #000000 !important;
  }

  .text-black .ant-select-selector {
    color: #000000 !important;
  }

  .text-black .ant-select-selection-search-input {
    color: #000000 !important;
  }

  .agent-form-modal .ant-input {
    color: #000000 !important;
  }

  .agent-form-modal .ant-input::placeholder {
    color: #666666 !important;
  }

  .agent-form-modal .ant-select-selection-item {
    color: #000000 !important;
  }

  .agent-form-modal .ant-select-selection-placeholder {
    color: #666666 !important;
  }

  .agent-form-modal .ant-input-password input {
    color: #000000 !important;
  }

  .agent-form-modal .ant-select-selector {
    color: #000000 !important;
  }

  .agent-form-modal .ant-select-selection-search-input {
    color: #000000 !important;
  }

  .text-black .ant-input {
    color: #000000 !important;
  }

  .agent-form-modal .ant-input {
    color: #000000 !important;
  }
`

export default function AgentLibraryPage() {
  const router = useRouter()
  const [showAgentForm, setShowAgentForm] = useState(false)
  const [editingAgent, setEditingAgent] = useState<Agent | null>(null)
  const [agentForm] = Form.useForm()

  // 使用全局智能体状态管理
  const {
    agents,
    currentAgent,
    addAgent,
    updateAgent,
    deleteAgent,
    toggleAgent,
    initializeAgents,
    syncToServer
  } = useAgentStore()

  // 初始化智能体数据
  useEffect(() => {
    initializeAgents()
  }, [initializeAgents])

  // 统计数据
  const enabledAgents = agents.filter(agent => agent.enabled)
  const disabledAgents = agents.filter(agent => !agent.enabled)
  const departmentStats = agents.reduce((acc, agent) => {
    acc[agent.department] = (acc[agent.department] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // 智能体管理函数
  const handleAddAgent = () => {
    setEditingAgent(null)
    agentForm.resetFields()
    setShowAgentForm(true)
  }

  const handleEditAgent = (agent: Agent) => {
    setEditingAgent(agent)
    agentForm.setFieldsValue(agent)
    setShowAgentForm(true)
  }

  const handleDeleteAgent = async (agentId: string) => {
    // 检查是否是当前使用的智能体
    if (currentAgent?.id === agentId) {
      message.error('无法删除当前使用的智能体')
      return
    }

    try {
      await deleteAgent(agentId)
      message.success('智能体已删除并同步到服务器')
    } catch (error) {
      console.error('删除智能体失败:', error)
      message.error('删除失败，请重试')
    }
  }

  const handleToggleAgent = async (agentId: string) => {
    // 检查是否是当前使用的智能体
    if (currentAgent?.id === agentId && agents.find(a => a.id === agentId)?.enabled) {
      message.error('无法停用当前使用的智能体')
      return
    }

    const agent = agents.find(a => a.id === agentId)
    if (!agent) return

    try {
      await toggleAgent(agentId)
      message.success(`智能体 ${agent.name} 已${agent.enabled ? '停用' : '启用'}并同步到服务器`)
    } catch (error) {
      console.error('切换智能体状态失败:', error)
      message.error('操作失败，请重试')
    }
  }

  const handleSyncToServer = async () => {
    try {
      await syncToServer()
      message.success('智能体数据已同步到服务器')
    } catch (error) {
      console.error('同步失败:', error)
      message.error('同步失败，请重试')
    }
  }

  const handleSaveAgent = async (values: any) => {
    try {
      if (editingAgent) {
        // 编辑现有智能体
        await updateAgent(editingAgent.id, values)
        message.success('智能体信息已更新并保存到服务器')
      } else {
        // 新增智能体
        await addAgent(values)
        message.success('智能体已添加并保存到服务器')
      }

      setShowAgentForm(false)
      agentForm.resetFields()
      setEditingAgent(null)
    } catch (error) {
      console.error('保存智能体失败:', error)
      message.error('保存失败，请检查网络连接后重试')
    }
  }

  const getDepartmentColor = (department: string) => {
    const colors: Record<string, string> = {
      '财务部': 'blue',
      '风控部': 'red',
      '合规部': 'orange',
      '投资部': 'green',
      '审计部': 'purple',
      '全部门': 'cyan'
    }
    return colors[department] || 'default'
  }

  return (
    <DashboardLayout>
      <style dangerouslySetInnerHTML={{ __html: inputTextStyles }} />
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
        {/* 页面头部 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => router.back()}
                className="flex items-center space-x-2"
              >
                返回
              </Button>
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <RobotOutlined className="text-xl text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    智能体库管理
                  </h1>
                  <p className="text-gray-600 mt-1">管理和配置您的AI智能体</p>
                </div>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button
                icon={<SyncOutlined />}
                onClick={handleSyncToServer}
                size="large"
                className="border-blue-300 text-blue-600 hover:border-blue-500 hover:text-blue-700"
              >
                同步到服务器
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddAgent}
                size="large"
                className="bg-gradient-to-r from-blue-500 to-purple-600 border-0 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                新增智能体
              </Button>
            </div>
          </div>
        </motion.div>

        {/* 统计卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <Row gutter={[24, 24]}>
            <Col xs={24} sm={12} lg={6}>
              <Card className="text-center shadow-lg border-0 bg-gradient-to-br from-blue-50 to-blue-100">
                <Statistic
                  title="总智能体数"
                  value={agents.length}
                  prefix={<RobotOutlined className="text-blue-500" />}
                  valueStyle={{ color: '#1890ff', fontSize: '2rem', fontWeight: 'bold' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="text-center shadow-lg border-0 bg-gradient-to-br from-green-50 to-green-100">
                <Statistic
                  title="已启用"
                  value={enabledAgents.length}
                  prefix={<CheckCircleOutlined className="text-green-500" />}
                  valueStyle={{ color: '#52c41a', fontSize: '2rem', fontWeight: 'bold' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="text-center shadow-lg border-0 bg-gradient-to-br from-orange-50 to-orange-100">
                <Statistic
                  title="已停用"
                  value={disabledAgents.length}
                  prefix={<StopOutlined className="text-orange-500" />}
                  valueStyle={{ color: '#fa8c16', fontSize: '2rem', fontWeight: 'bold' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="text-center shadow-lg border-0 bg-gradient-to-br from-purple-50 to-purple-100">
                <Statistic
                  title="部门数量"
                  value={Object.keys(departmentStats).length}
                  prefix={<TeamOutlined className="text-purple-500" />}
                  valueStyle={{ color: '#722ed1', fontSize: '2rem', fontWeight: 'bold' }}
                />
              </Card>
            </Col>
          </Row>
        </motion.div>

        {/* 智能体卡片网格 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Row gutter={[24, 24]}>
            {agents.map((agent, index) => (
              <Col xs={24} sm={12} lg={8} xl={6} key={agent.id}>
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ y: -5, transition: { duration: 0.2 } }}
                >
                  <Card
                    className={`h-full shadow-lg border-0 transition-all duration-300 hover:shadow-xl ${
                      agent.enabled
                        ? 'bg-gradient-to-br from-white to-blue-50'
                        : 'bg-gradient-to-br from-gray-50 to-gray-100'
                    }`}
                    bodyStyle={{ padding: '24px' }}
                  >
                    {/* 智能体头部 */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <Avatar
                          size={48}
                          icon={<RobotOutlined />}
                          className={`${
                            agent.enabled
                              ? 'bg-gradient-to-br from-blue-500 to-purple-600'
                              : 'bg-gray-400'
                          } shadow-lg`}
                        />
                        <div>
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="text-lg font-bold text-gray-800">
                              {agent.name}
                            </h3>
                            {currentAgent?.id === agent.id && (
                              <Tag color="green" size="small">当前使用</Tag>
                            )}
                          </div>
                          <Tag
                            color={getDepartmentColor(agent.department)}
                            className="mb-0"
                          >
                            {agent.department}
                          </Tag>
                        </div>
                      </div>
                      <Switch
                        checked={agent.enabled}
                        onChange={() => handleToggleAgent(agent.id)}
                        className={agent.enabled ? 'bg-blue-500' : ''}
                      />
                    </div>

                    {/* 功能描述 */}
                    <div className="mb-4">
                      <p className="text-sm text-gray-600 leading-relaxed bg-gray-50 p-3 rounded-lg">
                        {agent.description}
                      </p>
                    </div>

                    {/* 智能体信息 */}
                    <div className="space-y-3 mb-4">
                      <div className="flex items-center space-x-2 text-sm">
                        <SafetyOutlined className="text-gray-500" />
                        <span className="text-gray-600">数据范围:</span>
                        <span className="text-gray-800 font-medium">{agent.dataScope}</span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm">
                        <UserOutlined className="text-gray-500" />
                        <span className="text-gray-600">权限限额:</span>
                        <span className="text-gray-800 font-medium">{agent.permissionLimit}</span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm">
                        <KeyOutlined className="text-gray-500" />
                        <span className="text-gray-600">API Key:</span>
                        <span className="font-mono text-xs text-gray-600">
                          {agent.apiKey.substring(0, 8)}...{agent.apiKey.substring(agent.apiKey.length - 4)}
                        </span>
                      </div>
                    </div>

                    <Divider className="my-4" />

                    {/* 时间信息 */}
                    <div className="text-xs text-gray-500 mb-4">
                      <div>创建时间: {agent.createTime}</div>
                      <div>更新时间: {agent.updateTime}</div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex space-x-2">
                      <Button
                        type="primary"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => handleEditAgent(agent)}
                        className="flex-1 bg-blue-500 border-blue-500"
                      >
                        编辑
                      </Button>
                      <Popconfirm
                        title="确定要删除这个智能体吗？"
                        onConfirm={() => handleDeleteAgent(agent.id)}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Button
                          danger
                          size="small"
                          icon={<DeleteOutlined />}
                          className="flex-1"
                        >
                          删除
                        </Button>
                      </Popconfirm>
                    </div>
                  </Card>
                </motion.div>
              </Col>
            ))}
          </Row>
        </motion.div>

        {/* 智能体表单弹窗 */}
        <Modal
          title={
            <div className="flex items-center space-x-2">
              <SettingOutlined className="text-blue-500" />
              <span>{editingAgent ? '编辑智能体' : '新增智能体'}</span>
            </div>
          }
          open={showAgentForm}
          onCancel={() => {
            setShowAgentForm(false)
            agentForm.resetFields()
          }}
          onOk={() => agentForm.submit()}
          width={600}
          className="agent-form-modal"
        >
          <Form
            form={agentForm}
            layout="vertical"
            onFinish={handleSaveAgent}
          >
            <Form.Item
              name="name"
              label="智能体名称"
              rules={[{ required: true, message: '请输入智能体名称' }]}
            >
              <Input
                placeholder="请输入智能体名称"
                size="large"
                style={{ color: '#000000' }}
                className="text-black"
              />
            </Form.Item>

            <Form.Item
              name="description"
              label="功能描述"
              rules={[{ required: true, message: '请输入功能描述' }]}
            >
              <Input.TextArea
                placeholder="请输入一句话功能描述，帮助用户了解该智能体的主要功能"
                size="large"
                rows={2}
                style={{ color: '#000000' }}
                className="text-black"
                maxLength={100}
                showCount
              />
            </Form.Item>

            <Form.Item
              name="department"
              label="使用部门"
              rules={[{ required: false }]}
            >
              <Select
                placeholder="请选择使用部门"
                size="large"
                style={{ color: '#000000' }}
                className="text-black"
              >
                <Option value="财务部">财务部</Option>
                <Option value="风控部">风控部</Option>
                <Option value="合规部">合规部</Option>
                <Option value="投资部">投资部</Option>
                <Option value="审计部">审计部</Option>
                <Option value="全部门">全部门</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="dataScope"
              label="数据范围"
            >
              <Select
                placeholder="请选择数据访问范围"
                size="large"
                style={{ color: '#000000' }}
                className="text-black"
              >
                <Option value="全部财务数据">全部财务数据</Option>
                <Option value="风险相关数据">风险相关数据</Option>
                <Option value="合规审计数据">合规审计数据</Option>
                <Option value="投资决策数据">投资决策数据</Option>
                <Option value="基础财务数据">基础财务数据</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="permissionLimit"
              label="权限限额"
            >
              <Select
                placeholder="请选择权限限额"
                size="large"
                style={{ color: '#000000' }}
                className="text-black"
              >
                <Option value="无限制">无限制</Option>
                <Option value="仅查看">仅查看</Option>
                <Option value="审计权限">审计权限</Option>
                <Option value="分析权限">分析权限</Option>
                <Option value="基础权限">基础权限</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="apiKey"
              label="API Key"
              rules={[{ required: true, message: '请输入API Key' }]}
            >
              <Input.Password
                placeholder="请输入Dify智能体的API Key"
                visibilityToggle
                size="large"
                style={{ color: '#000000' }}
                className="text-black"
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </DashboardLayout>
  )
}
