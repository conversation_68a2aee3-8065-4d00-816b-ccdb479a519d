# 吉发集团财务AI平台

<div align="center">

![Finance AI Platform](https://img.shields.io/badge/Finance-AI%20Platform-blue?style=for-the-badge)
![Next.js](https://img.shields.io/badge/Next.js-15.3.5-black?style=for-the-badge&logo=next.js)
![Docker](https://img.shields.io/badge/Docker-Ready-blue?style=for-the-badge&logo=docker)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)

**智能化财务管理平台，集成20个专业AI智能体**

[🚀 快速开始](#快速开始) • [📖 功能特性](#功能特性) • [🐳 Docker部署](#docker部署) • [📚 文档](#文档)

</div>

---

## 📋 项目概述

吉发集团财务AI平台是一个现代化的智能财务管理系统，基于Next.js 15构建，集成了20个专业的AI智能体，为企业提供全方位的财务智能化解决方案。

### 🎯 核心价值

- **智能化决策**: 20个专业AI智能体提供精准的财务分析和建议
- **实时数据**: 动态仪表板展示关键财务指标和趋势
- **多场景应用**: 覆盖预算、风控、审计、税务等全业务场景
- **企业级安全**: 完善的权限管理和数据安全保障

## ✨ 功能特性

### 🤖 智能体管理
- **20个预配置智能体**: 覆盖财务管理全流程
- **动态配置**: 支持智能体参数调整和API密钥管理
- **权限控制**: 细粒度的数据访问权限设置
- **实时切换**: 无缝切换不同专业领域的AI助手

### 📊 数据可视化
- **实时仪表板**: ECharts驱动的动态图表展示
- **多维分析**: 收入、成本、利润等多角度财务分析
- **趋势预测**: 基于历史数据的智能趋势分析
- **风险监控**: 实时财务风险评估和预警

### 💬 智能对话
- **流式响应**: 实时流式AI对话体验
- **多模态交互**: 支持文本、图表、文件等多种交互方式
- **上下文记忆**: 智能体间独立的对话会话管理
- **专业回答**: 基于Dify平台的专业财务知识库

### 🎨 用户体验
- **现代化UI**: 基于Ant Design的专业界面设计
- **响应式布局**: 完美适配桌面和移动设备
- **科技感设计**: 符合国企形象的稳重专业风格
- **流畅动画**: Framer Motion驱动的丝滑交互体验

## 🚀 快速开始

### 方式一：Docker部署（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd finance-ai-demo

# 2. 运行部署脚本
./deploy.sh

# 3. 访问应用
open http://localhost:5666
```

### 方式二：本地开发

```bash
# 1. 安装依赖
npm install

# 2. 启动开发服务器
npm run dev

# 3. 访问应用
open http://localhost:5666
```

## 🐳 Docker部署

### 系统要求
- Docker 20.10.0+
- Docker Compose 1.29.0+
- 4GB+ 内存
- 10GB+ 存储空间

### 部署步骤

1. **准备环境**
   ```bash
   # 确保Docker服务运行
   docker --version
   docker-compose --version
   ```

2. **配置环境变量**
   ```bash
   # 编辑docker-compose.yml中的环境变量
   vim docker-compose.yml
   ```

3. **执行部署**
   ```bash
   # Linux/macOS
   ./deploy.sh

   # Windows
   deploy.bat
   ```

4. **验证部署**
   ```bash
   # 检查服务状态
   docker-compose ps

   # 查看日志
   docker-compose logs -f
   ```

### 健康检查

访问 `http://localhost:3000/api/health` 查看服务状态：

```json
{
  "status": "healthy",
  "timestamp": "2025-07-09T12:00:00.000Z",
  "version": "1.0.0",
  "services": {
    "api": "running",
    "agents": "20 loaded"
  }
}
```

## 📚 文档

- [📖 部署指南](./DEPLOYMENT.md) - 详细的Docker部署说明
- [📋 任务清单](./task.md) - 项目开发进度和任务记录
- [🎤 演讲稿](./系统介绍演讲稿.md) - 系统介绍和演示材料

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Next.js 15.3.5 (App Router)
- **语言**: TypeScript 5.0
- **UI库**: Ant Design 5.x
- **动画**: Framer Motion
- **图表**: ECharts + ECharts for React
- **样式**: Tailwind CSS + CSS Modules
- **状态管理**: Zustand

### 后端技术栈
- **运行时**: Node.js 18+
- **API**: Next.js API Routes
- **AI集成**: Dify Platform
- **数据存储**: JSON文件 + 文件系统
- **部署**: Docker + Docker Compose

### 核心特性
- **服务端渲染**: Next.js SSR/SSG
- **API代理**: 避免CORS和密钥暴露
- **数据持久化**: 文件系统存储
- **健康检查**: 内置监控端点
- **容器化**: Docker多阶段构建

## 🤖 智能体列表

系统预配置了20个专业的财务AI智能体：

| 序号 | 智能体名称 | 主要功能 | 应用场景 |
|------|------------|----------|----------|
| 1 | 财务智能助手 | 综合财务咨询 | 日常财务问题解答 |
| 2 | 财务报表洞察智能体 | 报表分析与洞察 | 财务报表深度分析 |
| 3 | 智能预算编制智能体 | 预算制定与管理 | 年度预算编制 |
| 4 | 现金流预测与调度智能体 | 现金流管理 | 资金计划与调度 |
| 5 | 项目投后管理智能体 | 投资项目管理 | 投资效果评估 |
| 6 | 投资组合优化智能体 | 投资组合分析 | 资产配置优化 |
| 7 | 成本算法智能体 | 成本核算分析 | 成本控制与优化 |
| 8 | 费用报销审核智能体 | 费用审核管理 | 报销流程自动化 |
| 9 | 供应链金融智能体 | 供应链金融 | 供应商金融服务 |
| 10 | 招投标财务评分智能体 | 招投标评估 | 供应商财务评价 |
| 11 | 财务风险雷达智能体 | 风险识别预警 | 财务风险管控 |
| 12 | 税务筹划与申报智能体 | 税务管理 | 税务筹划与申报 |
| 13 | 国资委报表助手智能体 | 国资委报表 | 国有企业报表编制 |
| 14 | IFRS/新准则转换智能体 | 会计准则转换 | 新旧准则对接 |
| 15 | ESG财务披露智能体 | ESG报告 | 可持续发展报告 |
| 16 | 内部审计智能体 | 内部审计 | 审计流程自动化 |
| 17 | 财务制度知识库问答智能体 | 制度咨询 | 财务制度解答 |
| 18 | 会议纪要和任务智能体 | 会议管理 | 会议记录与任务跟踪 |
| 19 | 知识蒸馏与训练智能体 | 知识管理 | AI模型训练优化 |
| 20 | 财务-企业微信联动智能体 | 系统集成 | 企业微信集成 |

## 🌐 页面导航

- **🏠 首页** (`/`) - 平台概览和快速导航
- **📊 仪表板** (`/dashboard`) - 财务数据可视化大屏
- **💬 智能对话** (`/chat`) - AI智能体对话界面
- **🤖 智能体管理** (`/agent-library`) - 智能体配置和管理
- **🔍 洞察工作室** (`/insight-studio`) - 数据洞察分析
- **📈 趋势分析** (`/trend-analysis`) - 财务趋势预测
- **🧪 数据建模** (`/simulation`) - 财务模型仿真
- **🏛️ 董事会** (`/boardroom`) - 高管决策支持

## 🔧 开发指南

### 本地开发环境

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start

# 代码检查
npm run lint
```

### 环境变量配置

创建 `.env.local` 文件：

```env
# Dify API配置
DIFY_API_URL=https://ai.hongtai-idi.com/v1
DIFY_API_KEY=your-api-key-here

# 应用配置
NODE_ENV=development
PORT=3000
```

### 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API路由
│   ├── chat/              # 智能对话页面
│   ├── dashboard/         # 仪表板页面
│   └── ...
├── components/            # 可复用组件
├── services/              # 服务层
├── store/                 # 状态管理
├── styles/                # 样式文件
└── utils/                 # 工具函数
```

## 📄 许可证

本项目为吉发集团内部使用，版权所有。

## 🤝 贡献

感谢所有为本项目做出贡献的开发者！

---

<div align="center">

**🏢 吉发集团财务AI平台**

*让AI赋能财务管理，让数据驱动决策*

</div>
