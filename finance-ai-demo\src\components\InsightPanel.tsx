'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, Badge, Progress, Statistic, Timeline, Tag } from 'antd'
import {
  ThunderboltOutlined,
  RiseOutlined,
  AlertOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'

interface InsightPanelProps {
  visible: boolean
  onClose: () => void
}

export default function InsightPanel({ visible, onClose }: InsightPanelProps) {
  const [activeTab, setActiveTab] = useState('insights')

  // 实时洞察数据
  const insights = [
    {
      id: 1,
      type: 'trend',
      title: '营收增长趋势',
      content: 'Q4营收同比增长12.3%，超出预期目标2.3个百分点',
      confidence: 95,
      impact: 'high',
      timestamp: '2分钟前'
    },
    {
      id: 2,
      type: 'risk',
      title: '现金流预警',
      content: '预计下月现金流可能出现短期紧张，建议提前安排资金调度',
      confidence: 78,
      impact: 'medium',
      timestamp: '5分钟前'
    },
    {
      id: 3,
      type: 'opportunity',
      title: '投资机会',
      content: '检测到优质理财产品，预期年化收益率6.8%，风险等级较低',
      confidence: 88,
      impact: 'medium',
      timestamp: '10分钟前'
    }
  ]

  // 智能体活动记录
  const agentActivities = [
    {
      agent: '财务分析师',
      action: '完成Q4财务报表分析',
      status: 'completed',
      time: '刚刚'
    },
    {
      agent: '风险评估师',
      action: '识别潜在现金流风险',
      status: 'completed',
      time: '3分钟前'
    },
    {
      agent: '报告生成器',
      action: '正在生成月度财务报告',
      status: 'processing',
      time: '5分钟前'
    },
    {
      agent: '投资顾问',
      action: '分析投资组合表现',
      status: 'pending',
      time: '8分钟前'
    }
  ]

  // 趋势图配置
  const trendOption = {
    title: {
      text: '关键指标趋势',
      textStyle: { fontSize: 14, color: '#334155' }
    },
    tooltip: { trigger: 'axis' },
    legend: {
      data: ['营收', '利润', '现金流'],
      bottom: 0,
      textStyle: { fontSize: 12 }
    },
    grid: { left: '3%', right: '4%', bottom: '15%', top: '20%', containLabel: true },
    xAxis: {
      type: 'category',
      data: ['10月', '11月', '12月', '1月'],
      axisLabel: { fontSize: 10 }
    },
    yAxis: {
      type: 'value',
      axisLabel: { fontSize: 10 }
    },
    series: [
      {
        name: '营收',
        type: 'line',
        data: [2200, 2800, 3200, 3600],
        smooth: true,
        lineStyle: { color: '#1890ff' },
        areaStyle: { color: 'rgba(24, 144, 255, 0.1)' }
      },
      {
        name: '利润',
        type: 'line',
        data: [580, 720, 850, 960],
        smooth: true,
        lineStyle: { color: '#52c41a' },
        areaStyle: { color: 'rgba(82, 196, 26, 0.1)' }
      },
      {
        name: '现金流',
        type: 'line',
        data: [1800, 2200, 2600, 2850],
        smooth: true,
        lineStyle: { color: '#fa8c16' },
        areaStyle: { color: 'rgba(250, 140, 22, 0.1)' }
      }
    ]
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'trend': return <RiseOutlined className="text-blue-500" />
      case 'risk': return <AlertOutlined className="text-red-500" />
      case 'opportunity': return <ThunderboltOutlined className="text-green-500" />
      default: return <ExclamationCircleOutlined />
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'red'
      case 'medium': return 'orange'
      case 'low': return 'green'
      default: return 'default'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircleOutlined className="text-green-500" />
      case 'processing': return <ClockCircleOutlined className="text-blue-500" />
      case 'pending': return <ExclamationCircleOutlined className="text-orange-500" />
      default: return <ClockCircleOutlined />
    }
  }

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{ opacity: 0, x: 300 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 300 }}
          transition={{ duration: 0.3 }}
          className="fixed right-0 top-0 h-full w-96 bg-white shadow-2xl z-50 border-l"
        >
          {/* 头部 */}
          <div className="p-4 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-slate-800">AI洞察面板</h3>
              <button 
                onClick={onClose}
                className="text-slate-500 hover:text-slate-700 text-xl"
              >
                ×
              </button>
            </div>
            
            {/* 标签页 */}
            <div className="flex space-x-1 mt-3">
              {[
                { key: 'insights', label: '实时洞察' },
                { key: 'activities', label: '智能体活动' },
                { key: 'trends', label: '趋势分析' }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key)}
                  className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                    activeTab === tab.key 
                      ? 'bg-blue-500 text-white' 
                      : 'text-slate-600 hover:bg-blue-100'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 overflow-y-auto p-4">
            {activeTab === 'insights' && (
              <div className="space-y-4">
                {insights.map((insight) => (
                  <motion.div
                    key={insight.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card size="small" className="shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-start space-x-3">
                        <div className="mt-1">
                          {getInsightIcon(insight.type)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-slate-800">{insight.title}</h4>
                            <Tag color={getImpactColor(insight.impact)}>
                              {insight.impact === 'high' ? '高影响' : 
                               insight.impact === 'medium' ? '中影响' : '低影响'}
                            </Tag>
                          </div>
                          <p className="text-sm text-slate-600 mb-3">{insight.content}</p>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <span className="text-xs text-slate-400">置信度</span>
                              <Progress 
                                percent={insight.confidence} 
                                size="small" 
                                strokeColor={insight.confidence > 80 ? '#52c41a' : '#fa8c16'}
                                showInfo={false}
                                className="w-16"
                              />
                              <span className="text-xs text-slate-500">{insight.confidence}%</span>
                            </div>
                            <span className="text-xs text-slate-400">{insight.timestamp}</span>
                          </div>
                        </div>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </div>
            )}

            {activeTab === 'activities' && (
              <div>
                <Timeline>
                  {agentActivities.map((activity, index) => (
                    <Timeline.Item
                      key={index}
                      dot={getStatusIcon(activity.status)}
                    >
                      <div className="pb-3">
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium text-slate-800">{activity.agent}</span>
                          <span className="text-xs text-slate-400">{activity.time}</span>
                        </div>
                        <p className="text-sm text-slate-600">{activity.action}</p>
                        <Badge 
                          status={
                            activity.status === 'completed' ? 'success' :
                            activity.status === 'processing' ? 'processing' : 'warning'
                          }
                          text={
                            activity.status === 'completed' ? '已完成' :
                            activity.status === 'processing' ? '进行中' : '等待中'
                          }
                          className="text-xs"
                        />
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </div>
            )}

            {activeTab === 'trends' && (
              <div>
                <Card size="small" className="mb-4">
                  <ReactECharts option={trendOption} style={{ height: '200px' }} />
                </Card>
                
                <div className="grid grid-cols-2 gap-3">
                  <Card size="small" className="text-center">
                    <Statistic 
                      title="月增长率" 
                      value={12.3} 
                      suffix="%" 
                      valueStyle={{ color: '#52c41a', fontSize: '18px' }}
                    />
                  </Card>
                  <Card size="small" className="text-center">
                    <Statistic 
                      title="风险指数" 
                      value={65} 
                      suffix="/100" 
                      valueStyle={{ color: '#fa8c16', fontSize: '18px' }}
                    />
                  </Card>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
