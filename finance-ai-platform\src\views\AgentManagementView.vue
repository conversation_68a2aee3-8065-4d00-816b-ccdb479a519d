<template>
  <div class="min-h-screen bg-slate-50">
    <!-- 顶部工具栏 -->
    <header class="bg-white shadow-sm border-b border-slate-200">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <router-link to="/dashboard" class="text-slate-400 hover:text-slate-600">
              <ArrowLeftOutlined class="w-5 h-5" />
            </router-link>
            <div>
              <h1 class="text-xl font-bold text-slate-900">智能体管理</h1>
              <p class="text-sm text-slate-500">Agent Management - 配置和管理Dify智能体</p>
            </div>
          </div>
          
          <div class="flex items-center space-x-3">
            <a-badge :count="enabledAgents.length" class="mr-2">
              <a-button @click="testAllConnections">
                <ApiOutlined />
                测试连接
              </a-button>
            </a-badge>
            <a-button type="primary" @click="showConfigModal = true">
              <SettingOutlined />
              全局配置
            </a-button>
          </div>
        </div>
      </div>
    </header>

    <div class="p-6">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="finance-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-slate-500">总智能体数</p>
              <p class="text-2xl font-bold text-slate-900">{{ agents.length }}</p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <RobotOutlined class="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>
        
        <div class="finance-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-slate-500">已启用</p>
              <p class="text-2xl font-bold text-green-600">{{ enabledAgents.length }}</p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircleOutlined class="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>
        
        <div class="finance-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-slate-500">总调用次数</p>
              <p class="text-2xl font-bold text-slate-900">{{ totalCalls }}</p>
            </div>
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <BarChartOutlined class="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
        
        <div class="finance-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-slate-500">平均成功率</p>
              <p class="text-2xl font-bold text-slate-900">{{ averageSuccessRate }}%</p>
            </div>
            <div class="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
              <TrophyOutlined class="w-6 h-6 text-emerald-600" />
            </div>
          </div>
        </div>
      </div>

      <!-- 智能体列表 -->
      <div class="space-y-6">
        <div v-for="(categoryAgents, category) in agentsByCategory" :key="category" class="bg-white rounded-lg shadow-sm border border-slate-200">
          <div class="px-6 py-4 border-b border-slate-200">
            <h2 class="text-lg font-bold text-slate-900">{{ category }}</h2>
            <p class="text-sm text-slate-500">{{ categoryAgents.length }} 个智能体</p>
          </div>
          
          <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              <div
                v-for="agent in categoryAgents"
                :key="agent.id"
                class="border border-slate-200 rounded-lg p-4 hover:border-blue-300 transition-colors"
                :class="{ 'border-green-300 bg-green-50': agent.enabled }"
              >
                <div class="flex items-start justify-between mb-3">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 rounded-lg flex items-center justify-center" :class="getAgentIconBg(agent.appType)">
                      <component :is="getAgentIcon(agent.appType)" class="w-5 h-5" :class="getAgentIconColor(agent.appType)" />
                    </div>
                    <div>
                      <h3 class="font-medium text-slate-900">{{ agent.name }}</h3>
                      <p class="text-xs text-slate-500">{{ agent.appType.toUpperCase() }}</p>
                    </div>
                  </div>
                  
                  <a-switch
                    v-model:checked="agent.enabled"
                    :disabled="!agent.apiKey"
                    @change="handleAgentToggle(agent)"
                  />
                </div>
                
                <p class="text-sm text-slate-600 mb-4">{{ agent.description }}</p>
                
                <!-- API Key 配置 -->
                <div class="mb-4">
                  <label class="block text-xs font-medium text-slate-700 mb-1">API Key</label>
                  <div class="flex space-x-2">
                    <a-input
                      v-model:value="agent.apiKey"
                      placeholder="输入Dify API Key"
                      type="password"
                      size="small"
                      @change="saveAgentsToStorage"
                    />
                    <a-button size="small" @click="testConnection(agent)" :loading="testingAgents[agent.id]">
                      <ApiOutlined />
                    </a-button>
                  </div>
                </div>
                
                <!-- 统计信息 -->
                <div v-if="agent.enabled && agent.totalCalls" class="grid grid-cols-2 gap-4 text-xs">
                  <div>
                    <span class="text-slate-500">调用次数:</span>
                    <span class="font-medium ml-1">{{ agent.totalCalls }}</span>
                  </div>
                  <div>
                    <span class="text-slate-500">成功率:</span>
                    <span class="font-medium ml-1" :class="getSuccessRateColor(agent.successRate)">
                      {{ (agent.successRate * 100).toFixed(1) }}%
                    </span>
                  </div>
                  <div>
                    <span class="text-slate-500">响应时间:</span>
                    <span class="font-medium ml-1">{{ agent.avgResponseTime?.toFixed(0) }}ms</span>
                  </div>
                  <div>
                    <span class="text-slate-500">最后使用:</span>
                    <span class="font-medium ml-1">{{ formatLastUsed(agent.lastUsed) }}</span>
                  </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex space-x-2 mt-4">
                  <a-button size="small" @click="openChatWithAgent(agent)" :disabled="!agent.enabled">
                    <MessageOutlined />
                    测试对话
                  </a-button>
                  <a-button size="small" @click="viewAgentLogs(agent)" :disabled="!agent.enabled">
                    <FileTextOutlined />
                    查看日志
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 未分类的智能体 -->
        <div v-if="uncategorizedAgents.length > 0" class="bg-white rounded-lg shadow-sm border border-slate-200">
          <div class="px-6 py-4 border-b border-slate-200">
            <h2 class="text-lg font-bold text-slate-900">其他智能体</h2>
            <p class="text-sm text-slate-500">{{ uncategorizedAgents.length }} 个智能体</p>
          </div>
          
          <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              <!-- 智能体卡片内容同上 -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全局配置模态框 -->
    <a-modal
      v-model:open="showConfigModal"
      title="全局配置"
      width="600px"
      @ok="saveGlobalConfig"
    >
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-slate-700 mb-2">Dify 平台地址</label>
          <a-input
            v-model:value="tempDifyBaseUrl"
            placeholder="https://ai.hongtai-idi.com/v1"
          />
          <p class="text-xs text-slate-500 mt-1">您的Dify平台API基础地址</p>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-slate-700 mb-2">默认用户ID</label>
          <a-input
            v-model:value="tempCurrentUser"
            placeholder="finance-user-001"
          />
          <p class="text-xs text-slate-500 mt-1">用于API调用的用户标识</p>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-slate-700 mb-2">测试API Key</label>
          <a-input
            v-model:value="testApiKey"
            placeholder="app-3ajHRb54q01xGTfXd98qoDIx"
            type="password"
          />
          <p class="text-xs text-slate-500 mt-1">用于测试连接的API Key</p>
        </div>
      </div>
    </a-modal>

    <!-- 测试对话模态框 -->
    <a-modal
      v-model:open="showChatModal"
      :title="`测试对话 - ${selectedAgent?.name}`"
      width="800px"
      :footer="null"
    >
      <div v-if="selectedAgent" class="space-y-4">
        <div class="max-h-96 overflow-y-auto border border-slate-200 rounded-lg p-4">
          <div
            v-for="message in testMessages"
            :key="message.id"
            class="mb-4"
            :class="message.type === 'user' ? 'text-right' : 'text-left'"
          >
            <div
              class="inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg"
              :class="message.type === 'user' 
                ? 'bg-blue-600 text-white' 
                : 'bg-slate-100 text-slate-900'"
            >
              {{ message.content }}
            </div>
            <div class="text-xs text-slate-500 mt-1">
              {{ formatTime(message.timestamp) }}
            </div>
          </div>
          
          <div v-if="isLoading" class="text-center">
            <a-spin />
            <p class="text-sm text-slate-500 mt-2">智能体正在思考...</p>
          </div>
        </div>
        
        <div class="flex space-x-2">
          <a-input
            v-model:value="testMessage"
            placeholder="输入测试消息..."
            @keydown.enter="sendTestMessage"
            :disabled="isLoading"
          />
          <a-button type="primary" @click="sendTestMessage" :loading="isLoading">
            发送
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useAgentStore } from '@/stores/agentStore'
import {
  ArrowLeftOutlined,
  SettingOutlined,
  ApiOutlined,
  RobotOutlined,
  CheckCircleOutlined,
  BarChartOutlined,
  TrophyOutlined,
  MessageOutlined,
  FileTextOutlined,
  BranchesOutlined,
  ThunderboltOutlined,
  FileSearchOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const agentStore = useAgentStore()

// 响应式数据
const showConfigModal = ref(false)
const showChatModal = ref(false)
const selectedAgent = ref<any>(null)
const testingAgents = ref<Record<string, boolean>>({})
const testMessages = ref<any[]>([])
const testMessage = ref('')
const tempDifyBaseUrl = ref('')
const tempCurrentUser = ref('')
const testApiKey = ref('app-3ajHRb54q01xGTfXd98qoDIx')

// 计算属性
const { agents, enabledAgents, agentsByCategory, isLoading, saveAgentsToStorage } = agentStore

const uncategorizedAgents = computed(() => 
  agents.filter(agent => !Object.keys(agentsByCategory).some(category => 
    agentsByCategory[category].includes(agent)
  ))
)

const totalCalls = computed(() => 
  agents.reduce((sum, agent) => sum + (agent.totalCalls || 0), 0)
)

const averageSuccessRate = computed(() => {
  const enabledWithStats = enabledAgents.filter(agent => agent.successRate !== undefined)
  if (enabledWithStats.length === 0) return 0
  
  const sum = enabledWithStats.reduce((sum, agent) => sum + (agent.successRate || 0), 0)
  return Math.round((sum / enabledWithStats.length) * 100)
})

// 方法
const getAgentIcon = (appType: string) => {
  switch (appType) {
    case 'chatflow': return BranchesOutlined
    case 'workflow': return ThunderboltOutlined
    case 'completion': return FileSearchOutlined
    default: return RobotOutlined
  }
}

const getAgentIconBg = (appType: string) => {
  switch (appType) {
    case 'chatflow': return 'bg-blue-100'
    case 'workflow': return 'bg-green-100'
    case 'completion': return 'bg-purple-100'
    default: return 'bg-slate-100'
  }
}

const getAgentIconColor = (appType: string) => {
  switch (appType) {
    case 'chatflow': return 'text-blue-600'
    case 'workflow': return 'text-green-600'
    case 'completion': return 'text-purple-600'
    default: return 'text-slate-600'
  }
}

const getSuccessRateColor = (rate?: number) => {
  if (!rate) return 'text-slate-500'
  if (rate >= 0.9) return 'text-green-600'
  if (rate >= 0.7) return 'text-yellow-600'
  return 'text-red-600'
}

const formatLastUsed = (date?: Date) => {
  if (!date) return '从未使用'
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (minutes < 1440) return `${Math.floor(minutes / 60)}小时前`
  return `${Math.floor(minutes / 1440)}天前`
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleAgentToggle = (agent: any) => {
  if (agent.enabled && !agent.apiKey) {
    message.warning('请先配置API Key')
    agent.enabled = false
    return
  }
  
  if (agent.enabled) {
    agentStore.enableAgent(agent.id, agent.apiKey)
    message.success(`${agent.name} 已启用`)
  } else {
    agentStore.disableAgent(agent.id)
    message.info(`${agent.name} 已禁用`)
  }
}

const testConnection = async (agent: any) => {
  if (!agent.apiKey) {
    message.warning('请先配置API Key')
    return
  }
  
  testingAgents.value[agent.id] = true
  
  try {
    const success = await agentStore.testAgentConnection(agent.id)
    if (success) {
      message.success(`${agent.name} 连接测试成功`)
    } else {
      message.error(`${agent.name} 连接测试失败`)
    }
  } catch (error) {
    message.error(`${agent.name} 连接测试失败`)
  } finally {
    testingAgents.value[agent.id] = false
  }
}

const testAllConnections = async () => {
  const enabled = enabledAgents.filter(agent => agent.apiKey)
  if (enabled.length === 0) {
    message.warning('没有已配置的智能体')
    return
  }
  
  message.loading('正在测试所有连接...', 2)
  
  const results = await Promise.allSettled(
    enabled.map(agent => agentStore.testAgentConnection(agent.id))
  )
  
  const successful = results.filter(result => result.status === 'fulfilled' && result.value).length
  message.success(`连接测试完成：${successful}/${enabled.length} 个智能体连接正常`)
}

const openChatWithAgent = (agent: any) => {
  selectedAgent.value = agent
  testMessages.value = []
  testMessage.value = ''
  showChatModal.value = true
}

const sendTestMessage = async () => {
  if (!testMessage.value.trim() || !selectedAgent.value) return
  
  const userMessage = {
    id: Date.now().toString(),
    type: 'user',
    content: testMessage.value,
    timestamp: new Date()
  }
  
  testMessages.value.push(userMessage)
  const currentMessage = testMessage.value
  testMessage.value = ''
  
  try {
    await agentStore.sendMessageToAgent(selectedAgent.value.id, currentMessage)
    
    // 获取最新的对话消息
    const conversation = agentStore.conversations.find(c => c.agentId === selectedAgent.value.id)
    if (conversation && conversation.messages.length > 0) {
      const lastMessage = conversation.messages[conversation.messages.length - 1]
      if (lastMessage.type === 'assistant') {
        testMessages.value.push({
          id: lastMessage.id,
          type: 'assistant',
          content: lastMessage.content,
          timestamp: lastMessage.timestamp
        })
      }
    }
  } catch (error: any) {
    message.error(error.message || '发送消息失败')
  }
}

const viewAgentLogs = (agent: any) => {
  message.info(`查看 ${agent.name} 的调用日志`)
  // 这里可以跳转到日志页面或打开日志模态框
}

const saveGlobalConfig = () => {
  agentStore.difyBaseUrl = tempDifyBaseUrl.value || agentStore.difyBaseUrl
  agentStore.currentUser = tempCurrentUser.value || agentStore.currentUser
  
  // 如果提供了测试API Key，可以用它来测试连接
  if (testApiKey.value) {
    // 这里可以添加全局连接测试逻辑
  }
  
  showConfigModal.value = false
  message.success('全局配置已保存')
}

// 生命周期
onMounted(() => {
  agentStore.initialize()
  tempDifyBaseUrl.value = agentStore.difyBaseUrl
  tempCurrentUser.value = agentStore.currentUser
})
</script>
