{"version": "1.0.0", "timestamp": "2025-07-09T11:32:00.919Z", "agents": [{"id": "1", "name": "财务智能助手", "department": "财务部", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-xuReGCaYZmkWMn0JGAAxqBvg", "enabled": true, "createTime": "2025-01-01 10:00:00", "updateTime": "2025-01-01 10:00:00"}, {"name": "财务报表洞察智能体", "department": "财务部", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-hm51L3nYQ2MuzzG2cY2hvlTx", "id": "1751905965394", "enabled": true, "createTime": "2025/7/8 00:32:45", "updateTime": "2025/7/8 00:32:45"}, {"name": "智能预算编制智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-DYWV5CwAzlgsXAbUg6QHLOE6", "id": "1751906673562", "enabled": true, "createTime": "2025/7/8 00:44:33", "updateTime": "2025/7/8 00:44:33"}, {"name": "现金流预测与调度智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-3wZxdb3aGVT3Wpo4n7adj2L5", "id": "1751906762267", "enabled": true, "createTime": "2025/7/8 00:46:02", "updateTime": "2025/7/8 00:46:02"}, {"name": "项目投后管理智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-UUNzAkIRDWRBw2rGlYmPOSi1", "id": "1751906799656", "enabled": true, "createTime": "2025/7/8 00:46:39", "updateTime": "2025/7/8 00:46:39"}, {"name": "投资组合优化智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-0mIJygNnSNsbBVZNoHU3GbRr", "id": "1751906843412", "enabled": true, "createTime": "2025/7/8 00:47:23", "updateTime": "2025/7/8 00:47:23"}, {"name": "成本算法智能体", "department": "财务部", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-AXpwdBzLexHWMfsZThDtePki", "id": "1751907121263", "enabled": true, "createTime": "2025/7/8 00:52:01", "updateTime": "2025/7/8 00:52:01"}, {"name": "费用报销审核智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-IyA2VkAXZINYZsmN933t9eVA", "id": "1751907155210", "enabled": true, "createTime": "2025/7/8 00:52:35", "updateTime": "2025/7/8 00:52:35"}, {"name": "供应链金融智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-NkMBplvnJvPH9fj3cfcuvP9U", "id": "1751907216596", "enabled": true, "createTime": "2025/7/8 00:53:36", "updateTime": "2025/7/8 00:53:36"}, {"name": "招投标财务评分智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-TbsphjgCWUqRvoGRqhDqK3Hn", "id": "1751907258169", "enabled": true, "createTime": "2025/7/8 00:54:18", "updateTime": "2025/7/8 00:54:18"}, {"name": "财务风险雷达智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-fX4EFJzpu5pm79JY9smr4m2n", "id": "1751907290639", "enabled": true, "createTime": "2025/7/8 00:54:50", "updateTime": "2025/7/8 00:54:50"}, {"name": "税务筹划与申报智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-4BZrXVwWN4LWlrD2IAUOwCex", "id": "1751907321410", "enabled": true, "createTime": "2025/7/8 00:55:21", "updateTime": "2025/7/8 00:55:21"}, {"name": "国资委报表助手智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-G5yZd1kY5IRvjNIfciYsM3f2", "id": "1751907355875", "enabled": true, "createTime": "2025/7/8 00:55:55", "updateTime": "2025/7/8 01:03:56"}, {"name": "IFRS/新准则转换智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-j7Pk9oyLZPcC9vw9nBoEiUku", "id": "1751907397882", "enabled": true, "createTime": "2025/7/8 00:56:37", "updateTime": "2025/7/8 00:56:37"}, {"name": "ESG财务披露智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-2UzxL0XpAjiMV2xrfHoIWNnO", "id": "1751907432408", "enabled": true, "createTime": "2025/7/8 00:57:12", "updateTime": "2025/7/8 00:57:12"}, {"name": "内部审计智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-RDoG8ya6np88JpPpvPkfIJOZ", "id": "1751907467540", "enabled": true, "createTime": "2025/7/8 00:57:47", "updateTime": "2025/7/8 00:57:47"}, {"name": "财务制度知识库问答智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-biFOE7w5uy5EFzQrkDeg6GtQ", "id": "1751907501679", "enabled": true, "createTime": "2025/7/8 00:58:21", "updateTime": "2025/7/8 00:58:21"}, {"name": "会议纪要和任务智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-D8iSWsUHjyCC7vhsYL5T4Zm2", "id": "1751907535286", "enabled": true, "createTime": "2025/7/8 00:58:55", "updateTime": "2025/7/8 00:58:55"}, {"name": "知识蒸馏与训练智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-ybC7WB2nyXh9afRf10wKg9D0", "id": "1751907568780", "enabled": true, "createTime": "2025/7/8 00:59:28", "updateTime": "2025/7/8 00:59:28"}, {"name": "财务-企业微信联动智能体", "department": "全部门", "dataScope": "全部财务数据", "permissionLimit": "无限制", "apiKey": "app-B7WyuUsuBJKMThmvnq3AIJ9O", "id": "1751907602277", "enabled": true, "createTime": "2025/7/8 01:00:02", "updateTime": "2025/7/8 01:00:02"}]}