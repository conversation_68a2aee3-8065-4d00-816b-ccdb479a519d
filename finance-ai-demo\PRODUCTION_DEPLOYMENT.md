# 生产环境部署指南

## 🚀 快速部署步骤

### 1. 准备云服务器

推荐云服务商：
- **阿里云ECS**
- **腾讯云CVM** 
- **华为云ECS**
- **AWS EC2**

**服务器配置要求：**
- CPU: 2核心以上
- 内存: 4GB以上
- 存储: 20GB以上
- 操作系统: Ubuntu 20.04+

### 2. 配置服务器环境

```bash
# 1. 连接到服务器
ssh ubuntu@your-server-ip

# 2. 上传并运行环境配置脚本
scp server-setup.sh ubuntu@your-server-ip:~/
ssh ubuntu@your-server-ip 'chmod +x server-setup.sh && ./server-setup.sh'

# 3. 重新登录使Docker权限生效
ssh ubuntu@your-server-ip
```

### 3. 部署应用

```bash
# 在本地项目目录执行
chmod +x deploy-production.sh
./deploy-production.sh your-server-ip ubuntu
```

### 4. 验证部署

```bash
# 检查服务状态
curl http://your-server-ip/api/health

# 访问应用
open http://your-server-ip
```

## 🌐 域名配置（可选）

### 1. 购买域名
- 在域名注册商购买域名（如阿里云、腾讯云）
- 配置DNS解析指向服务器IP

### 2. 配置SSL证书

#### 方式一：Let's Encrypt免费证书
```bash
# 在服务器上安装certbot
sudo apt install certbot

# 申请证书
sudo certbot certonly --standalone -d your-domain.com

# 证书路径
# /etc/letsencrypt/live/your-domain.com/fullchain.pem
# /etc/letsencrypt/live/your-domain.com/privkey.pem
```

#### 方式二：云服务商SSL证书
- 在云服务商控制台申请免费SSL证书
- 下载证书文件到服务器

### 3. 启用Nginx反向代理

```bash
# 修改nginx.conf中的域名
vim nginx.conf

# 复制SSL证书到指定目录
sudo mkdir -p /opt/finance-ai-demo/ssl
sudo cp your-cert.pem /opt/finance-ai-demo/ssl/cert.pem
sudo cp your-key.pem /opt/finance-ai-demo/ssl/key.pem

# 使用生产配置重新部署
docker-compose -f docker-compose.prod.yml up -d
```

## 🔧 生产环境配置

### 环境变量配置

编辑 `docker-compose.prod.yml`：

```yaml
environment:
  - NODE_ENV=production
  - PORT=3000
  - HOSTNAME=0.0.0.0
  # 修改为您的Dify配置
  - DIFY_API_URL=https://your-dify-instance.com/v1
  - DIFY_API_KEY=your-production-api-key
```

### 数据备份策略

```bash
# 创建备份脚本
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/backups"
mkdir -p $BACKUP_DIR

# 备份智能体数据
cp /opt/finance-ai-demo/data/agents.json $BACKUP_DIR/agents_$DATE.json

# 备份Docker镜像
docker save finance-ai-demo_finance-ai-demo > $BACKUP_DIR/finance-ai-demo_$DATE.tar

# 清理7天前的备份
find $BACKUP_DIR -name "*.json" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar" -mtime +7 -delete
EOF

chmod +x backup.sh

# 设置定时备份
crontab -e
# 添加：0 2 * * * /opt/finance-ai-demo/backup.sh
```

## 📊 监控和维护

### 1. 查看服务状态
```bash
# 查看容器状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f

# 查看资源使用
docker stats
```

### 2. 性能监控
```bash
# 安装监控工具
sudo apt install htop iotop

# 查看系统资源
htop
iotop
df -h
```

### 3. 常用维护命令
```bash
# 重启服务
docker-compose -f docker-compose.prod.yml restart

# 更新应用
./deploy-production.sh your-server-ip ubuntu

# 清理Docker资源
docker system prune -f

# 查看磁盘使用
du -sh /opt/finance-ai-demo/*
```

## 🛡️ 安全配置

### 1. 防火墙配置
```bash
# 只开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw deny 3000   # 禁止直接访问应用端口
sudo ufw enable
```

### 2. SSH安全
```bash
# 禁用密码登录，只允许密钥登录
sudo vim /etc/ssh/sshd_config
# PasswordAuthentication no
# PubkeyAuthentication yes

sudo systemctl restart ssh
```

### 3. 定期更新
```bash
# 系统更新
sudo apt update && sudo apt upgrade -y

# Docker更新
sudo apt update docker-ce docker-ce-cli containerd.io
```

## 🚨 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 查看详细日志
   docker-compose -f docker-compose.prod.yml logs
   
   # 检查端口占用
   sudo netstat -tulpn | grep :80
   ```

2. **内存不足**
   ```bash
   # 查看内存使用
   free -h
   
   # 清理Docker缓存
   docker system prune -a
   ```

3. **磁盘空间不足**
   ```bash
   # 查看磁盘使用
   df -h
   
   # 清理日志文件
   sudo journalctl --vacuum-time=7d
   ```

## 📞 技术支持

如遇到部署问题，请检查：
1. 服务器配置是否满足要求
2. 网络连接是否正常
3. Docker服务是否正常运行
4. 防火墙配置是否正确

更多技术支持，请联系开发团队。
