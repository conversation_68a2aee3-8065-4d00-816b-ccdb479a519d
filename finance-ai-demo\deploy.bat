@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo Starting Finance AI Platform deployment...

REM Check Docker installation
docker --version >nul 2>&1
if errorlevel 1 (
    echo Docker not installed, please install Docker Desktop first
    pause
    exit /b 1
)

REM Check Docker Compose installation
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo Docker Compose not installed
    pause
    exit /b 1
)

REM Check agent data file
if not exist ".\data\agents.json" (
    echo Agent data file not found: .\data\agents.json
    echo Please ensure 20 agents are configured
    pause
    exit /b 1
)

REM Count agents
for /f %%i in ('findstr /c:"\"id\":" .\data\agents.json') do set /a AGENT_COUNT+=1
echo Found !AGENT_COUNT! agent configurations

REM Stop existing containers
echo Stopping existing containers...
docker-compose down --remove-orphans

REM Ask about cleaning old images
set /p CLEAN_IMAGES="Clean old Docker images? (y/N): "
if /i "!CLEAN_IMAGES!"=="y" (
    echo Cleaning old images...
    docker system prune -f
)

REM Build new images
echo Building Docker images...
docker-compose build --no-cache

REM Start services
echo Starting services...
docker-compose up -d

REM Wait for startup
echo Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check service status
echo Checking service status...
docker-compose ps

echo.
echo Deployment completed!
echo Access URL: http://localhost:5666
echo Health check: http://localhost:5666/api/health
echo Agent management: http://localhost:5666/agent-library
echo Chat interface: http://localhost:5666/chat
echo.
echo Common commands:
echo   View logs: docker-compose logs -f
echo   Stop service: docker-compose down
echo   Restart service: docker-compose restart
echo   Check status: docker-compose ps

pause
