'use client'

import { useState } from 'react'
import { Button, Input, Card, Alert, Spin } from 'antd'
import { difyApi } from '@/services/difyApi'

const { TextArea } = Input

export default function TestDifyPage() {
  const [message, setMessage] = useState('')
  const [response, setResponse] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [connectionStatus, setConnectionStatus] = useState<boolean | null>(null)

  const testConnection = async () => {
    setLoading(true)
    setError('')
    try {
      const isConnected = await difyApi.testConnection()
      setConnectionStatus(isConnected)
      if (!isConnected) {
        setError('Dify连接测试失败，请检查API配置')
      }
    } catch (err) {
      setError('连接测试出错: ' + (err as Error).message)
      setConnectionStatus(false)
    } finally {
      setLoading(false)
    }
  }

  const sendMessage = async () => {
    if (!message.trim()) return
    
    setLoading(true)
    setError('')
    setResponse('')
    
    try {
      const result = await difyApi.sendMessage(message)
      setResponse(result.answer)
      console.log('完整响应:', result)
    } catch (err) {
      setError('发送消息失败: ' + (err as Error).message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">Dify API 测试页面</h1>
        
        {/* API配置信息 */}
        <Card className="mb-6">
          <h2 className="text-xl font-semibold mb-4">API 配置信息</h2>
          <div className="space-y-2 text-sm">
            <div><strong>Base URL:</strong> https://ai.hongtai-idi.com/v1</div>
            <div><strong>API Key:</strong> app-3ajHRb54q01xGTfXd98qoDIx</div>
            <div><strong>Endpoint:</strong> /chat-messages</div>
          </div>
          
          <div className="mt-4">
            <Button 
              type="primary" 
              onClick={testConnection}
              loading={loading}
              className="mr-4"
            >
              测试连接
            </Button>
            
            {connectionStatus !== null && (
              <span className={`text-sm ${connectionStatus ? 'text-green-600' : 'text-red-600'}`}>
                {connectionStatus ? '✓ 连接成功' : '✗ 连接失败'}
              </span>
            )}
          </div>
        </Card>

        {/* 消息测试 */}
        <Card className="mb-6">
          <h2 className="text-xl font-semibold mb-4">消息测试</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">输入消息:</label>
              <TextArea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="输入要发送给Dify的消息..."
                rows={3}
              />
            </div>
            
            <Button 
              type="primary" 
              onClick={sendMessage}
              loading={loading}
              disabled={!message.trim()}
            >
              发送消息
            </Button>
          </div>
        </Card>

        {/* 错误显示 */}
        {error && (
          <Alert
            message="错误"
            description={error}
            type="error"
            showIcon
            className="mb-6"
          />
        )}

        {/* 响应显示 */}
        {response && (
          <Card>
            <h2 className="text-xl font-semibold mb-4">Dify 响应</h2>
            <div className="bg-gray-100 p-4 rounded-lg">
              <pre className="whitespace-pre-wrap text-sm">{response}</pre>
            </div>
          </Card>
        )}

        {/* 加载状态 */}
        {loading && (
          <div className="text-center py-8">
            <Spin size="large" />
            <div className="mt-2 text-gray-600">正在处理请求...</div>
          </div>
        )}

        {/* 快速测试按钮 */}
        <Card className="mt-6">
          <h2 className="text-xl font-semibold mb-4">快速测试</h2>
          <div className="space-x-2">
            {[
              '你好，请介绍一下你自己',
              '请帮我分析一下财务数据',
              '什么是风险评估？',
              '如何进行投资决策？'
            ].map((testMsg) => (
              <Button
                key={testMsg}
                size="small"
                onClick={() => {
                  setMessage(testMsg)
                  setTimeout(() => sendMessage(), 100)
                }}
                disabled={loading}
                className="mb-2"
              >
                {testMsg}
              </Button>
            ))}
          </div>
        </Card>

        {/* 调试信息 */}
        <Card className="mt-6">
          <h2 className="text-xl font-semibold mb-4">调试信息</h2>
          <div className="text-xs text-gray-600">
            <div>打开浏览器开发者工具的Console查看详细的API调用日志</div>
            <div>所有的请求和响应都会在控制台中显示</div>
          </div>
        </Card>
      </div>
    </div>
  )
}
