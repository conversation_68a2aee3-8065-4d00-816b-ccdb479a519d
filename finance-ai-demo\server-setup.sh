#!/bin/bash

# 服务器环境配置脚本
# 适用于 Ubuntu 20.04+

echo "🚀 开始配置服务器环境..."

# 更新系统
echo "📦 更新系统包..."
sudo apt update && sudo apt upgrade -y

# 安装必要工具
echo "🔧 安装基础工具..."
sudo apt install -y curl wget git vim htop

# 安装Docker
echo "🐳 安装Docker..."
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
echo "🔧 安装Docker Compose..."
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
echo "▶️ 启动Docker服务..."
sudo systemctl start docker
sudo systemctl enable docker

# 配置防火墙
echo "🔥 配置防火墙..."
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 5666  # 应用端口
sudo ufw --force enable

# 创建应用目录
echo "📁 创建应用目录..."
sudo mkdir -p /opt/finance-ai-demo
sudo chown $USER:$USER /opt/finance-ai-demo

echo "✅ 服务器环境配置完成！"
echo "💡 请重新登录以使Docker权限生效"
