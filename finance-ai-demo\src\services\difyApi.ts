// Dify API 集成服务
export interface DifyMessage {
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

export interface DifyResponse {
  message_id: string
  conversation_id: string
  mode: string
  answer: string
  metadata?: {
    usage?: any
    retriever_resources?: any[]
  }
  created_at?: number
}

export interface DifyStreamResponse {
  event: string
  task_id?: string
  message_id?: string
  conversation_id?: string
  answer?: string
  created_at?: number
  metadata?: any
}

export class DifyApiService {
  private apiProxyUrl: string
  private conversationIds: Map<string, string> = new Map() // 按智能体API密钥存储对话ID

  constructor() {
    // 使用API代理路由，避免CORS和密钥暴露问题
    this.apiProxyUrl = '/api/dify'

    console.log('🔧 Dify API服务初始化:', {
      apiProxyUrl: this.apiProxyUrl,
      mode: 'proxy'
    })
  }

  // 获取指定智能体的对话ID
  private getConversationId(apiKey: string): string {
    return this.conversationIds.get(apiKey) || ''
  }

  // 设置指定智能体的对话ID
  private setConversationId(apiKey: string, conversationId: string): void {
    this.conversationIds.set(apiKey, conversationId)
    console.log('💾 保存对话ID:', {
      apiKey: apiKey.substring(0, 10) + '...',
      conversationId: conversationId.substring(0, 10) + '...'
    })
  }

  // 发送消息到Dify
  async sendMessage(message: string, userId: string = 'demo_user', customApiKey?: string): Promise<DifyResponse> {
    // 使用默认API密钥如果没有提供自定义密钥
    const apiKey = customApiKey || 'default'
    const conversationId = this.getConversationId(apiKey)

    console.log('🚀 正在调用Dify API代理...', {
      apiProxyUrl: this.apiProxyUrl,
      message: message.substring(0, 50) + '...',
      conversationId: conversationId || '新对话',
      customApiKey: customApiKey ? customApiKey.substring(0, 10) + '...' : '使用默认密钥'
    })

    // 使用API代理调用
    try {
      const requestBody = {
        endpoint: 'chat-messages',
        inputs: {},
        query: message,
        response_mode: 'blocking',
        conversation_id: conversationId,
        user: userId,
        // 传递自定义API密钥
        customApiKey: customApiKey
      }

      console.log('📤 代理请求体:', {
        ...requestBody,
        customApiKey: customApiKey ? customApiKey.substring(0, 10) + '...' : '默认'
      })

      const response = await fetch(this.apiProxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      console.log('📥 响应状态:', response.status, response.statusText)

      if (response.ok) {
        const data = await response.json()
        console.log('✅ Dify API响应成功:', data)

        // 保存会话ID以便后续使用
        if (data.conversation_id) {
          this.setConversationId(apiKey, data.conversation_id)
        }

        return {
          message_id: data.message_id || `msg_${Date.now()}`,
          conversation_id: data.conversation_id || conversationId || `conv_${Date.now()}`,
          mode: data.mode || 'chat',
          answer: data.answer || '抱歉，我暂时无法回答这个问题。',
          metadata: data.metadata,
          created_at: data.created_at || Date.now()
        }
      } else {
        const errorText = await response.text()
        console.error('❌ Dify API返回错误:', response.status, errorText)
        throw new Error(`Dify API error: ${response.status} - ${errorText}`)
      }
    } catch (error) {
      console.error('❌ Dify API调用失败:', error)
      throw error
    }
  }

  // 流式响应
  async sendMessageStream(
    message: string,
    userId: string = 'demo_user',
    onChunk: (chunk: DifyStreamResponse) => void,
    onComplete: (finalData: DifyResponse) => void,
    customApiKey?: string
  ): Promise<void> {
    // 使用默认API密钥如果没有提供自定义密钥
    const apiKey = customApiKey || 'default'
    const conversationId = this.getConversationId(apiKey)

    console.log('🚀 开始流式调用Dify API代理...', {
      apiProxyUrl: this.apiProxyUrl,
      message: message.substring(0, 50) + '...',
      conversationId: conversationId || '新对话',
      customApiKey: customApiKey ? customApiKey.substring(0, 10) + '...' : '使用默认密钥'
    })

    try {
      const requestBody = {
        endpoint: 'chat-messages',
        inputs: {},
        query: message,
        response_mode: 'streaming',
        conversation_id: conversationId,
        user: 'demo_user',
        // 传递自定义API密钥
        customApiKey: customApiKey
      }

      console.log('📤 流式代理请求体:', {
        ...requestBody,
        customApiKey: customApiKey ? customApiKey.substring(0, 10) + '...' : '默认'
      })

      const response = await fetch(this.apiProxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      })

      console.log('📥 流式响应状态:', response.status, response.statusText)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ Dify流式API错误:', response.status, errorText)
        throw new Error(`Dify streaming API error: ${response.status} - ${errorText}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法读取响应流')
      }

      const decoder = new TextDecoder()
      let buffer = ''
      let finalMessageId = ''
      let finalConversationId = ''

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.trim() === '') continue

          if (line.startsWith('data: ')) {
            try {
              const jsonStr = line.slice(6).trim()
              if (jsonStr === '') continue

              const data = JSON.parse(jsonStr)
              console.log('📨 收到流式数据:', data)

              // 处理不同类型的事件
              switch (data.event) {
                case 'message':
                  // LLM返回文本块
                  if (data.answer) {
                    onChunk({
                      event: 'message',
                      task_id: data.task_id,
                      message_id: data.message_id || data.id,
                      conversation_id: data.conversation_id,
                      answer: data.answer,
                      created_at: data.created_at
                    })
                    finalMessageId = data.message_id || data.id
                    finalConversationId = data.conversation_id
                  }
                  break

                case 'message_end':
                  // 消息结束
                  console.log('✅ 流式响应完成:', data)

                  // 保存会话ID
                  if (data.conversation_id) {
                    this.setConversationId(apiKey, data.conversation_id)
                  }

                  // 调用完成回调
                  onComplete({
                    message_id: data.message_id || finalMessageId,
                    conversation_id: data.conversation_id || finalConversationId,
                    mode: 'chat',
                    answer: '', // 流式响应中answer是分块的，这里为空
                    metadata: data.metadata,
                    created_at: data.created_at
                  })
                  return

                case 'error':
                  // 错误事件
                  console.error('❌ 流式响应错误:', data)
                  throw new Error(`Stream error: ${data.code} - ${data.message}`)

                case 'ping':
                  // 心跳事件，保持连接
                  console.log('💓 收到心跳')
                  break

                default:
                  console.log('📋 其他事件:', data.event, data)
                  break
              }
            } catch (e) {
              console.warn('⚠️ 解析流数据失败:', e, '原始数据:', line)
            }
          }
        }
      }
    } catch (error) {
      console.error('❌ Dify流式API调用失败:', error)
      throw error
    }
  }

  // 获取会话历史
  async getConversationHistory(conversationId: string): Promise<DifyMessage[]> {
    try {
      const response = await fetch(this.apiProxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          endpoint: `conversations/${conversationId}/messages`
        })
      })

      if (!response.ok) {
        throw new Error(`Dify API error: ${response.status}`)
      }

      const data = await response.json()
      return data.data?.map((msg: any) => ({
        role: msg.from_source === 'user' ? 'user' : 'assistant',
        content: msg.query || msg.answer || '',
        timestamp: new Date(msg.created_at)
      })) || []
    } catch (error) {
      console.error('获取会话历史失败:', error)
      return []
    }
  }





  // 重置会话
  resetConversation(apiKey?: string): void {
    if (apiKey) {
      this.conversationIds.delete(apiKey)
      console.log('🗑️ 重置指定智能体的会话:', apiKey.substring(0, 10) + '...')
    } else {
      this.conversationIds.clear()
      console.log('🗑️ 重置所有会话')
    }
  }

  // 获取应用参数
  async getApplicationParameters(userId: string = 'demo_user'): Promise<any> {
    try {
      console.log('🔍 获取应用参数...')

      const response = await fetch(this.apiProxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          endpoint: `parameters?user=${encodeURIComponent(userId)}`
        })
      })

      console.log('📥 参数响应状态:', response.status, response.statusText)

      if (response.ok) {
        const data = await response.json()
        console.log('✅ 应用参数获取成功:', data)
        return data
      } else {
        const errorText = await response.text()
        console.warn('⚠️ 获取应用参数失败:', response.status, errorText)
        throw new Error(`Parameters API error: ${response.status} - ${errorText}`)
      }
    } catch (error) {
      console.error('❌ 获取应用参数失败:', error)
      throw error
    }
  }

  // 测试Dify连接
  async testConnection(): Promise<boolean> {
    try {
      console.log('🔍 测试Dify连接...')

      // 首先尝试获取应用参数（这是一个轻量级的测试）
      try {
        await this.getApplicationParameters('connection_test')
        console.log('✅ Dify API连接成功（通过参数端点验证）')
        return true
      } catch (paramError) {
        console.warn('⚠️ 参数端点测试失败，尝试聊天端点...')

        // 如果参数端点失败，尝试聊天端点
        const response = await fetch(this.apiProxyUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            endpoint: 'chat-messages',
            inputs: {},
            query: '连接测试',
            response_mode: 'blocking',
            conversation_id: '',
            user: 'connection_test'
          })
        })

        if (response.ok) {
          console.log('✅ Dify API连接成功（通过聊天端点验证）')
          return true
        } else {
          console.warn('⚠️ Dify API连接失败:', response.status, response.statusText)
          if (response.status === 401) {
            console.log('🔑 API密钥无效')
          }
          return false
        }
      }
    } catch (error) {
      console.error('❌ Dify连接测试失败:', error)
      return false
    }
  }

  // 获取智能体列表（模拟）
  getAvailableAgents() {
    return [
      {
        id: 'financial-analyst',
        name: '财务分析师',
        description: '专业的财务数据分析和报表解读',
        capabilities: ['财务报表分析', '比率分析', '趋势分析', '预算分析']
      },
      {
        id: 'risk-assessor',
        name: '风险评估师',
        description: '识别和评估各类财务风险',
        capabilities: ['信用风险评估', '市场风险分析', '流动性风险监控', '操作风险识别']
      },
      {
        id: 'investment-advisor',
        name: '投资顾问',
        description: '提供专业的投资建议和资产配置方案',
        capabilities: ['投资组合分析', '资产配置建议', '市场机会识别', '风险收益评估']
      },
      {
        id: 'compliance-checker',
        name: '合规检查员',
        description: '确保财务操作符合法规要求',
        capabilities: ['合规性检查', '政策解读', '风险预警', '审计支持']
      },
      {
        id: 'report-generator',
        name: '报告生成器',
        description: '自动生成各类财务报告和分析文档',
        capabilities: ['财务报告生成', '数据可视化', '趋势图表', '执行摘要']
      }
    ]
  }
}

// 创建单例实例
export const difyApi = new DifyApiService()

// 全局测试函数（用于浏览器控制台调试）
if (typeof window !== 'undefined') {
  (window as any).testDifyApi = async (message: string = '你好') => {
    console.log('🧪 开始测试Dify API...')
    try {
      const result = await difyApi.sendMessage(message)
      console.log('✅ 测试成功:', result)
      return result
    } catch (error) {
      console.error('❌ 测试失败:', error)
      return null
    }
  }

  (window as any).difyApi = difyApi
  console.log('🔧 Dify API测试工具已加载，使用 testDifyApi("你的消息") 进行测试')
}
