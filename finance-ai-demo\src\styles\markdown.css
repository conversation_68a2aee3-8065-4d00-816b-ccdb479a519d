/* Markdown 渲染样式 */
.markdown-content {
  line-height: 1.6;
  color: #1f2937;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #111827;
}

.markdown-content h1 {
  font-size: 1.25rem;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.25rem;
}

.markdown-content h2 {
  font-size: 1.125rem;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.25rem;
}

.markdown-content h3 {
  font-size: 1rem;
}

.markdown-content p {
  margin-bottom: 0.75rem;
  color: #374151;
}

.markdown-content ul,
.markdown-content ol {
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
}

.markdown-content li {
  margin-bottom: 0.25rem;
  color: #374151;
}

.markdown-content ul li {
  list-style-type: disc;
}

.markdown-content ol li {
  list-style-type: decimal;
}

.markdown-content strong {
  font-weight: 600;
  color: #111827;
}

.markdown-content em {
  font-style: italic;
  color: #374151;
}

.markdown-content code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.875rem;
  color: #dc2626;
}

.markdown-content pre {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
  margin-bottom: 0.75rem;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
  color: #1f2937;
  font-size: 0.875rem;
}

.markdown-content blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 1rem;
  margin: 0.75rem 0;
  font-style: italic;
  color: #6b7280;
  background-color: #f8fafc;
  padding: 0.75rem 1rem;
  border-radius: 0.25rem;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  overflow: hidden;
}

.markdown-content th {
  background-color: #f9fafb;
  padding: 0.5rem;
  text-align: left;
  font-weight: 600;
  color: #111827;
  border-bottom: 1px solid #d1d5db;
}

.markdown-content td {
  padding: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
}

.markdown-content tr:last-child td {
  border-bottom: none;
}

.markdown-content hr {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 1rem 0;
}

.markdown-content a {
  color: #3b82f6;
  text-decoration: underline;
}

.markdown-content a:hover {
  color: #1d4ed8;
}

/* 特殊格式支持 */
.markdown-content .emoji {
  font-size: 1.2em;
  vertical-align: middle;
}

/* 代码高亮样式调整 */
.markdown-content .hljs {
  background-color: #f8fafc !important;
  color: #1f2937 !important;
  padding: 1rem !important;
  border-radius: 0.5rem !important;
}

/* Thinking 部分样式 */
.thinking-section {
  background-color: #f8f9fa !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  padding: 12px !important;
  margin: 8px 0 16px 0 !important;
  color: #6b7280 !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
}

.thinking-section summary {
  font-weight: 600 !important;
  color: #4b5563 !important;
  cursor: pointer !important;
  margin-bottom: 8px !important;
  padding: 4px 0 !important;
  border-bottom: 1px solid #e5e7eb !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}

.thinking-section summary:hover {
  color: #374151 !important;
}

.thinking-section[open] summary {
  margin-bottom: 12px !important;
}

.thinking-section div {
  color: #6b7280 !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .markdown-content {
    font-size: 0.875rem;
  }

  .markdown-content h1 {
    font-size: 1.125rem;
  }

  .markdown-content h2 {
    font-size: 1rem;
  }

  .markdown-content table {
    font-size: 0.75rem;
  }

  .thinking-section {
    font-size: 13px !important;
    padding: 10px !important;
  }
}
