吉林市国有资本发展控股集团有限公司（简称“吉资本控股”）的财务智能体平台 Demo 设计方案，兼顾可落地性与展示效果。整体分为三部分：

平台总体架构与技术路线

财务智能体目录（19 个子智能体）

演示脚本与落地建议

1. 平台总体架构与技术路线
层次	作用	关键技术 / 产品	设计要点
数据接入层	统一接入 ERP、OA、资金系统、招投标管理系统、金蝶/用友账套、Excel、PDF、影像票据等	数据网关、API、ETL、ESB、OCR、RPA	• 最少接入一套近两年的“去敏”账套以支撑 Demo
• 采用 CDC（变更数据捕获）保证数据实时
数据治理层	清洗、脱敏、对帐、建数仓 & 向量库	Data Lakehouse + LakeFS + Iceberg、国产数据库（人大金仓、达梦）	• 数仓 Star/Snowflake 模型 + 财务域数据集市
• 构建“财务政策/制度文档”向量库，支持 RAG
模型与服务层	本地部署或私有云 LLM + Tool-Calling	Qwen2-72B/InternLM2-104B、动态路由至 GPT‑4o（无敏感数据时）	• 关键函数（财务计算、预测、优化）用 Python / Java 微服务暴露
• 使用 OpenAI Function Calling 或 Dify Tool
智能体编排层	多智能体协同、对话管理、上下文记忆	Dify Flow / LangGraph / Agentverse	• 用 YAML 定义流程，支持子智能体并行和依赖
安全与合规层	权限、审计、机密计算、国密加密	SSO、Keycloak、SM4、堡垒机、审计日志	• “三员分立”与操作留痕
• 生成内容自动加水印
交互与可视化层	Web + IM 混合界面	Vue3 + Vite + ECharts / Ant Design Vue	• 统一入口“财务智能体工作台”，含聊天窗口、看板、待办

Demo 目标：在一台 GPU 服务器 + 一台数据库服务器即可跑通；全程使用集团自有或“脱敏后伪造”数据，保证保密。

2. 财务智能体目录（19 个子智能体）
#	名称	主要职责 / 展示亮点	关键能力 (Tool)	典型指令示例
A. 经营分析与预测类				
1	财务报表洞察 Agent	一键生成月/季/年报摘要、同比环比、图表	SQL+LLM、ECharts渲染	“用一句话总结 2024Q4 资产负债表异动”
2	智能预算编制 Agent	读取历史数据 + LLM 预测 + 线性规划平衡	Prophet / sk‑forecast	“生成 2025 年经营预算草案”
3	现金流预测与调度 Agent	13 周滚动现金流、资金池净头寸建议	资金系统 API、优化器	“若本周应付推迟 7 天，现金缺口多少？”
4	项目投后管理 Agent	监控 200+ 子公司 / 项目 IRR、ROE	BI + RAG	“列出 IRR 低于 8% 的项目并给改善建议”
5	投资组合优化 Agent	对接券商 PB/银票/理财，Mark‑to‑Market	量化引擎、布朗运动模拟	“帮我配置一份低风险票据组合，目标年化 3.5%”
B. 成本与运营管控类				
6	成本算法 Copilot	产品/工程成本分摊、敏感性分析	Excel Parser + Python	“钢材上涨 5% 对单项工程毛利影响多少？”
7	费用报销审核 Agent	OCR + 规则 + LLM 语义一致性检查	OCR、RPA、异常检测	“扫描这 20 张发票，看是否合规”
8	供应链金融 Agent	足额质押、票据贴现机会识别	票据库 + LLM	“当前可贴现的电子银票有哪些？”
9	招投标财务评分 Agent	解析标书，自动填财务评分表	PDF Parser、模板匹配	“为 XX 项目生成财务打分意见”
C. 风险、合规与内控类				
10	财务风险雷达 Agent	监控债务到期、流动比率、AAA 评级维护	KPI 阈值报警	“有哪些即将触红线的指标？”
11	税务筹划与申报 Agent	预估税负、生成纳税申报 XML	税局 API 沙箱	“提示可用的研发加计扣除金额”
12	国资委报表助手	自动生成《国有资本经营预算执行报表》	模板 / Excel	“生成 2024H1 国资统计表并解释差异”
13	IFRS/新会计准则转换 Agent	中准 GAAP ↔ IFRS 调整分录、差异说明	Ruleset + LLM	“把这份合并报表转换为 IFRS”
14	ESG 财务披露 Agent	采集能耗、排污费数据→生成 ESG 报告	ESG 数据平台 API	“列出 Scope 2 排放成本并预测 3 年趋势”
15	内部审计 Copilot	随机抽样 + 异常交易聚类	随机森林	“找出与供应商 AB 输送利益的可疑支付”
D. 智能问答与助手类				
16	财务制度知识库问答 Agent	RAG 检索会计制度、集团红头文件	向量库 + 文档 OCR	“固定资产折旧年限的最新规定是？”
17	财务 Chat‑Ops Agent	Slack/企微内嵌，解析指令触发以上子 Agent	Webhook + Dify	“/fcf 现金流预测 8 周”
18	会议纪要 & 任务 Agent	语音转写 + To‑Do 出具责任人	Whisper 模型	“总结今天财务例会并分配任务”
19	知识蒸馏与训练 Agent	将高频 Q&A 转成微课，供新人学习	PPT 生成、视频合成	“把近三月报销问题整理成 10 分钟培训课”

覆盖面说明

经营分析：1–5

成本/运营：6–9

风险/合规：10–15

知识协同：16–19

如需“炫技”效果，可重点演示 Tool‑Calling + RAG + 多智能体协同：例如 CFO 在 Chat‑Ops 输入一句话→先调用 2（预算）→再调用 3（现金流）→最后调用 10（风险）→综合回答，并自动生成 PPT。

3. 演示脚本与落地建议
步骤	角色 & 场景	演示内容	亮点
1	财务经理登录工作台	主页左侧列出 19 个智能体；右侧为 KPI 看板	单点登录、RBAC 权限
2	语音输入：
“请在一页里总结 2024Q1 的经营状况，并预测 2024Q2 现金流”	Chat‑Ops 调度 1 → 3 → 5	串联多智能体、图表自动插入
3	点击 KPI 报警“流动比率跌破 1.2”	调用 10 自动生成原因分析	实时阈值监控
4	上传 50 张报销凭证 ZIP	7 完成 OCR + 语义对账 + 制单	发票真伪检测、RPA
5	财务总监说：“把这些内容做成 PPT，今晚董事会用”	1 + 2 + 10 结果 → 19 制作并邮件发送	自动文档生成

Demo 数据准备
数据	来源	建议处理
2023–2024 总账 / 凭证	金蝶 K/3 Cloud	随机打乱供应商、金额脱敏
预算–实际对照	Excel	手工插入异常值以便演示
发票影像	随机下载 + 修改	保留条码，用 OCR 识别

部署要点
模型选择：在演示环境先用中等规模本地 LLM（Qwen2-7B）；复杂场景路由 GPT‑4o，注意不上传敏感字段。

Tool‑Calling：用 OpenAI function 调用内部 Python 微服务，如 forecast_cashflow(), generate_ifrs_adjustment().

向量库：Milvus/Weaviate + OSS，存储制度文件、招投标 PDF。

安全：数据库与模型服务器隔离；实时审计；敏感词过滤。

结语
通过上述架构和 19 个财务智能体，Demo 可充分展示 “国有资本投资运营平台 + 先进 LLM 技术” 的结合价值：

降低财务人日常报表、审核、合规工作量 > 50 %

现金与成本风险提前预警，保障 AAA 信用评级

提升决策速度：从“数据→洞察→行动”闭环压缩至分钟级

若需要进一步深挖某一子智能体的算法实现或 UI 原型设计，请随时告知！







您说：
我想让这个demo充满创新型，让客户眼前一亮，能帮我具体设计一下功能和页面么


ChatGPT 说：
请求 o3 pro 模式
Reasoned for 3m 4s
下面是一份 “眼前一亮” 级别的 Demo 体验设计稿，包含整体信息架构、6 个核心页面蓝图、两段“WOW 场景”交互剧本，以及可快速落地的技术栈建议。既给出创意，也兼顾可实现性——拿去做 Figma / Vite 项目都会很顺。

0. 设计基调
Narrative‑Driven：让财务数据“会讲故事”，而不是堆报表

Multimodal Interaction：文字 + 语音 + 拖拽文件 + 即时图表

Agent‑as‑Component：每个智能体像可插拔的 UI Widget

可炫可收：演示时有酷炫动效，交付时可一键关闭

1. 信息架构 & 关键页面
mathematica
复制
┌───────┐
│ Login │（粒子流动背景 + 实时问候）
└──┬────┘
   │
   ▼
┌────────────────── Intelligent Finance Workbench ──────────────────┐
│ ① Executive Dashboard │ ② Finance Copilot Chat │ ③ Insight Studio │
│ ④ Document Lab       │ ⑤ Simulation Playground │ ⑥ Boardroom Mode │
└────────────────────────────────────────────────────────────────────┘
六大页面即六种演示视角，不必全做完；完成 ①②⑥ 和任意一个中台页即可惊艳客户。

2. 页面蓝图（逐页拆解）
① Executive Dashboard
目的：30 秒内让 CFO 抓住风险 & 机会

Hero 区：动态 3D “资金池液位”——WebGL 水面随实时净现金流起伏

KPI 卡片：流动比率、资产负债率等 6 个核心指标，绿→黄→红渐变

AI Narrative Bar：一行滚动文字，LLM 每小时自动写一句“今日看点”

Alert Center：卡片堆叠动画，左滑消警；点击跳 10 秒即生成原因分析

微交互：鼠标悬停任一 KPI → 侧边浮出 mini 趋势火花线 (sparkline)

② Finance Copilot Chat
目的：所有智能体的统一入口，Slack 风格

多模输入：文字、语音、拖拽 Excel/PDF 区域

函数标注：LLM auto‑detect 意图 → 显示将要调用的子 Agent Tag（例：#CashFlowPredictor），用户可点叉禁用

右侧洞察 Pane：一次对话拉出动态组件：图表、表格、Markdown、PPT 缩略图

Inline 编辑：图表即点即改维度；改完自动反向写入缓存数据帧

Memory Chip：底部显示“已记住：××预算假设”——随时清空

③ Insight Studio (Story Canvas)
目的：把复杂分析做成可拖拽的“故事时间轴”

白板画布 + 左侧“Insight Block”库（盈亏折线、现金瀑布、敏感性热力等）

拖一个 Block 上来 → 自动弹 Prompt 让 LLM 拉数据 & 生成视觉稿

Block 之间可连线（因果/对比）产生 Flow 动效

一键 “Narrate”：TTS 朗读 + 页面自动切镜头 → 类 TED 片段

导出选项：PPT、短视频、长图

④ Document Lab
目的：OCR + 语义审核 + 分录生成，一切用 Drag‑n‑Drop

Drop Zone：支持 ZIP/图片/PDF，一丢即弹“进度水滴”百分比

票据墙：瀑布流缩略图，异常票据自动抖动

审计轨迹：侧栏实时列出每步调用的规则 / LLM explanation

复核模式：点异常→弹双栏 diff（票面 vs ERP 记录）→ 一键通过/驳回

⑤ Simulation Playground (Digital Twin)
目的：让客户“玩”数据，感受 AI 预测与优化

Parameter Pad：利率、钢材价、完工进度滑块，带弹簧效果

3D Sankey / Energy Ribbon：展示资金流向；参数一动即流线颜色&流量变

优化按钮：“平滑现金缺口”→ LLM＋求解器计算 → 动画回放调整结果

快照功能：保存任意场景 → 生成可分享 URL（仅本地存储演示）

⑥ Boardroom Mode
目的：10 秒生成董事会可直接播放的汇报

深色全屏、自动排练式分页，顶部时间轴点亮进度

左下角实时字幕（LLM 同步生成）

支持 激光笔：鼠标即激光，高亮元素放大 1.1×

“提问”按钮：与 ② Chat 面板无缝切换，台上问一句，后台即时给答案并推送到当前幻灯片脚注

3. 两段 “WOW 场景” 演示剧本
场景 A：CFO 即席提问 → 即席决策（Dashboard → Chat → Boardroom）
Dashboard 红色 Alert：“6 周后现金池下穿警戒线”

CFO 点 Alert，一键 “Explain”

Chat 面板弹出，从资金预测 Agent调用 3 个版本场景：常规 / 延付供应商 / 加速回款

CFO 语音：“如果推迟偿债 1 个月呢？”（中文）

LLM 即时算新曲线，右侧 Pane 三连图滚入

CFO 点击 “Add to Deck”

切到 Boardroom Mode：新的一页自动加入并排好布局——全场“哇”！

场景 B：发票拉群 → 合规风控（Document Lab → ChatOps）
财务同事拖 50 张发票进 Document Lab

OCR 进度水滴走完，3 张票抖动变红

聊天窗口弹出系统推送：“发现供应商 A、B 疑似虚开发票，总额 87 万”

点击“生成整改单”→ 内控 Agent 调用模板，填好字段，发至钉钉群 @相关人

群里直接 /audit follow-up，ChatOps 自动创建 Jira 任务并倒计时

4. 技术与实现要点（只列创新相关，详细可扩展）
WebGL + Tailwind Motion：Hero 动效、3D Sankey

LLM Router：短 Prompt 走 Qwen2‑7B，长文对话切 GPT‑4o；走向量库时使用 Milvus Hybrid Search

二阶段 Tool‑Calling：① 判断需求 → ② 传给 Airflow‑托管的 Python FN（保底兜错）

前端微组件化：每个智能体提供 InsightCard.vue，主桌面用动态 import 聚合

Edge TTS：Boardroom Narrate 功能离线缓存 mp3，低网速也不卡

效果开关：URL 加 ?noFx=true 关闭全部高耗动画，方便严肃场合

5. 快速落地节奏
T‑0	日志驱动数据网关接好 3 张核心表：GL、现金、应收
T‑3d	搭好 ② Copilot Chat + 1 个子 Agent（报表洞察）
T‑7d	补 ① Dashboard、⑥ Boardroom；演示线路打通
T‑14d	③ Story Canvas MVP 与 ④ OCR 审核上线
★	双机演示：MacBook 展示炫酷前端；服务器本地跑 LLM，全程离线

结语
聚焦 即时洞察 + 互动故事化 + 数字孪生模拟 三大创新点，这套 Demo 让传统财务报表摇身变成“可以玩的电影”。