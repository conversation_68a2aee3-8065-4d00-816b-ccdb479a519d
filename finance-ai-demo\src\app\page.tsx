'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Button, Input, Card } from 'antd'
import { UserOutlined, LockOutlined, RocketOutlined } from '@ant-design/icons'
import { useRouter } from 'next/navigation'

export default function LoginPage() {
  const [loading, setLoading] = useState(false)
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [particles, setParticles] = useState<Array<{id: number, x: number, y: number, size: number, speed: number}>>([])
  const [currentTime, setCurrentTime] = useState(new Date())
  const router = useRouter()

  // 生成简约粒子效果
  useEffect(() => {
    const newParticles = Array.from({ length: 30 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 3 + 1,
      speed: Math.random() * 1 + 0.3
    }))
    setParticles(newParticles)

    // 实时时间更新
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => {
      clearInterval(timeInterval)
    }
  }, [])

  const handleLogin = async () => {
    setLoading(true)
    await new Promise(resolve => setTimeout(resolve, 1500))

    localStorage.setItem('finance_ai_token', 'demo_token')
    localStorage.setItem('finance_ai_user', JSON.stringify({
      username: username || 'demo_user',
      role: 'admin'
    }))

    router.push('/dashboard')
  }

  const quickLogin = (role: string) => {
    setUsername(role)
    setPassword('123456')
    setTimeout(handleLogin, 500)
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* 背景图片 */}
      {/* 背景图片 - 虚化灰化效果 */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: 'url(/back.jpg)',
          filter: 'blur(2px) grayscale(30%) brightness(0.7)',
        }}
      />

      {/* 额外的灰化遮罩 */}
      <div className="absolute inset-0 bg-slate-900/20"></div>
      {/* 动态粒子背景 */}
      <div className="absolute inset-0">
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            className="absolute rounded-full"
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              width: `${particle.size}px`,
              height: `${particle.size}px`,
              background: `rgba(148, 163, 184, ${0.2 - particle.size * 0.03})`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.1, 0.3, 0.1],
            }}
            transition={{
              duration: 6 + particle.speed,
              repeat: Infinity,
              ease: "easeInOut",
              delay: particle.id * 0.2
            }}
          />
        ))}
      </div>

      {/* 科技感网格背景 */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48cGF0dGVybiBpZD0iZ3JpZCIgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIj48cGF0aCBkPSJNIDQwIDAgTCAwIDAgMCA0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJyZ2JhKDE0OCwgMTYzLCAxODQsIDAuMDgpIiBzdHJva2Utd2lkdGg9IjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48L3N2Zz4=')] opacity-30"></div>

      {/* 动态扫描线 */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        style={{
          background: 'linear-gradient(90deg, transparent 0%, rgba(148, 163, 184, 0.1) 50%, transparent 100%)',
          width: '1px',
        }}
        animate={{
          x: ['-50px', 'calc(100vw + 50px)'],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: "linear",
        }}
      />

      {/* 边缘光晕 */}
      <div className="absolute inset-0 bg-gradient-to-r from-slate-600/5 via-transparent to-slate-600/5"></div>
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-slate-500/3 to-transparent"></div>

      {/* 角落装饰线 */}
      <div className="absolute top-0 left-0 w-32 h-32">
        <div className="absolute top-4 left-4 w-16 h-0.5 bg-gradient-to-r from-slate-400/50 to-transparent"></div>
        <div className="absolute top-4 left-4 w-0.5 h-16 bg-gradient-to-b from-slate-400/50 to-transparent"></div>
      </div>
      <div className="absolute top-0 right-0 w-32 h-32">
        <div className="absolute top-4 right-4 w-16 h-0.5 bg-gradient-to-l from-slate-400/50 to-transparent"></div>
        <div className="absolute top-4 right-4 w-0.5 h-16 bg-gradient-to-b from-slate-400/50 to-transparent"></div>
      </div>
      <div className="absolute bottom-0 left-0 w-32 h-32">
        <div className="absolute bottom-4 left-4 w-16 h-0.5 bg-gradient-to-r from-slate-400/50 to-transparent"></div>
        <div className="absolute bottom-4 left-4 w-0.5 h-16 bg-gradient-to-t from-slate-400/50 to-transparent"></div>
      </div>
      <div className="absolute bottom-0 right-0 w-32 h-32">
        <div className="absolute bottom-4 right-4 w-16 h-0.5 bg-gradient-to-l from-slate-400/50 to-transparent"></div>
        <div className="absolute bottom-4 right-4 w-0.5 h-16 bg-gradient-to-t from-slate-400/50 to-transparent"></div>
      </div>

      {/* 主要内容 */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="w-full max-w-md relative"
        >
          {/* 内容区域背景遮罩 */}
          <div className="absolute inset-0 -m-16 rounded-3xl bg-black/40 backdrop-blur-sm"></div>
          <div className="absolute inset-0 -m-16 rounded-3xl bg-gradient-to-br from-slate-900/60 via-slate-800/50 to-slate-900/60"></div>
          {/* 渐变边缘 */}
          <div className="absolute inset-0 -m-20 rounded-3xl bg-gradient-to-r from-transparent via-black/20 to-transparent"></div>

          {/* 内容容器 */}
          <div className="relative z-10">
          {/* Logo和标题 */}
          <motion.div
            className="text-center mb-8"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            {/* 商务Logo */}
            <motion.div
              className="relative inline-flex items-center justify-center w-24 h-24 mb-6"
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.6 }}
            >
              {/* 外圈动态边框 */}
              <motion.div
                className="absolute inset-0 rounded-full border border-slate-400/20"
                animate={{ rotate: 360 }}
                transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
              />

              {/* 内圈静态边框 */}
              <div className="absolute inset-2 rounded-full border border-slate-400/30"></div>

              {/* 中心Logo */}
              <div className="relative w-16 h-16 bg-gradient-to-br from-slate-600 to-slate-700 rounded-full shadow-xl flex items-center justify-center">
                <RocketOutlined className="text-xl text-white" />

                {/* 微妙的脉冲效果 */}
                <motion.div
                  className="absolute inset-0 rounded-full bg-slate-500/20"
                  animate={{ scale: [1, 1.1, 1], opacity: [0.3, 0.1, 0.3] }}
                  transition={{ duration: 3, repeat: Infinity }}
                />
              </div>
            </motion.div>

            {/* 标题文字 */}
            <motion.h1
              className="text-4xl font-bold mb-4 text-white"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
            >
              财务智能体平台
            </motion.h1>

            {/* 副标题 */}
            <motion.p
              className="text-blue-200 text-lg mb-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.6 }}
            >
              吉发集团有限公司
            </motion.p>

            {/* 实时时间显示 */}
            <motion.div
              className="mt-4 text-sm text-gray-300 font-mono bg-black/20 px-4 py-2 rounded border border-slate-500/30"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8, duration: 0.6 }}
            >
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>系统时间: {currentTime.toLocaleString('zh-CN')}</span>
              </div>
            </motion.div>
          </motion.div>

          {/* 登录表单 */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2, duration: 0.8 }}
          >
            <div className="relative">
              {/* 表单装饰角 */}
              <div className="absolute -top-2 -left-2 w-4 h-4">
                <div className="absolute top-0 left-0 w-3 h-0.5 bg-slate-400/50"></div>
                <div className="absolute top-0 left-0 w-0.5 h-3 bg-slate-400/50"></div>
              </div>
              <div className="absolute -top-2 -right-2 w-4 h-4">
                <div className="absolute top-0 right-0 w-3 h-0.5 bg-slate-400/50"></div>
                <div className="absolute top-0 right-0 w-0.5 h-3 bg-slate-400/50"></div>
              </div>
              <div className="absolute -bottom-2 -left-2 w-4 h-4">
                <div className="absolute bottom-0 left-0 w-3 h-0.5 bg-slate-400/50"></div>
                <div className="absolute bottom-0 left-0 w-0.5 h-3 bg-slate-400/50"></div>
              </div>
              <div className="absolute -bottom-2 -right-2 w-4 h-4">
                <div className="absolute bottom-0 right-0 w-3 h-0.5 bg-slate-400/50"></div>
                <div className="absolute bottom-0 right-0 w-0.5 h-3 bg-slate-400/50"></div>
              </div>

              <Card
                className="relative backdrop-blur-xl border shadow-xl overflow-hidden"
                style={{
                  background: 'rgba(15, 23, 42, 0.9)',
                  backdropFilter: 'blur(20px)',
                  borderColor: 'rgba(148, 163, 184, 0.3)'
                }}
              >

                <div className="relative space-y-6 p-2">
                  {/* 表单标题 */}
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-white mb-2">系统登录</h3>
                    <div className="w-16 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400 mx-auto"></div>
                  </div>

                  <div>
                    <label className="block text-white font-medium mb-3 text-sm">
                      <UserOutlined className="mr-2 text-blue-400" />
                      用户名
                    </label>
                    <div className="relative">
                      <Input
                        size="large"
                        placeholder="请输入用户名"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        className="bg-black/30 border-blue-500/30 hover:border-blue-400 focus:border-blue-400"
                        style={{
                          backgroundColor: 'rgba(0, 0, 0, 0.3)',
                          borderColor: 'rgba(59, 130, 246, 0.3)',
                          color: 'white',
                          boxShadow: '0 0 10px rgba(59, 130, 246, 0.1)'
                        }}
                      />
                      {/* 输入框光效 */}
                      <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-blue-500/20 to-purple-500/20 opacity-0 hover:opacity-100 transition-opacity pointer-events-none"></div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-white font-medium mb-3 text-sm">
                      <LockOutlined className="mr-2 text-blue-400" />
                      密码
                    </label>
                    <div className="relative">
                      <Input.Password
                        size="large"
                        placeholder="请输入密码"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="bg-black/30 border-blue-500/30 hover:border-blue-400 focus:border-blue-400"
                        style={{
                          backgroundColor: 'rgba(0, 0, 0, 0.3)',
                          borderColor: 'rgba(59, 130, 246, 0.3)',
                          color: 'white',
                          boxShadow: '0 0 10px rgba(59, 130, 246, 0.1)'
                        }}
                      />
                      {/* 输入框光效 */}
                      <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-blue-500/20 to-purple-500/20 opacity-0 hover:opacity-100 transition-opacity pointer-events-none"></div>
                    </div>
                  </div>

                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      type="primary"
                      size="large"
                      loading={loading}
                      onClick={handleLogin}
                      className="w-full h-12 border-none text-lg font-medium"
                      style={{
                        background: 'linear-gradient(135deg, #475569, #64748b)',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)'
                      }}
                    >
                      {loading ? '正在验证身份...' : '登录系统'}
                    </Button>
                  </motion.div>
                </div>
              </Card>
            </div>
          </motion.div>

          {/* 快速登录选项 */}
          <motion.div
            className="mt-8 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.4, duration: 0.8 }}
          >
            <div className="text-sm text-white mb-6 flex items-center justify-center gap-2">
              <div className="w-8 h-0.5 bg-gradient-to-r from-transparent to-blue-400"></div>
              <span>⚡ 演示账户 - 一键登录</span>
              <div className="w-8 h-0.5 bg-gradient-to-l from-transparent to-blue-400"></div>
            </div>

            <div className="grid grid-cols-3 gap-3">
              {[
                {
                  role: 'admin',
                  label: '管理员',
                  icon: '�',
                },
                {
                  role: 'finance',
                  label: '财务经理',
                  icon: '💼',
                },
                {
                  role: 'cfo',
                  label: '财务总监',
                  icon: '📊',
                }
              ].map((item, index) => (
                <motion.div
                  key={item.role}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.6 + index * 0.1, duration: 0.6 }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={() => quickLogin(item.role)}
                    className="w-full border text-white font-medium hover:bg-slate-600 transition-colors"
                    style={{
                      background: 'linear-gradient(135deg, #475569, #64748b)',
                      borderColor: 'rgba(148, 163, 184, 0.3)',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
                      height: '72px',
                      minHeight: '72px'
                    }}
                  >
                    <div className="flex flex-col items-center justify-center gap-2 py-2">
                      <span className="text-xl leading-none">{item.icon}</span>
                      <span className="text-sm leading-tight">{item.label}</span>
                    </div>
                  </Button>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* 底部信息 */}
          <motion.div
            className="text-center mt-12 text-white text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2, duration: 1 }}
          >
            {/* 版权信息 */}
            <p className="mb-2 text-gray-400">
              © 2025 吉发集团 · 财务智能体平台演示系统
            </p>

            {/* 技术信息 */}
            <div className="text-xs text-gray-500">
              Powered by React & Next.js
            </div>
          </motion.div>

          </div> {/* 关闭内容容器 */}
        </motion.div>
      </div>
    </div>
  )
}