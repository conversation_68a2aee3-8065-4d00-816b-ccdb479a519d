'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, Badge } from 'antd'
import { EnvironmentOutlined } from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'

// 吉发集团固定资产数据 - 使用真实经纬度
const assetData = [
  {
    id: 1,
    name: '吉发集团总部大厦',
    type: 'office',
    coord: [126.5504, 43.8436], // 吉林市船营区
    value: 28000,
    area: 15000,
    status: 'active',
    description: '集团总部办公楼，地上32层',
    color: '#3b82f6',
    address: '吉林市船营区解放大路1号'
  },
  {
    id: 2,
    name: '吉林化工园区厂房',
    type: 'factory',
    coord: [126.6200, 43.8800], // 龙潭区
    value: 45000,
    area: 28000,
    status: 'active',
    description: '化工生产基地，占地280亩',
    color: '#10b981',
    address: '吉林市龙潭区化工园区'
  },
  {
    id: 3,
    name: '松花江物流中心',
    type: 'warehouse',
    coord: [126.5200, 43.8200], // 丰满区
    value: 18000,
    area: 12000,
    status: 'active',
    description: '现代化物流仓储中心',
    color: '#f59e0b',
    address: '吉林市丰满区松花江大街'
  },
  {
    id: 4,
    name: '高新区研发中心',
    type: 'research',
    coord: [126.5800, 43.8600], // 高新区
    value: 22000,
    area: 8000,
    status: 'active',
    description: '技术研发与创新中心',
    color: '#8b5cf6',
    address: '吉林市高新技术开发区'
  },
  {
    id: 5,
    name: '龙潭区商业综合体',
    type: 'commercial',
    coord: [126.6100, 43.8700], // 龙潭区
    value: 35000,
    area: 20000,
    status: 'construction',
    description: '在建商业地产项目',
    color: '#ef4444',
    address: '吉林市龙潭区遵义东路'
  },
  {
    id: 6,
    name: '昌邑区住宅项目',
    type: 'residential',
    coord: [126.5300, 43.8500], // 昌邑区
    value: 42000,
    area: 35000,
    status: 'planning',
    description: '规划中的住宅开发项目',
    color: '#06b6d4',
    address: '吉林市昌邑区民主路'
  }
]

// 主地图组件
export default function JilinMapReal() {
  const [selectedAsset, setSelectedAsset] = useState<any>(null)
  const [totalValue, setTotalValue] = useState(0)
  const [mapReady, setMapReady] = useState(false)

  useEffect(() => {
    const total = assetData.reduce((sum, asset) => sum + asset.value, 0)
    setTotalValue(total)
  }, [])

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '运营中'
      case 'construction': return '建设中'
      case 'planning': return '规划中'
      default: return '未知'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success'
      case 'construction': return 'warning'
      case 'planning': return 'default'
      default: return 'default'
    }
  }

  // 使用散点图配置（不依赖地图）
  const scatterOption = {
    title: {
      text: '吉发集团固定资产分布图',
      left: 'center',
      textStyle: {
        color: '#334155',
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        const asset = assetData.find(item => item.name === params.data.name)
        if (asset) {
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${asset.name}</div>
              <div>资产价值: ${asset.value.toLocaleString()}万元</div>
              <div>占地面积: ${asset.area.toLocaleString()}㎡</div>
              <div>状态: ${getStatusText(asset.status)}</div>
              <div style="color: #666; font-size: 12px; margin-top: 4px;">${asset.address}</div>
            </div>
          `
        }
        return params.name
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e2e8f0',
      textStyle: { color: '#334155' }
    },
    grid: {
      left: '10%',
      right: '10%',
      top: '15%',
      bottom: '10%'
    },
    xAxis: {
      type: 'value',
      name: '经度',
      min: 126.45,
      max: 126.65,
      axisLabel: {
        formatter: '{value}°'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#e2e8f0',
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '纬度',
      min: 43.80,
      max: 43.90,
      axisLabel: {
        formatter: '{value}°'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#e2e8f0',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        type: 'scatter',
        data: assetData.map(asset => ({
          name: asset.name,
          value: asset.coord,
          symbol: 'pin',
          symbolSize: 40,
          itemStyle: {
            color: asset.color,
            borderColor: '#fff',
            borderWidth: 2,
            shadowBlur: 5,
            shadowColor: asset.color,
            shadowOffsetY: 2
          },
          label: {
            show: true,
            position: 'top',
            formatter: '{b}',
            fontSize: 11,
            color: '#334155',
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            padding: [4, 6],
            borderRadius: 4,
            borderColor: asset.color,
            borderWidth: 1
          },
          emphasis: {
            scale: 1.3,
            itemStyle: {
              shadowBlur: 15,
              shadowColor: asset.color
            }
          }
        })),
        animationDuration: 2000,
        animationEasing: 'cubicOut',
        animationDelay: function(idx: number) {
          return idx * 200
        }
      }
    ],
    graphic: [
      {
        type: 'text',
        left: 'center',
        bottom: '5%',
        style: {
          text: '吉林市 (126.55°E, 43.84°N)',
          fontSize: 12,
          fill: '#64748b'
        }
      }
    ]
  }

  // 组件挂载后设置准备状态
  useEffect(() => {
    setMapReady(true)
  }, [])

  return (
    <div className="w-full h-full relative">
      {/* ECharts地图 */}
      <div className="w-full h-96 bg-white rounded-lg overflow-hidden">
        {mapReady ? (
          <ReactECharts
            option={scatterOption}
            style={{ height: '100%', width: '100%' }}
            onEvents={{
              click: (params: any) => {
                if (params.seriesType === 'scatter') {
                  const asset = assetData.find(item => item.name === params.data.name)
                  if (asset) {
                    setSelectedAsset(asset)
                  }
                }
              }
            }}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-slate-50">
            <div className="text-center">
              <div className="text-slate-500 mb-2">正在加载吉林市地图...</div>
              <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
            </div>
          </div>
        )}
      </div>

      {/* 信息面板 */}
      <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* 总览信息 */}
        <Card className="shadow-lg">
          <div className="text-center">
            <h3 className="text-lg font-bold text-slate-800 mb-2">固定资产总览</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-2xl font-bold text-blue-600">{assetData.length}</div>
                <div className="text-sm text-slate-500">资产项目</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">{totalValue.toLocaleString()}</div>
                <div className="text-sm text-slate-500">总价值(万元)</div>
              </div>
            </div>
          </div>
        </Card>

        {/* 选中资产详情 */}
        <AnimatePresence>
          {selectedAsset && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="shadow-lg border-l-4" style={{ borderLeftColor: selectedAsset.color }}>
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-lg font-bold text-slate-800">{selectedAsset.name}</h3>
                  <Badge 
                    status={getStatusColor(selectedAsset.status) as any}
                    text={getStatusText(selectedAsset.status)}
                  />
                </div>
                <p className="text-sm text-slate-600 mb-3">{selectedAsset.description}</p>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <span className="text-slate-500">资产价值:</span>
                    <div className="font-bold text-blue-600">{selectedAsset.value.toLocaleString()}万元</div>
                  </div>
                  <div>
                    <span className="text-slate-500">占地面积:</span>
                    <div className="font-bold text-green-600">{selectedAsset.area.toLocaleString()}㎡</div>
                  </div>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 操作提示 */}
      <div className="mt-4 text-center text-sm text-slate-500">
        <EnvironmentOutlined className="mr-1" />
        点击资产标记查看详情 | 鼠标拖拽移动地图 | 滚轮缩放
      </div>
    </div>
  )
}
