// 智能体数据恢复工具
export interface AgentData {
  id: string
  name: string
  description: string
  department: string
  dataScope: string
  permissionLimit: string
  apiKey: string
  enabled: boolean
  createTime: string
  updateTime: string
}

// 检查所有localStorage键，寻找智能体数据
export const checkAllStorageKeys = (): Record<string, any> => {
  const allData: Record<string, any> = {}
  
  console.log('🔍 开始检查localStorage中的所有数据...')
  
  // 检查所有可能的localStorage键
  const possibleKeys = [
    'agent-storage',
    'finance_ai_agents', 
    'agents',
    'agentStore',
    'finance-agents',
    'dify-agents',
    'ai-agents'
  ]
  
  possibleKeys.forEach(key => {
    const data = localStorage.getItem(key)
    if (data) {
      try {
        allData[key] = JSON.parse(data)
        console.log(`✅ 找到数据键: ${key}`, allData[key])
      } catch (error) {
        allData[key] = data
        console.log(`📝 找到文本数据键: ${key}`, data.substring(0, 100))
      }
    }
  })
  
  // 检查所有localStorage键，寻找包含智能体相关信息的数据
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key && !possibleKeys.includes(key)) {
      const data = localStorage.getItem(key)
      if (data && (data.includes('agent') || data.includes('智能体') || data.includes('dify') || data.includes('财务'))) {
        try {
          allData[key] = JSON.parse(data)
          console.log(`🔍 发现相关数据键: ${key}`, allData[key])
        } catch (error) {
          allData[key] = data
          console.log(`🔍 发现相关文本键: ${key}`, data.substring(0, 100))
        }
      }
    }
  }
  
  return allData
}

// 恢复您之前录入的智能体数据
export const recoverUserAgentData = (): AgentData[] => {
  console.log('🔄 开始搜索您之前录入的智能体数据...')
  
  // 检查所有localStorage数据
  const allStorageData = checkAllStorageKeys()
  
  // 尝试从不同的存储键中恢复数据
  let recoveredAgents: AgentData[] = []
  
  // 检查主要的存储键
  if (allStorageData['agent-storage']?.state?.agents) {
    recoveredAgents = allStorageData['agent-storage'].state.agents
    console.log('✅ 从 agent-storage 恢复了', recoveredAgents.length, '个智能体')
  } else if (allStorageData['finance_ai_agents']) {
    recoveredAgents = Array.isArray(allStorageData['finance_ai_agents']) 
      ? allStorageData['finance_ai_agents'] 
      : []
    console.log('✅ 从 finance_ai_agents 恢复了', recoveredAgents.length, '个智能体')
  }
  
  // 如果没有找到数据，检查其他可能的键
  if (recoveredAgents.length === 0) {
    Object.keys(allStorageData).forEach(key => {
      const data = allStorageData[key]
      if (Array.isArray(data) && data.length > 0 && data[0]?.name && data[0]?.apiKey) {
        recoveredAgents = data
        console.log(`✅ 从 ${key} 恢复了`, recoveredAgents.length, '个智能体')
      } else if (data?.agents && Array.isArray(data.agents)) {
        recoveredAgents = data.agents
        console.log(`✅ 从 ${key}.agents 恢复了`, recoveredAgents.length, '个智能体')
      }
    })
  }
  
  return recoveredAgents
}

// 强制保存智能体数据到localStorage
export const forceStoreAgentData = (agents: AgentData[]): void => {
  const storeData = {
    state: {
      agents: agents,
      currentAgent: agents.find(a => a.enabled) || null
    },
    version: 0
  }
  
  localStorage.setItem('agent-storage', JSON.stringify(storeData))
  console.log('💾 强制保存了', agents.length, '个智能体到localStorage')
}

// 显示恢复报告
export const showRecoveryReport = (): { found: boolean; count: number; agents: AgentData[] } => {
  console.log('📊 智能体数据恢复报告')
  console.log('========================')
  
  const allData = checkAllStorageKeys()
  console.log(`localStorage键数量: ${Object.keys(allData).length}`)
  
  const recoveredAgents = recoverUserAgentData()
  console.log(`可恢复的智能体数量: ${recoveredAgents.length}`)
  
  if (recoveredAgents.length > 0) {
    console.log('可恢复的智能体列表:')
    recoveredAgents.forEach((agent, index) => {
      console.log(`  ${index + 1}. ${agent.name} (${agent.department})`)
    })
  }
  
  return {
    found: recoveredAgents.length > 0,
    count: recoveredAgents.length,
    agents: recoveredAgents
  }
}
