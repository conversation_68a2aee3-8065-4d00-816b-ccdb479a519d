import { NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function GET() {
  try {
    // 检查智能体数据文件是否存在
    const dataDir = path.join(process.cwd(), 'data')
    const agentsFile = path.join(dataDir, 'agents.json')
    
    let agentCount = 0
    if (fs.existsSync(agentsFile)) {
      const data = JSON.parse(fs.readFileSync(agentsFile, 'utf-8'))
      agentCount = data.agents?.length || 0
    }

    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      services: {
        api: 'running',
        agents: `${agentCount} loaded`
      }
    })
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
