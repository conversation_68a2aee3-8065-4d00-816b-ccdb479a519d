# ⚡ 吉发集团财务AI平台 - 快速启动指南

## 🎯 5分钟快速部署

### 前提条件
- ✅ 已购买云服务器（2核4G+）
- ✅ 已获取服务器IP和SSH访问权限
- ✅ 本地已安装Git和SSH客户端

### 🚀 一键部署命令

```bash
# 1. 下载项目（如果还没有）
git clone <repository-url>
cd finance-ai-demo

# 2. 配置服务器环境（首次部署）
scp server-setup.sh ubuntu@YOUR_SERVER_IP:~/
ssh ubuntu@YOUR_SERVER_IP 'chmod +x server-setup.sh && ./server-setup.sh'

# 3. 一键部署应用
chmod +x deploy-production.sh
./deploy-production.sh YOUR_SERVER_IP ubuntu

# 4. 验证部署
curl http://YOUR_SERVER_IP:5666/api/health
```

### 🎉 部署完成！

访问地址：`http://YOUR_SERVER_IP:5666`

## 📱 主要功能入口

| 功能 | 访问地址 | 说明 |
|------|----------|------|
| 🏠 **主页** | `/` | 平台概览和导航 |
| 💬 **智能对话** | `/chat` | 与20个AI智能体对话 |
| 🤖 **智能体管理** | `/agent-library` | 管理和配置智能体 |
| 📊 **数据仪表板** | `/dashboard` | 财务数据可视化 |
| 🔍 **洞察工作室** | `/insight-studio` | 数据分析工具 |

## 🤖 智能体快速使用

### 1. 选择智能体
1. 访问 `/chat` 页面
2. 点击智能体选择器
3. 从20个专业智能体中选择合适的

### 2. 开始对话
```
示例对话：
用户: "请分析一下公司的现金流状况"
智能体: "我来为您分析现金流状况..."
```

### 3. 智能体推荐

| 场景 | 推荐智能体 | 用途 |
|------|------------|------|
| 📊 **日常咨询** | 财务智能助手 | 综合财务问题解答 |
| 📈 **报表分析** | 财务报表洞察智能体 | 深度报表分析 |
| 💰 **预算管理** | 智能预算编制智能体 | 预算制定和管理 |
| 🔍 **风险管控** | 财务风险雷达智能体 | 风险识别和预警 |
| 📋 **税务处理** | 税务筹划与申报智能体 | 税务筹划和申报 |

## 🛠️ 常用管理命令

### 查看服务状态
```bash
# 连接服务器
ssh ubuntu@YOUR_SERVER_IP

# 查看容器状态
cd /opt/finance-ai-demo
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

### 重启服务
```bash
# 重启应用
docker-compose -f docker-compose.prod.yml restart

# 完全重新部署
./deploy-production.sh YOUR_SERVER_IP ubuntu
```

### 备份数据
```bash
# 备份智能体配置
cp /opt/finance-ai-demo/data/agents.json ~/backup_$(date +%Y%m%d).json
```

## 🔧 故障排除

### 常见问题

#### 1. 无法访问应用
```bash
# 检查服务状态
docker-compose -f docker-compose.prod.yml ps

# 检查端口
sudo netstat -tulpn | grep :80

# 检查防火墙
sudo ufw status
```

#### 2. 智能体无响应
```bash
# 检查API配置
curl http://localhost:3000/api/health

# 查看应用日志
docker-compose -f docker-compose.prod.yml logs finance-ai-demo
```

#### 3. 内存不足
```bash
# 查看内存使用
free -h

# 清理Docker缓存
docker system prune -f
```

### 紧急恢复
```bash
# 快速重启所有服务
cd /opt/finance-ai-demo
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d

# 恢复智能体数据
cp ~/backup_YYYYMMDD.json /opt/finance-ai-demo/data/agents.json
docker-compose -f docker-compose.prod.yml restart
```

## 📞 获取帮助

### 📚 文档资源
- **详细部署指南**: `PRODUCTION_DEPLOYMENT.md`
- **部署检查清单**: `DEPLOYMENT_CHECKLIST.md`
- **项目说明**: `README.md`
- **技术文档**: `PROJECT_SUMMARY.md`

### 🆘 技术支持
- **在线文档**: 项目Wiki
- **技术支持**: 开发团队
- **紧急联系**: 24小时技术热线

### 💡 最佳实践
1. **定期备份**: 每日备份智能体配置数据
2. **监控资源**: 定期检查服务器资源使用情况
3. **更新维护**: 定期更新系统和应用
4. **安全检查**: 定期检查安全配置和日志

---

## 🎉 恭喜！

您已成功部署吉发集团财务AI平台！

现在可以开始使用20个专业的AI智能体来提升财务管理效率了。

**记住您的访问地址**: `http://YOUR_SERVER_IP`

如有任何问题，请参考详细文档或联系技术支持团队。
