<template>
  <div class="min-h-screen relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
    <!-- 粒子背景 -->
    <div class="absolute inset-0">
      <canvas ref="particleCanvas" class="w-full h-full"></canvas>
    </div>
    
    <!-- 主要内容 -->
    <div class="relative z-10 min-h-screen flex items-center justify-center px-4">
      <div class="w-full max-w-md">
        <!-- Logo和标题 -->
        <div class="text-center mb-8">
          <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
          <h1 class="text-3xl font-bold text-white mb-2">财务智能体平台</h1>
          <p class="text-blue-200">吉林市国有资本发展控股集团有限公司</p>
          <div class="mt-4 text-sm text-blue-300">
            {{ currentTime }}
          </div>
        </div>
        
        <!-- 登录表单 -->
        <div class="finance-glass rounded-2xl p-8 shadow-2xl">
          <a-form
            :model="loginForm"
            :rules="rules"
            @finish="handleLogin"
            layout="vertical"
            class="space-y-6"
          >
            <a-form-item name="username" label="用户名" class="mb-6">
              <a-input
                v-model:value="loginForm.username"
                size="large"
                placeholder="请输入用户名"
                class="finance-input"
              >
                <template #prefix>
                  <UserOutlined class="text-blue-400" />
                </template>
              </a-input>
            </a-form-item>
            
            <a-form-item name="password" label="密码" class="mb-6">
              <a-input-password
                v-model:value="loginForm.password"
                size="large"
                placeholder="请输入密码"
                class="finance-input"
              >
                <template #prefix>
                  <LockOutlined class="text-blue-400" />
                </template>
              </a-input-password>
            </a-form-item>
            
            <a-form-item class="mb-6">
              <a-checkbox v-model:checked="loginForm.remember" class="text-white">
                记住我
              </a-checkbox>
            </a-form-item>
            
            <a-form-item>
              <a-button
                type="primary"
                html-type="submit"
                size="large"
                :loading="loading"
                class="w-full h-12 finance-button finance-button-primary text-lg font-medium"
              >
                登录系统
              </a-button>
            </a-form-item>
          </a-form>
          
          <!-- 快速登录选项 -->
          <div class="mt-6 text-center">
            <div class="text-sm text-blue-200 mb-4">演示账户</div>
            <div class="flex gap-2">
              <a-button
                size="small"
                @click="quickLogin('admin')"
                class="flex-1 finance-button finance-button-secondary"
              >
                管理员
              </a-button>
              <a-button
                size="small"
                @click="quickLogin('finance')"
                class="flex-1 finance-button finance-button-secondary"
              >
                财务经理
              </a-button>
              <a-button
                size="small"
                @click="quickLogin('cfo')"
                class="flex-1 finance-button finance-button-secondary"
              >
                财务总监
              </a-button>
            </div>
          </div>
        </div>
        
        <!-- 底部信息 -->
        <div class="text-center mt-8 text-blue-200 text-sm">
          <p>© 2024 吉资本控股 · 财务智能体平台演示系统</p>
          <p class="mt-1">Powered by AI & Vue 3</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'

const router = useRouter()
const particleCanvas = ref<HTMLCanvasElement>()
const currentTime = ref('')
const loading = ref(false)

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
  remember: false
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 更新时间
const updateTime = () => {
  const now = new Date()
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    weekday: 'long'
  }
  currentTime.value = now.toLocaleDateString('zh-CN', options)
}

// 粒子动画
const initParticles = () => {
  if (!particleCanvas.value) return
  
  const canvas = particleCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return
  
  canvas.width = window.innerWidth
  canvas.height = window.innerHeight
  
  const particles: Array<{
    x: number
    y: number
    vx: number
    vy: number
    size: number
    opacity: number
  }> = []
  
  // 创建粒子
  for (let i = 0; i < 50; i++) {
    particles.push({
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      vx: (Math.random() - 0.5) * 0.5,
      vy: (Math.random() - 0.5) * 0.5,
      size: Math.random() * 2 + 1,
      opacity: Math.random() * 0.5 + 0.2
    })
  }
  
  // 动画循环
  const animate = () => {
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    
    particles.forEach(particle => {
      particle.x += particle.vx
      particle.y += particle.vy
      
      if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1
      if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1
      
      ctx.beginPath()
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
      ctx.fillStyle = `rgba(59, 130, 246, ${particle.opacity})`
      ctx.fill()
    })
    
    requestAnimationFrame(animate)
  }
  
  animate()
}

// 登录处理
const handleLogin = async () => {
  loading.value = true
  
  try {
    // 模拟登录API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 保存登录状态
    localStorage.setItem('finance_ai_token', 'demo_token')
    localStorage.setItem('finance_ai_user', JSON.stringify({
      username: loginForm.value.username,
      role: 'admin',
      loginTime: new Date().toISOString()
    }))
    
    message.success('登录成功！')
    router.push('/dashboard')
  } catch (error) {
    message.error('登录失败，请重试')
  } finally {
    loading.value = false
  }
}

// 快速登录
const quickLogin = (role: string) => {
  const userMap = {
    admin: { username: 'admin', password: '123456' },
    finance: { username: 'finance', password: '123456' },
    cfo: { username: 'cfo', password: '123456' }
  }
  
  loginForm.value.username = userMap[role as keyof typeof userMap].username
  loginForm.value.password = userMap[role as keyof typeof userMap].password
  handleLogin()
}

// 生命周期
onMounted(() => {
  updateTime()
  const timeInterval = setInterval(updateTime, 1000)
  initParticles()
  
  // 窗口大小变化时重新初始化粒子
  const handleResize = () => {
    if (particleCanvas.value) {
      particleCanvas.value.width = window.innerWidth
      particleCanvas.value.height = window.innerHeight
    }
  }
  window.addEventListener('resize', handleResize)
  
  onUnmounted(() => {
    clearInterval(timeInterval)
    window.removeEventListener('resize', handleResize)
  })
})
</script>

<style scoped>
.finance-input {
  @apply bg-white/10 border-white/20 text-white placeholder-blue-200;
}

.finance-input:focus {
  @apply border-blue-400 bg-white/20;
}

:deep(.ant-input-affix-wrapper) {
  @apply bg-white/10 border-white/20;
}

:deep(.ant-input-affix-wrapper:focus) {
  @apply border-blue-400 bg-white/20 shadow-none;
}

:deep(.ant-input-affix-wrapper-focused) {
  @apply border-blue-400 bg-white/20 shadow-none;
}

:deep(.ant-input) {
  @apply bg-transparent text-white placeholder-blue-200;
}

:deep(.ant-checkbox-wrapper) {
  @apply text-white;
}

:deep(.ant-form-item-label > label) {
  @apply text-white font-medium;
}
</style>
