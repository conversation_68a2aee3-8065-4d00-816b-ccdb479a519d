'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, Badge, Progress, Statistic, Alert, Row, Col } from 'antd'
import {
  DashboardOutlined,
  RiseOutlined,
  AlertOutlined,
  <PERSON>Outlined,
  <PERSON><PERSON><PERSON><PERSON>utlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON>Outlined,
  <PERSON>Outlined,
  <PERSON><PERSON>Outlined,
  FallOutlined,
  TrophyOutlined,
  FireOutlined,
  ThunderboltFilled,
  EnvironmentOutlined
} from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'
import CountUp from 'react-countup'
import DashboardLayout from '@/components/DashboardLayout'
import OpenStreetMap from '@/components/OpenStreetMap'

export default function DashboardPage() {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [aiInsights, setAiInsights] = useState('')
  const [alertCount, setAlertCount] = useState(3)
  const [realtimeData, setRealtimeData] = useState({
    cashFlow: 2850000,
    revenue: 15600000,
    profit: 3200000,
    expenses: 12400000
  })
  const [animationKey, setAnimationKey] = useState(0)

  // 处理KPI卡片点击
  const handleKpiClick = (kpiTitle: string) => {
    console.log('点击了KPI卡片:', kpiTitle)
    // 这里可以添加具体的跳转逻辑，比如跳转到详细页面
    switch(kpiTitle) {
      case '营业收入':
        // 可以跳转到收入详情页面
        window.location.href = '/revenue-details'
        break
      case '净利润':
        // 可以跳转到利润详情页面
        window.location.href = '/profit-details'
        break
      case '现金流':
        // 可以跳转到现金流详情页面
        window.location.href = '/cashflow-details'
        break
      case 'ROE':
        // 可以跳转到ROE详情页面
        window.location.href = '/roe-details'
        break
      default:
        // 默认跳转到Chat页面进行咨询
        window.location.href = '/chat'
    }
  }

  // 处理警报点击
  const handleAlertClick = (alertTitle: string) => {
    console.log('点击了警报:', alertTitle)
    // 跳转到Chat页面并预设问题
    const query = encodeURIComponent(`请帮我分析：${alertTitle}`)
    window.location.href = `/chat?q=${query}`
  }

  // 实时时间更新
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  // 实时数据更新模拟
  useEffect(() => {
    const dataTimer = setInterval(() => {
      setRealtimeData(prev => ({
        cashFlow: prev.cashFlow + (Math.random() - 0.5) * 50000,
        revenue: prev.revenue + (Math.random() - 0.5) * 100000,
        profit: prev.profit + (Math.random() - 0.5) * 30000,
        expenses: prev.expenses + (Math.random() - 0.5) * 80000
      }))
      setAnimationKey(prev => prev + 1)
    }, 3000)
    return () => clearInterval(dataTimer)
  }, [])

  // AI洞察滚动文字
  useEffect(() => {
    const insights = [
      '今日现金流较昨日增长12.3%，主要来源于应收账款回收...',
      '检测到Q4预算执行率偏低，建议关注市场推广费用控制...',
      '供应商付款周期延长至45天，可能影响供应链稳定性...',
      '投资收益率达到8.2%，超出年度目标0.7个百分点...'
    ]
    
    let index = 0
    const updateInsight = () => {
      setAiInsights(insights[index])
      index = (index + 1) % insights.length
    }
    
    updateInsight()
    const insightTimer = setInterval(updateInsight, 4000)
    return () => clearInterval(insightTimer)
  }, [])

  // 现金流趋势图配置
  const cashFlowOption = {
    title: {
      text: '现金流趋势',
      textStyle: { color: '#334155', fontSize: 16, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e2e8f0',
      textStyle: { color: '#334155' }
    },
    legend: {
      data: ['现金流入', '现金流出'],
      textStyle: { color: '#64748b' },
      top: '8%'
    },
    grid: { left: '3%', right: '4%', bottom: '3%', top: '15%', containLabel: true },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      axisLine: { lineStyle: { color: '#e2e8f0' } },
      axisLabel: { color: '#64748b' }
    },
    yAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: '#e2e8f0' } },
      axisLabel: { color: '#64748b', formatter: '{value}万' },
      splitLine: { lineStyle: { color: '#f1f5f9' } }
    },
    animation: true,
    animationDuration: 2000,
    animationEasing: 'cubicOut',
    series: [{
      name: '现金流入',
      type: 'line',
      smooth: true,
      data: [2200, 2800, 2600, 3200, 2900, 3500, 3800, 3600, 4200, 3900, 4500, 4800],
      lineStyle: { color: '#10b981', width: 3 },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
            { offset: 1, color: 'rgba(16, 185, 129, 0.05)' }
          ]
        }
      },
      symbol: 'circle',
      symbolSize: 6,
      emphasis: { focus: 'series' }
    }, {
      name: '现金流出',
      type: 'line',
      smooth: true,
      data: [1800, 2200, 2100, 2600, 2400, 2800, 3100, 2900, 3400, 3200, 3600, 3900],
      lineStyle: { color: '#ef4444', width: 3 },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(239, 68, 68, 0.3)' },
            { offset: 1, color: 'rgba(239, 68, 68, 0.05)' }
          ]
        }
      },
      symbol: 'circle',
      symbolSize: 6,
      emphasis: { focus: 'series' }
    }]
  }

  // 收入构成饼图配置
  const revenueOption = {
    title: {
      text: '收入构成分析',
      textStyle: { color: '#334155', fontSize: 16, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}万元 ({d}%)',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e2e8f0',
      textStyle: { color: '#334155' }
    },
    legend: {
      bottom: '5%',
      textStyle: { color: '#64748b' },
      itemGap: 20
    },
    animation: true,
    animationType: 'scale',
    animationEasing: 'elasticOut',
    animationDelay: function (idx: number) {
      return Math.random() * 200;
    },
    series: [{
      name: '收入来源',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '45%'],
      roseType: 'area',
      data: [
        {
          value: 4500,
          name: '主营业务',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 1,
              colorStops: [
                { offset: 0, color: '#3b82f6' },
                { offset: 1, color: '#1d4ed8' }
              ]
            }
          }
        },
        {
          value: 2800,
          name: '投资收益',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 1,
              colorStops: [
                { offset: 0, color: '#10b981' },
                { offset: 1, color: '#047857' }
              ]
            }
          }
        },
        {
          value: 1200,
          name: '其他业务',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 1,
              colorStops: [
                { offset: 0, color: '#f59e0b' },
                { offset: 1, color: '#d97706' }
              ]
            }
          }
        },
        {
          value: 800,
          name: '政府补贴',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 1,
              colorStops: [
                { offset: 0, color: '#8b5cf6' },
                { offset: 1, color: '#7c3aed' }
              ]
            }
          }
        }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 20,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        },
        scaleSize: 10
      },
      label: {
        show: true,
        formatter: '{b}\n{d}%',
        fontSize: 12,
        fontWeight: 'bold'
      },
      labelLine: {
        show: true,
        length: 15,
        length2: 10
      }
    }]
  }

  // 资产负债表配置
  const balanceSheetOption = {
    title: {
      text: '资产负债结构',
      textStyle: { color: '#334155', fontSize: 16, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e2e8f0',
      textStyle: { color: '#334155' },
      formatter: function(params: any) {
        let result = params[0].name + '<br/>';
        params.forEach((item: any) => {
          result += item.marker + item.seriesName + ': ' + item.value + '万元<br/>';
        });
        return result;
      }
    },
    legend: {
      data: ['资产', '负债', '所有者权益'],
      textStyle: { color: '#64748b' },
      top: '8%',
      itemGap: 30
    },
    grid: { left: '3%', right: '4%', bottom: '3%', top: '15%', containLabel: true },
    xAxis: {
      type: 'category',
      data: ['Q1', 'Q2', 'Q3', 'Q4'],
      axisLine: { lineStyle: { color: '#e2e8f0' } },
      axisLabel: { color: '#64748b', fontSize: 12 }
    },
    yAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: '#e2e8f0' } },
      axisLabel: { color: '#64748b', formatter: '{value}万' },
      splitLine: { lineStyle: { color: '#f1f5f9' } }
    },
    animation: true,
    animationDuration: 1500,
    animationEasing: 'cubicOut',
    animationDelay: function (idx: number) {
      return idx * 100;
    },
    series: [
      {
        name: '资产',
        type: 'bar',
        data: [15000, 16200, 17500, 18800],
        itemStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: '#3b82f6' },
              { offset: 1, color: '#1e40af' }
            ]
          },
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(59, 130, 246, 0.5)'
          }
        }
      },
      {
        name: '负债',
        type: 'bar',
        data: [6800, 7200, 7900, 8500],
        itemStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: '#ef4444' },
              { offset: 1, color: '#dc2626' }
            ]
          },
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(239, 68, 68, 0.5)'
          }
        }
      },
      {
        name: '所有者权益',
        type: 'bar',
        data: [8200, 9000, 9600, 10300],
        itemStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: '#10b981' },
              { offset: 1, color: '#059669' }
            ]
          },
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(16, 185, 129, 0.5)'
          }
        }
      }
    ]
  }

  // 模拟KPI数据 - 更加丰富和动态
  const kpiData = [
    {
      title: '总资产',
      value: realtimeData.revenue / 1000000,
      suffix: '亿元',
      trend: 'up',
      change: '****%',
      status: 'excellent',
      icon: <TrophyOutlined />,
      color: '#52c41a',
      description: '同比增长'
    },
    {
      title: '营业收入',
      value: realtimeData.revenue / 1000000,
      suffix: '万元',
      trend: 'up',
      change: '+12.3%',
      status: 'excellent',
      icon: <RiseOutlined />,
      color: '#1890ff',
      description: '月度增长'
    },
    {
      title: '净利润',
      value: realtimeData.profit / 1000000,
      suffix: '万元',
      trend: 'up',
      change: '+15.8%',
      status: 'excellent',
      icon: <FireOutlined />,
      color: '#722ed1',
      description: '超预期增长'
    },
    {
      title: '现金流',
      value: realtimeData.cashFlow / 1000000,
      suffix: '万元',
      trend: realtimeData.cashFlow > 2800000 ? 'up' : 'down',
      change: realtimeData.cashFlow > 2800000 ? '****%' : '-2.1%',
      status: realtimeData.cashFlow > 2800000 ? 'good' : 'warning',
      icon: <ThunderboltFilled />,
      color: realtimeData.cashFlow > 2800000 ? '#13c2c2' : '#fa8c16',
      description: '实时监控'
    },
    {
      title: '资产负债率',
      value: 45.2,
      suffix: '%',
      trend: 'down',
      change: '-2.1%',
      status: 'good',
      icon: <BarChartOutlined />,
      color: '#1890ff',
      description: '风险可控'
    },
    {
      title: 'ROE',
      value: 18.6,
      suffix: '%',
      trend: 'up',
      change: '****%',
      status: 'excellent',
      icon: <LineChartOutlined />,
      color: '#eb2f96',
      description: '行业领先'
    }
  ]

  // 告警数据
  const alerts = [
    {
      id: 1,
      type: 'warning',
      title: '现金流预警',
      message: '预计下周现金流可能出现缺口，建议提前安排资金调度',
      time: '2分钟前',
      priority: 'high'
    },
    {
      id: 2,
      type: 'info',
      title: '投资机会',
      message: '检测到优质理财产品，预期年化收益率6.8%',
      time: '15分钟前',
      priority: 'medium'
    },
    {
      id: 3,
      type: 'error',
      title: '异常交易',
      message: '发现3笔大额异常支出，需要财务总监审核',
      time: '1小时前',
      priority: 'high'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return '#52c41a'
      case 'good': return '#1890ff'
      case 'warning': return '#fa8c16'
      case 'danger': return '#ff4d4f'
      default: return '#d9d9d9'
    }
  }

  const getTrendIcon = (trend: string) => {
    return trend === 'up' ? '↗' : '↘'
  }

  const getTrendColor = (trend: string) => {
    return trend === 'up' ? '#52c41a' : '#ff4d4f'
  }

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48cGF0dGVybiBpZD0iZ3JpZCIgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIj48cGF0aCBkPSJNIDQwIDAgTCAwIDAgMCA0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJyZ2JhKDE0OCwgMTYzLCAxODQsIDAuMDMpIiBzdHJva2Utd2lkdGg9IjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48L3N2Zz4=')] opacity-50"></div>

      <div className="relative z-10 p-6">
        {/* 顶部导航栏 */}
        <motion.div 
          className="flex items-center justify-between mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-slate-600 to-slate-700 rounded-xl flex items-center justify-center shadow-lg">
              <DashboardOutlined className="text-xl text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-slate-800">Executive Dashboard</h1>
              <p className="text-slate-600">吉发集团财务智能体平台</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-sm text-slate-500">系统时间</div>
              <div className="font-mono text-slate-700">{currentTime.toLocaleString('zh-CN')}</div>
            </div>
            <Badge count={alertCount} className="cursor-pointer" onClick={() => {
              console.log('点击了通知铃铛')
              // 滚动到警报区域
              const alertSection = document.getElementById('alert-section')
              if (alertSection) {
                alertSection.scrollIntoView({ behavior: 'smooth' })
              }
            }}>
              <div className="w-10 h-10 bg-white rounded-lg shadow-md flex items-center justify-center hover:shadow-lg transition-shadow">
                <BellOutlined className="text-slate-600" />
              </div>
            </Badge>
          </div>
        </motion.div>

        {/* AI洞察栏 */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 shadow-lg">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <EyeOutlined className="text-white text-sm" />
              </div>
              <div className="flex-1">
                <div className="text-sm text-blue-600 font-medium mb-1">AI财务洞察</div>
                <motion.div 
                  className="text-slate-700"
                  key={aiInsights}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  {aiInsights}
                </motion.div>
              </div>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            </div>
          </Card>
        </motion.div>

        {/* KPI指标卡片 - 增强版 */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          {kpiData.map((kpi, index) => (
            <motion.div
              key={kpi.title}
              initial={{ opacity: 0, y: 20, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.1 * index }}
              whileHover={{ scale: 1.05, y: -5 }}
              className="cursor-pointer"
              onClick={() => handleKpiClick(kpi.title)}
            >
              <Card className="h-full shadow-lg hover:shadow-2xl transition-all duration-500 border-0 overflow-hidden relative">
                {/* 背景动态效果 */}
                <div
                  className="absolute top-0 right-0 w-20 h-20 rounded-full opacity-10"
                  style={{ backgroundColor: kpi.color }}
                ></div>

                <div className="relative z-10">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <motion.div
                        className="w-12 h-12 rounded-xl flex items-center justify-center text-white shadow-lg"
                        style={{ backgroundColor: kpi.color }}
                        whileHover={{ rotate: 360 }}
                        transition={{ duration: 0.6 }}
                      >
                        {kpi.icon}
                      </motion.div>
                      <div>
                        <div className="text-sm text-slate-500 mb-1">{kpi.title}</div>
                        <div className="flex items-center space-x-2">
                          <CountUp
                            end={kpi.value}
                            duration={2}
                            decimals={kpi.suffix.includes('%') || kpi.suffix.includes(':') ? 1 : 0}
                            className="text-2xl font-bold text-slate-800"
                          />
                          <span className="text-2xl font-bold text-slate-800">{kpi.suffix}</span>
                        </div>
                        <motion.span
                          className="text-sm font-medium flex items-center space-x-1"
                          style={{ color: getTrendColor(kpi.trend) }}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: 1 }}
                        >
                          <span>{getTrendIcon(kpi.trend)}</span>
                          <span>{kpi.change}</span>
                          <span className="text-xs text-slate-400">({kpi.description})</span>
                        </motion.span>
                      </div>
                    </div>
                    <div className="text-right">
                      <motion.div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: getStatusColor(kpi.status) }}
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      ></motion.div>
                    </div>
                  </div>

                  {/* 进度条 */}
                  <div className="mt-4">
                    <Progress
                      percent={Math.min(100, (kpi.value / (kpi.title.includes('率') ? 100 : kpi.value * 1.2)) * 100)}
                      strokeColor={kpi.color}
                      trailColor="#f1f5f9"
                      size="small"
                      showInfo={false}
                    />
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* 图表区域 */}
        <Row gutter={[24, 24]} className="mb-8">
          <Col xs={24} lg={12}>
            <Card
              title="现金流趋势分析"
              className="shadow-lg h-96"
              extra={
                <motion.div
                  className="flex items-center space-x-2"
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-xs text-green-600">实时更新</span>
                </motion.div>
              }
            >
              <ReactECharts option={cashFlowOption} style={{ height: '300px' }} />
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card title="收入构成分析" className="shadow-lg h-96">
              <ReactECharts option={revenueOption} style={{ height: '300px' }} />
            </Card>
          </Col>
        </Row>

        <Row gutter={[24, 24]} className="mb-8">
          <Col xs={24}>
            <Card title="资产负债结构分析" className="shadow-lg">
              <ReactECharts option={balanceSheetOption} style={{ height: '400px' }} />
            </Card>
          </Col>
        </Row>

        {/* 3D地图区域 */}
        <Row gutter={[24, 24]} className="mb-8">
          <Col xs={24}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.4 }}
            >
              <Card
                title={
                  <div className="flex items-center space-x-2">
                    <motion.div
                      animate={{ rotate: [0, 360] }}
                      transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                    >
                      <EnvironmentOutlined className="text-blue-500" />
                    </motion.div>
                    <span>吉发集团固定资产分布图</span>
                    <Badge count="3D" style={{ backgroundColor: '#52c41a' }} />
                  </div>
                }
                className="shadow-lg"
                extra={
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                      <span className="text-green-600">运营中</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 bg-orange-400 rounded-full"></div>
                      <span className="text-orange-600">建设中</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                      <span className="text-gray-600">规划中</span>
                    </div>
                  </div>
                }
              >
                <OpenStreetMap />
              </Card>
            </motion.div>
          </Col>
        </Row>

        {/* 实时数据流区域 */}
        <Row gutter={[24, 24]} className="mb-8">
          <Col xs={24} md={6}>
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 1.4 }}
            >
              <Card className="text-center shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                <motion.div
                  animate={{ y: [0, -5, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <DollarOutlined className="text-4xl text-blue-600 mb-2" />
                </motion.div>
                <div className="text-sm text-blue-600 mb-1">实时营收</div>
                <CountUp
                  end={realtimeData.revenue}
                  duration={2}
                  separator=","
                  className="text-2xl font-bold text-blue-800"
                />
                <div className="text-xs text-blue-500 mt-1">元</div>
              </Card>
            </motion.div>
          </Col>

          <Col xs={24} md={6}>
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 1.5 }}
            >
              <Card className="text-center shadow-lg bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                >
                  <RiseOutlined className="text-4xl text-green-600 mb-2" />
                </motion.div>
                <div className="text-sm text-green-600 mb-1">净利润</div>
                <CountUp
                  end={realtimeData.profit}
                  duration={2}
                  separator=","
                  className="text-2xl font-bold text-green-800"
                />
                <div className="text-xs text-green-500 mt-1">元</div>
              </Card>
            </motion.div>
          </Col>

          <Col xs={24} md={6}>
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 1.6 }}
            >
              <Card className="text-center shadow-lg bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <ThunderboltFilled className="text-4xl text-purple-600 mb-2" />
                </motion.div>
                <div className="text-sm text-purple-600 mb-1">现金流</div>
                <CountUp
                  end={realtimeData.cashFlow}
                  duration={2}
                  separator=","
                  className="text-2xl font-bold text-purple-800"
                />
                <div className="text-xs text-purple-500 mt-1">元</div>
              </Card>
            </motion.div>
          </Col>

          <Col xs={24} md={6}>
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 1.7 }}
            >
              <Card className="text-center shadow-lg bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
                <motion.div
                  animate={{ x: [0, 5, 0, -5, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <BarChartOutlined className="text-4xl text-orange-600 mb-2" />
                </motion.div>
                <div className="text-sm text-orange-600 mb-1">总支出</div>
                <CountUp
                  end={realtimeData.expenses}
                  duration={2}
                  separator=","
                  className="text-2xl font-bold text-orange-800"
                />
                <div className="text-xs text-orange-500 mt-1">元</div>
              </Card>
            </motion.div>
          </Col>
        </Row>

        {/* 底部区域：告警中心和快速操作 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 告警中心 - 增强版 */}
          <motion.div
            className="lg:col-span-2"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 1.8 }}
          >
            <Card
              id="alert-section"
              title={
                <div className="flex items-center space-x-2">
                  <motion.div
                    animate={{ rotate: [0, 15, -15, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <AlertOutlined className="text-orange-500" />
                  </motion.div>
                  <span>智能告警中心</span>
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  >
                    <Badge count={alertCount} size="small" />
                  </motion.div>
                  <div className="ml-auto flex items-center space-x-2">
                    <motion.div
                      className="w-2 h-2 bg-red-400 rounded-full"
                      animate={{ opacity: [0.3, 1, 0.3] }}
                      transition={{ duration: 1, repeat: Infinity }}
                    ></motion.div>
                    <span className="text-xs text-slate-500">实时监控</span>
                  </div>
                </div>
              }
              className="shadow-lg"
            >
              <AnimatePresence>
                <div className="space-y-4">
                  {alerts.map((alert, index) => (
                    <motion.div
                      key={alert.id}
                      initial={{ opacity: 0, x: -20, scale: 0.9 }}
                      animate={{ opacity: 1, x: 0, scale: 1 }}
                      exit={{ opacity: 0, x: 20, scale: 0.9 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      whileHover={{ scale: 1.02, x: 5 }}
                      className={`p-4 rounded-xl border-l-4 ${
                        alert.type === 'error' ? 'bg-gradient-to-r from-red-50 to-red-25 border-red-400' :
                        alert.type === 'warning' ? 'bg-gradient-to-r from-orange-50 to-orange-25 border-orange-400' :
                        'bg-gradient-to-r from-blue-50 to-blue-25 border-blue-400'
                      } hover:shadow-lg transition-all duration-300 cursor-pointer relative overflow-hidden`}
                      onClick={() => handleAlertClick(alert.title)}
                    >
                      {/* 背景动画效果 */}
                      <motion.div
                        className={`absolute top-0 right-0 w-16 h-16 rounded-full opacity-10 ${
                          alert.type === 'error' ? 'bg-red-400' :
                          alert.type === 'warning' ? 'bg-orange-400' :
                          'bg-blue-400'
                        }`}
                        animate={{ scale: [1, 1.2, 1], rotate: [0, 180, 360] }}
                        transition={{ duration: 4, repeat: Infinity }}
                      ></motion.div>

                      <div className="flex items-start justify-between relative z-10">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <motion.span
                              className="font-semibold text-slate-800"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 0.3 }}
                            >
                              {alert.title}
                            </motion.span>
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ delay: 0.5, type: "spring" }}
                            >
                              <Badge
                                color={alert.priority === 'high' ? 'red' : alert.priority === 'medium' ? 'orange' : 'blue'}
                                text={alert.priority === 'high' ? '高优先级' : alert.priority === 'medium' ? '中优先级' : '低优先级'}
                              />
                            </motion.div>
                          </div>
                          <motion.p
                            className="text-sm text-slate-600 mb-3 leading-relaxed"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.4 }}
                          >
                            {alert.message}
                          </motion.p>
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-slate-400">{alert.time}</span>
                            <motion.button
                              className="text-xs px-3 py-1 rounded-full bg-white shadow-sm hover:shadow-md transition-shadow"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              查看详情
                            </motion.button>
                          </div>
                        </div>
                        <div className="ml-4">
                          <motion.div
                            className={`w-4 h-4 rounded-full ${
                              alert.type === 'error' ? 'bg-red-400' :
                              alert.type === 'warning' ? 'bg-orange-400' :
                              'bg-blue-400'
                            }`}
                            animate={{
                              scale: alert.priority === 'high' ? [1, 1.3, 1] : [1, 1.1, 1],
                              opacity: alert.priority === 'high' ? [0.7, 1, 0.7] : [0.8, 1, 0.8]
                            }}
                            transition={{ duration: 2, repeat: Infinity }}
                          ></motion.div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </AnimatePresence>
            </Card>
          </motion.div>

          {/* 快速操作面板 - 增强版 */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 2 }}
          >
            <Card
              title={
                <div className="flex items-center space-x-2">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
                  >
                    <ThunderboltFilled className="text-blue-500" />
                  </motion.div>
                  <span>智能操作中心</span>
                </div>
              }
              className="shadow-lg"
            >
              <div className="space-y-4">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center space-x-3 shadow-lg hover:shadow-xl"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 2.2 }}
                >
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  >
                    <DashboardOutlined className="text-xl" />
                  </motion.div>
                  <div className="text-left">
                    <div className="font-medium">生成财务报告</div>
                    <div className="text-xs opacity-80">AI智能分析生成</div>
                  </div>
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full p-4 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-300 flex items-center space-x-3 shadow-lg hover:shadow-xl"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 2.4 }}
                >
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <BarChartOutlined className="text-xl" />
                  </motion.div>
                  <div className="text-left">
                    <div className="font-medium">启动智能分析</div>
                    <div className="text-xs opacity-80">深度数据挖掘</div>
                  </div>
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full p-4 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-300 flex items-center space-x-3 shadow-lg hover:shadow-xl"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 2.6 }}
                >
                  <motion.div
                    animate={{ y: [0, -5, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <LineChartOutlined className="text-xl" />
                  </motion.div>
                  <div className="text-left">
                    <div className="font-medium">预测模型运行</div>
                    <div className="text-xs opacity-80">未来趋势预测</div>
                  </div>
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full p-4 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-orange-600 hover:to-orange-700 transition-all duration-300 flex items-center space-x-3 shadow-lg hover:shadow-xl"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 2.8 }}
                >
                  <motion.div
                    animate={{ rotate: [0, 15, -15, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <AlertOutlined className="text-xl" />
                  </motion.div>
                  <div className="text-left">
                    <div className="font-medium">风险评估</div>
                    <div className="text-xs opacity-80">实时风险监控</div>
                  </div>
                </motion.button>
              </div>

              {/* 系统状态 - 增强版 */}
              <motion.div
                className="mt-6 pt-4 border-t border-slate-200"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 3 }}
              >
                <div className="text-sm text-slate-500 mb-4 flex items-center space-x-2">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                  >
                    <EyeOutlined />
                  </motion.div>
                  <span>系统监控状态</span>
                </div>
                <div className="space-y-3">
                  <motion.div
                    className="flex items-center justify-between p-2 rounded-lg bg-green-50"
                    whileHover={{ scale: 1.02 }}
                  >
                    <span className="text-sm text-slate-600">AI引擎</span>
                    <div className="flex items-center space-x-2">
                      <motion.div
                        className="w-3 h-3 bg-green-400 rounded-full"
                        animate={{ scale: [1, 1.3, 1] }}
                        transition={{ duration: 1, repeat: Infinity }}
                      ></motion.div>
                      <span className="text-xs text-green-600 font-medium">运行中</span>
                      <div className="text-xs text-green-500">99.9%</div>
                    </div>
                  </motion.div>
                  <motion.div
                    className="flex items-center justify-between p-2 rounded-lg bg-blue-50"
                    whileHover={{ scale: 1.02 }}
                  >
                    <span className="text-sm text-slate-600">数据同步</span>
                    <div className="flex items-center space-x-2">
                      <motion.div
                        className="w-3 h-3 bg-blue-400 rounded-full"
                        animate={{ opacity: [0.5, 1, 0.5] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      ></motion.div>
                      <span className="text-xs text-blue-600 font-medium">同步中</span>
                      <div className="text-xs text-blue-500">实时</div>
                    </div>
                  </motion.div>
                  <motion.div
                    className="flex items-center justify-between p-2 rounded-lg bg-purple-50"
                    whileHover={{ scale: 1.02 }}
                  >
                    <span className="text-sm text-slate-600">安全防护</span>
                    <div className="flex items-center space-x-2">
                      <motion.div
                        className="w-3 h-3 bg-purple-400 rounded-full"
                        animate={{ rotate: [0, 360] }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      ></motion.div>
                      <span className="text-xs text-purple-600 font-medium">已启用</span>
                      <div className="text-xs text-purple-500">高级</div>
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            </Card>
          </motion.div>
        </div>
      </div>
      </div>
    </DashboardLayout>
  )
}
