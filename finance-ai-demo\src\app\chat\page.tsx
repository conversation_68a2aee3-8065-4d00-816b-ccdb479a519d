'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Input,
  Button,
  Card,
  Avatar,
  Badge,
  Upload,
  Tooltip,
  Dropdown,
  Progress,
  Tag,
  Modal,
  Form,
  Select,
  Switch,
  Table,
  Space,
  Popconfirm,
  message
} from 'antd'
import {
  SendOutlined,
  AudioOutlined,
  PaperClipOutlined,
  RobotOutlined,
  UserOutlined,
  <PERSON><PERSON>Outlined,
  Bar<PERSON><PERSON>Outlined,
  FileTextOutlined,
  B<PERSON>bOutlined,
  LoadingOutlined,
  SoundOutlined,
  EyeOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SettingOutlined
} from '@ant-design/icons'
import DashboardLayout from '@/components/DashboardLayout'
import InsightPanel from '@/components/InsightPanel'
import { difyApi } from '@/services/difyApi'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'
import { useRouter } from 'next/navigation'
import { useAgentStore, type Agent } from '@/store/agentStore'
import 'highlight.js/styles/github.css'
import '@/styles/markdown.css'

// 智能体选择器样式
const agentSelectorStyles = `
  .agent-selector .ant-select-selector {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    border: 2px solid #e2e8f0 !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    transition: all 0.3s ease !important;
    height: 48px !important;
    padding: 8px 16px !important;
    display: flex !important;
    align-items: center !important;
    min-width: 400px !important;
  }

  .agent-selector .ant-select-selector:hover {
    border-color: #3b82f6 !important;
    box-shadow: 0 6px 20px -5px rgba(59, 130, 246, 0.25) !important;
    transform: translateY(-1px);
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%) !important;
  }

  .agent-selector .ant-select-selection-item {
    font-weight: 500 !important;
    color: #1e293b !important;
    font-size: 15px !important;
    line-height: 1.4 !important;
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
  }

  .agent-selector .ant-select-arrow {
    color: #3b82f6 !important;
    font-size: 16px !important;
  }

  .agent-selector .ant-select-selection-search {
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
  }

  .agent-selector .ant-select-selection-search-input {
    height: 100% !important;
    font-size: 15px !important;
  }

  .agent-selector .ant-select-dropdown {
    border-radius: 16px !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
    border: 2px solid #e2e8f0 !important;
  }

  .agent-selector .ant-select-item {
    padding: 12px 20px !important;
    border-bottom: 1px solid #e2e8f0 !important;
    min-height: 70px !important;
    display: flex !important;
    align-items: center !important;
    position: relative !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    transition: all 0.3s ease !important;
  }

  .agent-selector .ant-select-item:hover {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
    border-left: 4px solid #06b6d4 !important;
    box-shadow: 0 4px 15px -3px rgba(6, 182, 212, 0.2) !important;
    transform: translateX(4px) !important;
  }

  .agent-selector .ant-select-item:hover::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(90deg, rgba(6, 182, 212, 0.1) 0%, transparent 100%) !important;
    pointer-events: none !important;
  }

  .agent-selector .ant-select-item-option-selected {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%) !important;
    border-left: 4px solid #3b82f6 !important;
    box-shadow: 0 4px 15px -3px rgba(59, 130, 246, 0.3) !important;
  }

  .agent-selector .ant-select-item-option-selected::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.15) 0%, transparent 100%) !important;
    pointer-events: none !important;
  }

  .agent-selector .ant-select-item-option-content {
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
    position: relative !important;
    z-index: 1 !important;
  }

  .agent-selector .ant-select-dropdown {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(226, 232, 240, 0.8) !important;
  }
`

const { TextArea } = Input
const { Option } = Select



// 智能体类型定义
const agentTypes = [
  { key: 'financial-analysis', name: '财务分析师', icon: <BarChartOutlined />, color: '#1890ff' },
  { key: 'risk-assessment', name: '风险评估师', icon: <ThunderboltOutlined />, color: '#ff4d4f' },
  { key: 'report-generator', name: '报告生成器', icon: <FileTextOutlined />, color: '#52c41a' },
  { key: 'investment-advisor', name: '财务智能助手', icon: <BulbOutlined />, color: '#722ed1' },
  { key: 'compliance-checker', name: '合规检查员', icon: <EyeOutlined />, color: '#fa8c16' }
]

// 初始消息数据
const initialMessages = [
  {
    id: 1,
    type: 'system',
    content: '欢迎使用吉发集团财务智能体平台！我是您的AI财务助手，已连接智能体服务。请输入您的问题，我将为您提供专业的财务分析和建议。',
    timestamp: new Date(Date.now() - 60000),
    agent: null,
    components: undefined
  },
  {
    id: 2,
    type: 'assistant',
    content: `# 财务分析报告

## 📊 关键财务指标

### 收入分析
- **营业收入**: 15,600万元 *(同比增长12.3%)*
- **净利润**: 3,200万元 *(同比增长15.8%)*
- **现金流**: 2,850万元 *(环比增长8.2%)*

### 盈利能力
| 指标 | 数值 | 同比变化 |
|------|------|----------|
| ROE | 18.6% | +2.1% |
| ROA | 12.4% | +1.8% |
| 毛利率 | 35.2% | +0.9% |

## ⚠️ 风险提示

> **重要提醒**: 以下风险因素需要重点关注

1. **市场风险**
   - 宏观经济波动影响
   - 行业竞争加剧

2. **流动性风险**
   - 应收账款周转率下降
   - 现金流季节性波动

## 💡 投资建议

\`\`\`
建议配置方案：
- 货币基金: 30%
- 银行理财: 40%
- 债券基金: 20%
- 权益投资: 10%
\`\`\`

**预期年化收益**: 6.2-7.8%

---

*以上分析基于最新财务数据，仅供参考*`,
    timestamp: new Date(Date.now() - 30000),
    agent: agentTypes[0],
    components: undefined
  },
  {
    id: 3,
    type: 'assistant',
    content: `<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open> <summary> Thinking... </summary>用户要求我以吉发集团财务高级分析专家的身份进行风险评估，当前时间是2025年7月。这是个需要深度专业分析的任务。

用户可能正在准备集团半年度财务报告或向董事会汇报的材料，需要全面了解企业面临的主要风险点。考虑到吉发集团作为东北地区大型国有资本运营平台，资产规模超2280亿的特殊性，这份报告必须兼顾政策敏感性和市场真实性。

让我先梳理评估框架。作为市级国有资本投资公司，核心风险维度应包括政策合规性、市场波动、流动性管理、债务可持续性和运营效率五大板块。每个板块都需要量化分析和趋势预判。

先从政策风险切入。注意到集团90%股权由市国资委控股，近期国家发改委发布的《东北振兴"十四五"规划中期调整方案》特别强调地方融资平台转型，这直接影响我们的政府项目融资渠道。需要重点评估政策过渡期对存量项目的影响，比如松花江生态治理工程的专项债续发问题。</details>

# 🔍 吉发集团风险评估报告

## 📋 执行摘要

基于2025年上半年财务数据和市场环境分析，吉发集团当前面临**中等偏高**风险水平，主要集中在流动性管理和债务结构优化方面。

## ⚠️ 主要风险识别

### 1. 流动性风险 🔴 **高风险**
- 短期债务占比升至45%
- 货币资金覆盖率仅1.2倍
- Q4票据兑付高峰期临近

### 2. 债务结构风险 🟡 **中风险**
- 私募债占比突破30%，成本达7.2%
- 2026年80亿债务到期压力
- 需启动债务置换谈判

### 3. 政策合规风险 🟡 **中风险**
- 融资平台转型政策影响
- 政府项目融资渠道收紧
- 专项债续发不确定性

## 💡 应对建议

1. **立即启动应急融资方案**
2. **三季度前发行15亿超短融**
3. **优化债务结构，降低融资成本**
4. **建立与市财政局专项沟通机制**

---
*报告基于最新监管要求和市场数据编制*`,
    timestamp: new Date(Date.now() - 10000),
    agent: agentTypes[1],
    components: undefined
  }
]

// 处理thinking内容的函数
const processThinkingContent = (content: string) => {
  // 检查是否包含完整的thinking标签
  if (content.includes('<details') && content.includes('Thinking...') && content.includes('</details>')) {
    // 分离thinking部分和正常内容
    const thinkingMatch = content.match(/<details[^>]*>([\s\S]*?)<\/details>([\s\S]*)/);
    if (thinkingMatch) {
      const thinkingContent = thinkingMatch[1].replace(/<summary[^>]*>.*?<\/summary>/g, '').trim();
      const normalContent = thinkingMatch[2].trim();

      return {
        hasThinking: true,
        isThinkingComplete: true,
        thinkingContent,
        normalContent
      };
    }
  }

  // 检查是否正在输出thinking（开始了但还没结束）
  if (content.includes('<details') && content.includes('Thinking...') && !content.includes('</details>')) {
    const thinkingStartMatch = content.match(/<details[^>]*>[\s\S]*?<summary[^>]*>.*?Thinking.*?<\/summary>([\s\S]*)/);
    if (thinkingStartMatch) {
      return {
        hasThinking: true,
        isThinkingComplete: false,
        thinkingContent: thinkingStartMatch[1],
        normalContent: ''
      };
    }
  }

  return {
    hasThinking: false,
    isThinkingComplete: false,
    thinkingContent: '',
    normalContent: content
  };
};

export default function ChatPage() {
  const router = useRouter()
  const [messages, setMessages] = useState(initialMessages)
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [selectedAgent, setSelectedAgent] = useState<any>(null)
  const [showInsightPanel, setShowInsightPanel] = useState(false)
  const [difyConnected, setDifyConnected] = useState<boolean | null>(null)
  const [isStreaming, setIsStreaming] = useState(false)
  const [currentThinkingMessageId, setCurrentThinkingMessageId] = useState<number | null>(null)

  // 使用全局智能体状态管理
  const {
    agents,
    currentAgent,
    setCurrentAgent,
    getEnabledAgents,
    initializeAgents
  } = useAgentStore()

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  // 初始化智能体数据
  useEffect(() => {
    initializeAgents()
  }, [initializeAgents])

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // 测试智能体服务连接
  useEffect(() => {
    const testDifyConnection = async () => {
      console.log('🔍 开始测试智能体服务连接...')
      const isConnected = await difyApi.testConnection()
      setDifyConnected(isConnected)
      console.log('🔗 智能体服务连接状态:', isConnected ? '已连接' : '连接失败')
    }

    testDifyConnection()
  }, [])

  // 手动测试连接
  const handleTestConnection = async () => {
    setDifyConnected(null) // 设置为加载状态
    const isConnected = await difyApi.testConnection()
    setDifyConnected(isConnected)
  }

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage = {
      id: Date.now(),
      type: 'user' as const,
      content: inputValue,
      timestamp: new Date(),
      agent: null,
      components: undefined
    }

    setMessages(prev => [...prev, userMessage])
    const currentInput = inputValue
    setInputValue('')
    setIsLoading(true)

    try {
      console.log('💬 开始流式发送消息到智能体服务:', currentInput)
      console.log('🤖 使用当前选中的智能体:', {
        name: currentAgent?.name,
        apiKey: currentAgent?.apiKey ? currentAgent.apiKey.substring(0, 10) + '...' : '无API密钥'
      })

      // 检查是否有选中的智能体
      if (!currentAgent) {
        message.error('请先选择一个智能体')
        setIsLoading(false)
        return
      }

      // 创建初始的助手消息（空内容，用于流式更新）
      const assistantMessageId = Date.now() + 1
      const initialAssistantMessage = {
        id: assistantMessageId,
        type: 'assistant' as const,
        content: '',
        timestamp: new Date(),
        agent: {
          name: currentAgent.name,
          description: currentAgent.description,
          capabilities: []
        },
        components: undefined
      }

      setMessages(prev => [...prev, initialAssistantMessage])

      // 尝试使用流式API，如果失败则回退到阻塞模式
      try {
        setIsStreaming(true) // 开始流式响应
        await difyApi.sendMessageStream(
          currentInput,
          'demo_user',
          // 处理流式数据块
          (chunk) => {
            console.log('📨 收到流式数据块:', chunk)
            if (chunk.event === 'message' && chunk.answer) {
              setMessages(prev => prev.map(msg => {
                if (msg.id === assistantMessageId) {
                  const newContent = msg.content + chunk.answer;

                  // 检查是否开始thinking
                  if (newContent.includes('<details') && newContent.includes('Thinking...') && !currentThinkingMessageId) {
                    setCurrentThinkingMessageId(assistantMessageId);
                  }

                  // 检查thinking是否结束
                  if (currentThinkingMessageId === assistantMessageId && newContent.includes('</details>')) {
                    setCurrentThinkingMessageId(null);
                  }

                  return { ...msg, content: newContent };
                }
                return msg;
              }))
            }
          },
          // 处理完成
          (finalData) => {
            console.log('✅ 流式响应完成:', finalData)
            setIsStreaming(false)
            setIsLoading(false)
            setCurrentThinkingMessageId(null) // 重置thinking状态
          },
          // 传入当前智能体的API Key
          currentAgent?.apiKey
        )
      } catch (streamError) {
        console.warn('⚠️ 流式API失败，尝试阻塞模式:', streamError)
        setIsStreaming(false)
        setCurrentThinkingMessageId(null)

        // 回退到阻塞模式
        try {
          const response = await difyApi.sendMessage(currentInput, 'demo_user', currentAgent?.apiKey)
          console.log('📨 收到阻塞模式响应:', response)

          // 模拟流式效果显示阻塞响应
          setIsStreaming(true) // 开始模拟流式
          const text = response.answer
          let currentIndex = 0

          const typeWriter = () => {
            if (currentIndex < text.length) {
              const chunk = text.slice(0, currentIndex + 1)
              setMessages(prev => prev.map(msg =>
                msg.id === assistantMessageId
                  ? { ...msg, content: chunk }
                  : msg
              ))
              currentIndex++
              setTimeout(typeWriter, 30) // 30ms间隔，模拟打字效果
            } else {
              setIsStreaming(false)
              setIsLoading(false)
              setCurrentThinkingMessageId(null)
            }
          }

          typeWriter()
        } catch (blockingError) {
          console.error('❌ 阻塞模式也失败:', blockingError)
          setIsStreaming(false)
          setCurrentThinkingMessageId(null)
          throw blockingError
        }
      }
    } catch (error) {
      console.error('发送消息失败:', error)
      setIsStreaming(false)
      setCurrentThinkingMessageId(null)

      // 错误处理 - 显示具体错误信息
      const errorMessage = {
        id: Date.now() + 1,
        type: 'assistant' as const,
        content: `❌ **连接失败**\n\n无法连接到智能体服务。\n\n**可能的原因：**\n• API密钥无效或已过期\n• 网络连接问题\n• 服务暂时不可用\n\n**解决方案：**\n• 检查API密钥配置\n• 确认网络连接正常\n• 稍后重试\n\n**错误详情：** ${(error as Error).message}`,
        timestamp: new Date(),
        agent: null,
        components: undefined
      }

      setMessages(prev => [...prev, errorMessage])
      setIsLoading(false)
    }
  }

  // 智能体切换函数
  const handleAgentChange = (agentId: string) => {
    const agent = agents.find(a => a.id === agentId && a.enabled)
    if (agent) {
      // 如果切换到不同的智能体，重置对话
      if (currentAgent && currentAgent.id !== agent.id) {
        console.log('🔄 切换智能体，重置对话:', {
          from: currentAgent.name,
          to: agent.name
        })

        // 重置当前智能体的对话会话
        difyApi.resetConversation(currentAgent.apiKey)

        // 清空消息历史（可选，根据需求决定）
        // setMessages([])
      }

      setCurrentAgent(agent)
      message.success(`已切换到智能体：${agent.name}`)
    }
  }

  // 语音录制
  const handleVoiceRecord = () => {
    setIsRecording(!isRecording)
    // 这里可以集成语音识别API
  }

  // 文件上传
  const handleFileUpload = (file: any) => {
    console.log('上传文件:', file)
    return false // 阻止默认上传
  }

  // 渲染消息组件
  const renderMessageComponent = (component: any) => {
    switch (component.type) {
      case 'chart':
        return (
          <Card size="small" className="mt-2 bg-blue-50 border-blue-200">
            <div className="text-sm font-medium mb-2">{component.title}</div>
            <div className="h-32 bg-gradient-to-r from-blue-100 to-blue-200 rounded flex items-center justify-center">
              <BarChartOutlined className="text-2xl text-blue-600" />
              <span className="ml-2 text-blue-600">图表组件</span>
            </div>
          </Card>
        )
      case 'kpi':
        return (
          <Card size="small" className="mt-2 bg-green-50 border-green-200">
            <div className="text-sm font-medium mb-2">{component.title}</div>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(component.data).map(([key, value]) => (
                <div key={key} className="text-center p-2 bg-white rounded">
                  <div className="text-xs text-gray-500">{key}</div>
                  <div className="font-bold text-green-600">{value as string}</div>
                </div>
              ))}
            </div>
          </Card>
        )
      case 'risk':
        return (
          <Card size="small" className="mt-2 bg-orange-50 border-orange-200">
            <div className="text-sm font-medium mb-2">{component.title}</div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">风险评分</span>
                <Progress 
                  percent={component.data.score} 
                  size="small" 
                  strokeColor={component.data.score > 70 ? '#ff4d4f' : '#fa8c16'}
                />
              </div>
              <div className="flex flex-wrap gap-1">
                {component.data.factors.map((factor: string, index: number) => (
                  <Tag key={index} color="orange">{factor}</Tag>
                ))}
              </div>
            </div>
          </Card>
        )
      default:
        return null
    }
  }

  return (
    <DashboardLayout>
      <style dangerouslySetInnerHTML={{ __html: agentSelectorStyles }} />
      <div className="h-screen flex flex-col bg-gradient-to-br from-slate-50 to-slate-100">
        {/* 顶部标题栏 */}
        <motion.div 
          className="flex items-center justify-between p-6 bg-white shadow-sm border-b"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
              <RobotOutlined className="text-xl text-white" />
            </div>
            <div className="flex items-center space-x-8">
              {/* 左侧标题区域 */}
              <div>
                <h1 className="text-2xl font-bold text-slate-800">财务智能助手</h1>
                <div className="flex items-center space-x-2 mt-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-slate-700">当前智能体</span>
                </div>
              </div>

              {/* 右侧智能体选择器 */}
              <div className="relative flex-1 max-w-2xl">
                <Select
                  value={currentAgent?.id}
                  onChange={handleAgentChange}
                  className="w-full agent-selector"
                  size="large"
                  suffixIcon={<RobotOutlined className="text-blue-500" />}
                  placeholder="请选择智能体"
                  dropdownStyle={{ maxHeight: 600 }}
                  listHeight={600}
                >
                  {getEnabledAgents().map((agent, index) => (
                    <Option key={agent.id} value={agent.id}>
                      <div className="flex items-center justify-between relative">
                        {/* 科技感装饰线 */}
                        <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-cyan-400 via-blue-500 to-purple-600 opacity-20"></div>

                        <div className="flex items-center space-x-4 flex-1 relative">
                          {/* 智能体头像 */}
                          <div className="relative">
                            <div className={`w-10 h-10 rounded-xl flex items-center justify-center shadow-lg transition-all duration-300 ${
                              agent.id === currentAgent?.id
                                ? 'bg-gradient-to-br from-blue-500 to-purple-600 text-white'
                                : 'bg-gradient-to-br from-slate-100 to-slate-200 text-slate-600'
                            }`}>
                              <RobotOutlined className="text-base" />
                            </div>
                            {/* 科技感光环 */}
                            {agent.id === currentAgent?.id && (
                              <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 to-purple-600 rounded-xl opacity-30 animate-pulse"></div>
                            )}
                          </div>

                          {/* 智能体信息 */}
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-1">
                              <span className="font-semibold text-gray-800 text-base">{agent.name}</span>
                              {agent.id === currentAgent?.id && (
                                <div className="flex items-center space-x-1">
                                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                  <Tag color="green" size="small" className="text-xs">ACTIVE</Tag>
                                </div>
                              )}
                              <Tag
                                color={agent.department === '财务部' ? 'blue' :
                                       agent.department === '风控部' ? 'red' :
                                       agent.department === '合规部' ? 'orange' :
                                       agent.department === '投资部' ? 'green' : 'purple'}
                                size="small"
                                className="text-xs font-mono"
                              >
                                {agent.department}
                              </Tag>
                            </div>
                            <div className="text-sm text-gray-600 leading-relaxed">
                              {agent.description}
                            </div>
                          </div>

                          {/* 科技感状态指示器 */}
                          <div className="flex flex-col items-center space-y-1">
                            <div className={`w-3 h-3 rounded-full ${
                              agent.id === currentAgent?.id
                                ? 'bg-green-400 shadow-lg shadow-green-400/50 animate-pulse'
                                : 'bg-gray-300'
                            }`}></div>
                            <div className="text-xs text-gray-400 font-mono">
                              {agent.id === currentAgent?.id ? 'ON' : 'OFF'}
                            </div>
                          </div>
                        </div>

                        {/* 科技感网格背景 */}
                        <div className="absolute inset-0 opacity-5 pointer-events-none">
                          <div className="w-full h-full" style={{
                            backgroundImage: `
                              linear-gradient(rgba(59, 130, 246, 0.3) 1px, transparent 1px),
                              linear-gradient(90deg, rgba(59, 130, 246, 0.3) 1px, transparent 1px)
                            `,
                            backgroundSize: '20px 20px'
                          }}></div>
                        </div>
                      </div>
                    </Option>
                  ))}
                </Select>
                {/* 当前智能体状态指示器 */}
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white shadow-lg animate-pulse"></div>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <Badge count={agents.length} size="small">
              <Button
                type="text"
                className="text-slate-600 hover:text-blue-600 hover:bg-blue-50"
                onClick={() => router.push('/agent-library')}
                icon={<SettingOutlined />}
              >
                智能体库
              </Button>
            </Badge>
            <Button
              type="text"
              onClick={() => setShowInsightPanel(!showInsightPanel)}
              className={`text-slate-600 ${showInsightPanel ? 'bg-blue-50 text-blue-600' : ''}`}
            >
              AI洞察
            </Button>

            {/* 连接状态 */}
            <div className="flex items-center space-x-2">
              {difyConnected === null ? (
                <>
                  <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                  <span className="text-sm text-slate-500">连接测试中...</span>
                </>
              ) : difyConnected ? (
                <>
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm text-slate-500">已连接</span>
                </>
              ) : (
                <>
                  <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                  <span className="text-sm text-slate-500">连接失败</span>
                  <Button
                    size="small"
                    type="link"
                    onClick={handleTestConnection}
                    className="text-xs"
                  >
                    重试
                  </Button>
                </>
              )}
            </div>
          </div>
        </motion.div>

        {/* 聊天区域 */}
        <div className="flex-1 flex overflow-hidden">
          {/* 左侧信息面板 */}
          <div className="w-96 bg-white p-6 overflow-y-auto">
            {/* 快捷操作 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-900 mb-3">💡 快捷咨询</h3>
              <div className="space-y-2">
                {[
                  '分析Q4财务状况',
                  '评估投资风险',
                  '生成财务报告',
                  '检查合规性',
                  '现金流分析'
                ].map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => setInputValue(suggestion)}
                    className="w-full text-left p-2 text-sm bg-gray-50 hover:bg-blue-50 rounded-lg transition-colors text-gray-800 hover:text-blue-700"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>

            {/* 智能体团队 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-900 mb-3">👥 智能体团队</h3>
              <div className="space-y-2">
                {agentTypes.map((agent, index) => (
                  <div key={index} className="flex items-center p-2 bg-gray-50 rounded-lg">
                    <div className="text-lg mr-2" style={{ color: agent.color }}>
                      {agent.icon}
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-900">{agent.name}</div>
                      <div className="text-xs text-gray-500">
                        {index === 0 && '专业财务数据分析'}
                        {index === 1 && '风险识别与评估'}
                        {index === 2 && '智能报告生成'}
                        {index === 3 && '综合财务咨询'}
                        {index === 4 && '合规性检查'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 实时数据 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-900 mb-3">📊 实时数据</h3>
              <div className="space-y-3">
                <div className="p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg">
                  <div className="text-xs text-blue-600 mb-1">今日咨询量</div>
                  <div className="text-lg font-bold text-blue-900">127</div>
                  <div className="text-xs text-blue-600">+12% 较昨日</div>
                </div>
                <div className="p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg">
                  <div className="text-xs text-green-600 mb-1">服务响应时间</div>
                  <div className="text-lg font-bold text-green-900">0.8s</div>
                  <div className="text-xs text-green-600">优秀</div>
                </div>
                <div className="p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg">
                  <div className="text-xs text-purple-600 mb-1">智能体在线</div>
                  <div className="text-lg font-bold text-purple-900">5/5</div>
                  <div className="text-xs text-purple-600">全部就绪</div>
                </div>
              </div>
            </div>
          </div>

          {/* 消息列表 */}
          <div className="flex-1 flex flex-col bg-slate-50">
            <div className="flex-1 overflow-y-auto px-6 py-6 space-y-4">
              <AnimatePresence>
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                    className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} mb-4`}
                  >
                    <div className={`${message.type === 'user' ? 'max-w-lg order-2 mr-8' : 'max-w-full order-1 ml-8'}`}>
                      <div className="flex items-start space-x-3">
                        {message.type !== 'user' && (
                          <Avatar 
                            icon={message.agent ? message.agent.icon : <RobotOutlined />}
                            style={{ 
                              backgroundColor: message.agent ? message.agent.color : '#1890ff' 
                            }}
                          />
                        )}
                        
                        <div className="flex-1">
                          {message.agent && (
                            <div className="text-xs text-slate-500 mb-1">
                              {message.agent.name} 正在为您服务
                            </div>
                          )}
                          
                          <div className={`p-4 rounded-2xl ${
                            message.type === 'user'
                              ? 'bg-blue-500 text-white'
                              : message.type === 'system'
                              ? 'bg-gray-100 text-gray-800'
                              : 'bg-white shadow-md border'
                          }`}>
                            <div className={`leading-relaxed ${
                              message.type === 'user'
                                ? 'text-white text-base'
                                : 'text-gray-900 text-base'
                            }`}>
                              {message.type === 'user' ? (
                                message.content
                              ) : (
                                <div className="markdown-content">
                                  {(() => {
                                    const processed = processThinkingContent(message.content);
                                    const isCurrentlyThinking = currentThinkingMessageId === message.id;

                                    // 如果正在thinking但还没有完整的details标签，特殊处理
                                    if (isCurrentlyThinking && !processed.isThinkingComplete) {
                                      // 提取thinking内容（去掉HTML标签）
                                      const thinkingContent = message.content
                                        .replace(/<details[^>]*>/g, '')
                                        .replace(/<summary[^>]*>.*?<\/summary>/g, '')
                                        .trim();

                                      return (
                                        <details
                                          className="thinking-section"
                                          style={{
                                            color: '#6b7280',
                                            backgroundColor: '#f8f9fa',
                                            padding: '12px',
                                            borderRadius: '8px',
                                            margin: '8px 0',
                                            border: '1px solid #e5e7eb'
                                          }}
                                          open
                                        >
                                          <summary
                                            style={{
                                              fontWeight: '600',
                                              color: '#4b5563',
                                              cursor: 'pointer',
                                              marginBottom: '8px'
                                            }}
                                          >
                                            🤔 AI思考过程
                                          </summary>
                                          <div
                                            style={{
                                              color: '#6b7280',
                                              fontSize: '14px',
                                              lineHeight: '1.6',
                                              whiteSpace: 'pre-wrap'
                                            }}
                                          >
                                            {thinkingContent}
                                          </div>
                                        </details>
                                      );
                                    }

                                    return (
                                      <>
                                        {/* 渲染thinking部分 */}
                                        {processed.hasThinking && processed.isThinkingComplete && (
                                          <details
                                            className="thinking-section"
                                            style={{
                                              color: '#6b7280',
                                              backgroundColor: '#f8f9fa',
                                              padding: '12px',
                                              borderRadius: '8px',
                                              margin: '8px 0',
                                              border: '1px solid #e5e7eb'
                                            }}
                                            open
                                          >
                                            <summary
                                              style={{
                                                fontWeight: '600',
                                                color: '#4b5563',
                                                cursor: 'pointer',
                                                marginBottom: '8px'
                                              }}
                                            >
                                              🤔 AI思考过程
                                            </summary>
                                            <div
                                              style={{
                                                color: '#6b7280',
                                                fontSize: '14px',
                                                lineHeight: '1.6',
                                                whiteSpace: 'pre-wrap'
                                              }}
                                            >
                                              {processed.thinkingContent}
                                            </div>
                                          </details>
                                        )}

                                        {/* 渲染正常内容 */}
                                        {(processed.normalContent || (!processed.hasThinking && message.content)) && (
                                          <ReactMarkdown
                                            remarkPlugins={[remarkGfm]}
                                            rehypePlugins={[rehypeHighlight]}
                                            components={{
                                    // 自定义组件样式
                                    h1: ({children}) => <h1 className="text-xl font-bold mb-3 text-gray-900">{children}</h1>,
                                    h2: ({children}) => <h2 className="text-lg font-bold mb-2 text-gray-900">{children}</h2>,
                                    h3: ({children}) => <h3 className="text-base font-bold mb-2 text-gray-900">{children}</h3>,
                                    p: ({children}) => <p className="mb-2 text-gray-900">{children}</p>,
                                    ul: ({children}) => <ul className="list-disc list-inside mb-2 text-gray-900">{children}</ul>,
                                    ol: ({children}) => <ol className="list-decimal list-inside mb-2 text-gray-900">{children}</ol>,
                                    li: ({children}) => <li className="mb-1 text-gray-900">{children}</li>,
                                    strong: ({children}) => <strong className="font-bold text-gray-900">{children}</strong>,
                                    em: ({children}) => <em className="italic text-gray-900">{children}</em>,
                                    code: ({children}) => <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono text-gray-900">{children}</code>,
                                    pre: ({children}) => <pre className="bg-gray-100 p-3 rounded-lg overflow-x-auto mb-2">{children}</pre>,
                                    blockquote: ({children}) => <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-700 mb-2">{children}</blockquote>,
                                    table: ({children}) => <table className="border-collapse border border-gray-300 mb-2">{children}</table>,
                                    th: ({children}) => <th className="border border-gray-300 px-2 py-1 bg-gray-100 font-bold text-gray-900">{children}</th>,
                                    td: ({children}) => <td className="border border-gray-300 px-2 py-1 text-gray-900">{children}</td>,
                                  }}
                                            >
                                              {processed.normalContent || (!processed.hasThinking ? message.content : '')}
                                            </ReactMarkdown>
                                          )}
                                        </>
                                      );
                                    })()}
                                </div>
                              )}
                            </div>
                            
                            {/* 渲染组件 */}
                            {/* {message.components?.map((component: any, index: number) => (
                              <div key={index}>
                                {renderMessageComponent(component)}
                              </div>
                            ))} */}
                          </div>
                          
                          <div className="text-xs text-slate-500 mt-1">
                            {message.timestamp.toLocaleTimeString('zh-CN')}
                          </div>
                        </div>
                        
                        {message.type === 'user' && (
                          <Avatar icon={<UserOutlined />} className="bg-slate-400" />
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
              
              {/* 加载指示器 - 只在非流式状态下显示 */}
              {isLoading && !isStreaming && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex justify-start"
                >
                  <div className="flex items-center space-x-3">
                    <Avatar icon={<RobotOutlined />} className="bg-blue-500" />
                    <div className="bg-white p-4 rounded-2xl shadow-md border">
                      <div className="flex items-center space-x-2">
                        <LoadingOutlined className="text-blue-500" />
                        <span className="text-base text-gray-800">AI正在思考中...</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* 输入区域 */}
            <motion.div
              className="px-6 py-4 bg-slate-50"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="w-full">
              <div className="flex items-end space-x-3">
                <div className="flex-1">
                  <TextArea
                    ref={inputRef}
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    placeholder="请输入您的问题，我来为您提供专业的财务分析..."
                    autoSize={{ minRows: 1, maxRows: 4 }}
                    onPressEnter={(e) => {
                      if (!e.shiftKey) {
                        e.preventDefault()
                        handleSendMessage()
                      }
                    }}
                    className="resize-none text-gray-900"
                    styles={{
                      textarea: {
                        color: '#111827 !important',
                        fontSize: '16px',
                        backgroundColor: 'transparent'
                      }
                    }}
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <Upload beforeUpload={handleFileUpload} showUploadList={false}>
                    <Tooltip title="上传文件">
                      <Button 
                        type="text" 
                        icon={<PaperClipOutlined />}
                        className="text-slate-500 hover:text-blue-500"
                      />
                    </Tooltip>
                  </Upload>
                  
                  <Tooltip title={isRecording ? "停止录音" : "语音输入"}>
                    <Button
                      type="text"
                      icon={<AudioOutlined />}
                      onClick={handleVoiceRecord}
                      className={`${isRecording ? 'text-red-500 animate-pulse' : 'text-slate-500 hover:text-blue-500'}`}
                    />
                  </Tooltip>
                  
                  <Button 
                    type="primary"
                    icon={<SendOutlined />}
                    onClick={handleSendMessage}
                    loading={isLoading}
                    disabled={!inputValue.trim()}
                    className="bg-blue-500 hover:bg-blue-600"
                  >
                    发送
                  </Button>
                </div>
              </div>
              
              {/* 快速操作 */}
              <div className="flex items-center justify-between mt-3">
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-slate-400">快速操作:</span>
                  {['财务分析', '风险评估', '生成报告', '投资建议'].map((action) => (
                    <Button
                      key={action}
                      size="small"
                      type="text"
                      onClick={() => setInputValue(action)}
                      className="text-xs text-blue-500 hover:bg-blue-50"
                    >
                      {action}
                    </Button>
                  ))}
                </div>

                {/* 智能体服务状态指示 */}
                <div className="flex items-center space-x-2 text-xs">
                  <span className="text-slate-400">服务状态:</span>
                  {difyConnected === null ? (
                    <span className="text-yellow-500">智能体服务测试中...</span>
                  ) : difyConnected ? (
                    <span className="text-green-500">✓ 智能体服务已连接</span>
                  ) : (
                    <span className="text-red-500">✗ 智能体服务连接失败</span>
                  )}
                </div>
              </div>
              </div>
            </motion.div>
          </div>

          {/* 右侧信息面板 */}
          <div className="w-96 bg-white p-6 overflow-y-auto">
            {/* 会话历史 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-900 mb-3">📝 最近会话</h3>
              <div className="space-y-2">
                {[
                  { title: '财务风险评估报告', time: '2小时前', type: '风险分析' },
                  { title: 'Q4投资建议咨询', time: '4小时前', type: '投资咨询' },
                  { title: '现金流量表分析', time: '昨天', type: '财务分析' },
                  { title: '合规性检查结果', time: '2天前', type: '合规检查' },
                  { title: '年度财务报告', time: '3天前', type: '报告生成' }
                ].map((session, index) => (
                  <div key={index} className="p-2 bg-gray-50 hover:bg-gray-100 rounded-lg cursor-pointer transition-colors">
                    <div className="text-sm font-medium text-gray-900 truncate">{session.title}</div>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">{session.type}</span>
                      <span className="text-xs text-gray-500">{session.time}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 知识库 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-900 mb-3">📚 知识库</h3>
              <div className="space-y-2">
                {[
                  { title: '财务分析指南', category: '分析方法' },
                  { title: '风险管理手册', category: '风险控制' },
                  { title: '投资决策框架', category: '投资指导' },
                  { title: '合规操作规范', category: '合规管理' },
                  { title: '报告模板库', category: '文档模板' }
                ].map((doc, index) => (
                  <div key={index} className="p-2 bg-gray-50 hover:bg-blue-50 rounded-lg cursor-pointer transition-colors">
                    <div className="text-sm font-medium text-gray-900">{doc.title}</div>
                    <div className="text-xs text-gray-500 mt-1">{doc.category}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* 市场动态 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-900 mb-3">📈 市场动态</h3>
              <div className="space-y-3">
                <div className="p-3 bg-gradient-to-r from-red-50 to-red-100 rounded-lg">
                  <div className="text-xs text-red-600 mb-1">上证指数</div>
                  <div className="text-lg font-bold text-red-900">3,247.52</div>
                  <div className="text-xs text-red-600">-1.2% ↓</div>
                </div>
                <div className="p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg">
                  <div className="text-xs text-green-600 mb-1">美元汇率</div>
                  <div className="text-lg font-bold text-green-900">7.23</div>
                  <div className="text-xs text-green-600">+0.3% ↑</div>
                </div>
                <div className="p-3 bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg">
                  <div className="text-xs text-yellow-600 mb-1">黄金价格</div>
                  <div className="text-lg font-bold text-yellow-900">$2,045</div>
                  <div className="text-xs text-yellow-600">+0.8% ↑</div>
                </div>
              </div>
            </div>

            {/* 快捷工具 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-900 mb-3">🛠️ 快捷工具</h3>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { name: '计算器', icon: '🧮' },
                  { name: '汇率转换', icon: '💱' },
                  { name: '财务模板', icon: '📊' },
                  { name: '风险测评', icon: '⚠️' }
                ].map((tool, index) => (
                  <button
                    key={index}
                    className="p-2 bg-gray-50 hover:bg-blue-50 rounded-lg text-center transition-colors"
                  >
                    <div className="text-lg mb-1">{tool.icon}</div>
                    <div className="text-xs text-gray-700">{tool.name}</div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* AI洞察面板 */}
        <InsightPanel
          visible={showInsightPanel}
          onClose={() => setShowInsightPanel(false)}
        />


      </div>
    </DashboardLayout>
  )
}
