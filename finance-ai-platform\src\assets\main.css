@import './base.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义全局样式 */
@layer base {
  html {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  body {
    @apply bg-slate-50 text-slate-900;
  }
}

@layer components {
  /* 财务平台专用组件样式 */
  .finance-card {
    @apply bg-white rounded-xl shadow-lg border border-slate-200 p-6 transition-all duration-300 hover:shadow-xl;
  }

  .finance-button {
    @apply px-6 py-3 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .finance-button-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .finance-button-secondary {
    @apply bg-slate-200 text-slate-700 hover:bg-slate-300 focus:ring-slate-500;
  }

  .finance-input {
    @apply w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  }

  .finance-gradient {
    @apply bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800;
  }

  .finance-glass {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }
}

@layer utilities {
  /* 动画工具类 */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-gradient {
    animation: gradient 15s ease infinite;
    background-size: 200% 200%;
  }

  /* 文本渐变 */
  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }

  /* 阴影效果 */
  .shadow-finance {
    box-shadow: 0 10px 25px -3px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
  }

  /* 滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(148 163 184) rgb(241 245 249);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(241 245 249);
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(148 163 184);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(100 116 139);
  }
}

/* 全局应用样式 */
#app {
  @apply min-h-screen;
}

/* 粒子背景动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes gradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .finance-card {
    @apply p-4;
  }
}
