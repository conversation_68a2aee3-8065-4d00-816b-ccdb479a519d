# Windows PowerShell部署脚本
# 使用方法: .\deploy-windows.ps1

param(
    [Parameter(Mandatory=$false)]
    [string]$Mode = "local"  # local 或 server
)

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🚀 开始部署吉发集团财务AI平台 (Windows)" -ForegroundColor Green
Write-Host "📍 部署模式: $Mode" -ForegroundColor Yellow

# 检查Docker是否安装
Write-Host "🔍 检查Docker环境..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "✅ Docker已安装: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker未安装或未启动" -ForegroundColor Red
    Write-Host "💡 请先安装Docker Desktop for Windows" -ForegroundColor Yellow
    Write-Host "   下载地址: https://www.docker.com/products/docker-desktop/" -ForegroundColor White
    exit 1
}

# 检查Docker Compose
try {
    $composeVersion = docker-compose --version
    Write-Host "✅ Docker Compose已安装: $composeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose未安装" -ForegroundColor Red
    exit 1
}

# 检查智能体数据文件
Write-Host "📊 检查智能体数据..." -ForegroundColor Yellow
if (Test-Path ".\data\agents.json") {
    $agentData = Get-Content ".\data\agents.json" | ConvertFrom-Json
    $agentCount = $agentData.agents.Count
    Write-Host "✅ 检测到 $agentCount 个智能体配置" -ForegroundColor Green
} else {
    Write-Host "❌ 智能体数据文件不存在: .\data\agents.json" -ForegroundColor Red
    Write-Host "请确保已配置好智能体数据" -ForegroundColor Yellow
    exit 1
}

# 停止现有容器
Write-Host "🛑 停止现有容器..." -ForegroundColor Yellow
try {
    docker-compose down --remove-orphans 2>$null
    Write-Host "✅ 现有容器已停止" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 没有运行中的容器" -ForegroundColor Yellow
}

# 询问是否清理旧镜像
$cleanImages = Read-Host "是否清理旧的Docker镜像? (y/N)"
if ($cleanImages -eq "y" -or $cleanImages -eq "Y") {
    Write-Host "🧹 清理旧镜像..." -ForegroundColor Yellow
    docker system prune -f
    Write-Host "✅ 镜像清理完成" -ForegroundColor Green
}

# 构建新镜像
Write-Host "🔨 构建Docker镜像..." -ForegroundColor Yellow
try {
    docker-compose build --no-cache
    Write-Host "✅ 镜像构建成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 镜像构建失败" -ForegroundColor Red
    Write-Host "请检查Dockerfile和网络连接" -ForegroundColor Yellow
    exit 1
}

# 启动服务
Write-Host "🚀 启动服务..." -ForegroundColor Yellow
try {
    if ($Mode -eq "server") {
        docker-compose -f docker-compose.prod.yml up -d
    } else {
        docker-compose up -d
    }
    Write-Host "✅ 服务启动成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 服务启动失败" -ForegroundColor Red
    exit 1
}

# 等待服务启动
Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 检查服务状态
Write-Host "🔍 检查服务状态..." -ForegroundColor Yellow
try {
    docker-compose ps
    Write-Host "✅ 服务状态检查完成" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 无法获取服务状态" -ForegroundColor Yellow
}

# 健康检查
Write-Host "🏥 执行健康检查..." -ForegroundColor Yellow
$maxRetries = 10
$retryCount = 0
$healthCheckPassed = $false

while ($retryCount -lt $maxRetries -and -not $healthCheckPassed) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5666/api/health" -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            $healthCheckPassed = $true
            Write-Host "✅ 健康检查通过" -ForegroundColor Green
        }
    } catch {
        $retryCount++
        Write-Host "⏳ 等待服务启动... ($retryCount/$maxRetries)" -ForegroundColor Yellow
        Start-Sleep -Seconds 5
    }
}

if (-not $healthCheckPassed) {
    Write-Host "❌ 健康检查失败，请检查日志" -ForegroundColor Red
    Write-Host "查看日志命令: docker-compose logs" -ForegroundColor White
}

# 显示访问信息
Write-Host ""
Write-Host "🎉 部署完成！" -ForegroundColor Green
Write-Host "📱 访问地址: http://localhost:5666" -ForegroundColor Yellow
Write-Host "🏥 健康检查: http://localhost:5666/api/health" -ForegroundColor Yellow
Write-Host "📊 智能体管理: http://localhost:5666/agent-library" -ForegroundColor Yellow
Write-Host "💬 智能对话: http://localhost:5666/chat" -ForegroundColor Yellow
Write-Host ""
Write-Host "📋 常用命令:" -ForegroundColor Cyan
Write-Host "  查看日志: docker-compose logs -f" -ForegroundColor White
Write-Host "  停止服务: docker-compose down" -ForegroundColor White
Write-Host "  重启服务: docker-compose restart" -ForegroundColor White
Write-Host "  查看状态: docker-compose ps" -ForegroundColor White
Write-Host ""
Write-Host "🔧 如需修改配置，请编辑 docker-compose.yml 文件" -ForegroundColor Gray

# 询问是否打开浏览器
$openBrowser = Read-Host "是否打开浏览器访问应用? (Y/n)"
if ($openBrowser -ne "n" -and $openBrowser -ne "N") {
    Start-Process "http://localhost:5666"
}
