#!/bin/bash

# Docker构建测试脚本
echo "🧪 测试Docker构建..."

# 检查必要文件
echo "📋 检查必要文件..."
files=("Dockerfile" "docker-compose.yml" "data/agents.json" "package.json")
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 不存在"
        exit 1
    fi
done

# 检查智能体数量
AGENT_COUNT=$(grep -c '"id":' ./data/agents.json)
echo "📊 智能体数量: $AGENT_COUNT"

if [ "$AGENT_COUNT" -eq 20 ]; then
    echo "✅ 智能体数据完整"
else
    echo "❌ 智能体数据不完整，期望20个，实际$AGENT_COUNT个"
    exit 1
fi

# 测试Docker构建（不启动）
echo "🔨 测试Docker镜像构建..."
if docker-compose build --no-cache; then
    echo "✅ Docker镜像构建成功"
else
    echo "❌ Docker镜像构建失败"
    exit 1
fi

echo "🎉 Docker构建测试通过！"
echo "💡 运行 './deploy.sh' 开始完整部署"
