# 吉发集团财务智能体平台 - 项目启动说明

## 📋 项目概述

**项目名称**: 吉发集团财务智能体平台  
**项目版本**: v1.0.0  
**开发框架**: Next.js 15 + React 18 + TypeScript  
**UI框架**: Ant Design + Tailwind CSS  
**AI集成**: Dify平台 + 19个专业财务智能体  

## 🚀 快速启动

### 环境要求

- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0 或 **yarn**: >= 1.22.0
- **操作系统**: Windows 10/11, macOS 10.15+, Linux Ubuntu 18.04+

### 安装步骤

#### 1. 克隆项目
```bash
git clone [项目仓库地址]
cd finance-ai-demo
```

#### 2. 安装依赖
```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

#### 3. 环境配置
创建 `.env.local` 文件并配置以下环境变量：
```env
# Dify AI平台配置
NEXT_PUBLIC_DIFY_API_URL=https://ai.hongtai-idi.com/v1
NEXT_PUBLIC_DIFY_API_KEY=app-xuReGCaYZmkWMn0JGAAxqBvg

# 应用配置
NEXT_PUBLIC_APP_NAME=财务智能体平台
NEXT_PUBLIC_COMPANY_NAME=吉发集团

# 开发环境配置
NODE_ENV=development
```

#### 4. 启动开发服务器
```bash
# 使用 npm
npm run dev

# 或使用 yarn
yarn dev
```

#### 5. 访问应用
打开浏览器访问: [http://localhost:3000](http://localhost:3000)

## 📁 项目结构

```
finance-ai-demo/
├── src/
│   ├── app/                    # Next.js 13+ App Router
│   │   ├── dashboard/          # 高管仪表板
│   │   ├── chat/              # 智能助手聊天
│   │   ├── insight-studio/    # 财务分析中心
│   │   ├── document-lab/      # 智能审核平台
│   │   ├── simulation/        # 风险建模系统
│   │   ├── boardroom/         # 决策支持系统
│   │   ├── layout.tsx         # 全局布局
│   │   └── page.tsx           # 首页
│   ├── components/            # 共享组件
│   │   ├── DashboardLayout.tsx # 仪表板布局
│   │   └── ui/                # UI组件
│   ├── lib/                   # 工具库
│   │   ├── dify.ts           # Dify API集成
│   │   └── utils.ts          # 工具函数
│   └── styles/               # 样式文件
├── public/                   # 静态资源
├── package.json             # 项目配置
├── tailwind.config.js       # Tailwind配置
├── next.config.js          # Next.js配置
└── README.md               # 项目说明
```

## 🎯 核心功能模块

### 1. 高管仪表板 (`/dashboard`)
- **功能**: 财务KPI实时监控、数据可视化
- **特色**: 6大核心指标、动态图表、智能告警
- **技术**: Chart.js、实时数据更新、响应式设计

### 2. 智能助手 (`/chat`)
- **功能**: 19个专业财务智能体对话
- **特色**: 流式对话、Markdown渲染、文件上传
- **技术**: Dify API集成、WebSocket、实时通信

### 3. 财务分析中心 (`/insight-studio`)
- **功能**: 创建洞察、趋势分析、数据建模、故事画布
- **特色**: 拖拽式组件、多格式导出、协同工作
- **技术**: React DnD、Canvas API、数据可视化

### 4. 智能审核平台 (`/document-lab`)
- **功能**: 票据OCR识别、异常检测、批量处理
- **特色**: AI智能审核、实时处理、审计轨迹
- **技术**: OCR技术、机器学习、文件处理

### 5. 风险建模系统 (`/simulation`)
- **功能**: 3D风险可视化、情景分析、投资建议
- **特色**: 立体展示、实时市场数据、AI优化
- **技术**: Three.js、WebGL、金融建模

### 6. 决策支持系统 (`/boardroom`)
- **功能**: 会议管理、演示模式、决议投票
- **特色**: 全屏演示、实时协作、专业幻灯片
- **技术**: 全屏API、实时同步、交互设计

## 🔧 开发指南

### 代码规范
- **TypeScript**: 严格类型检查
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Husky**: Git钩子管理

### 组件开发
```typescript
// 组件示例
import React from 'react';
import { Card } from 'antd';

interface ComponentProps {
  title: string;
  data: any[];
}

export const CustomComponent: React.FC<ComponentProps> = ({ title, data }) => {
  return (
    <Card title={title}>
      {/* 组件内容 */}
    </Card>
  );
};
```

### API集成
```typescript
// Dify API调用示例
import { difyService } from '@/lib/dify';

const response = await difyService.chat({
  query: '分析Q4财务状况',
  conversation_id: 'uuid',
  user: 'user_id'
});
```

## 🎨 UI/UX设计规范

### 设计原则
- **科技感**: 深色主题、渐变效果、动画交互
- **专业性**: 企业级设计、数据可视化、清晰层次
- **易用性**: 直观操作、响应式布局、无障碍设计

### 色彩规范
```css
/* 主色调 */
--primary-blue: #1890ff;
--primary-purple: #722ed1;
--primary-green: #52c41a;

/* 渐变色 */
--gradient-blue: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
--gradient-purple: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

/* 状态色 */
--success: #52c41a;
--warning: #faad14;
--error: #ff4d4f;
--info: #1890ff;
```

### 动画规范
```css
/* 过渡动画 */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果 */
.hover-scale {
  transform: scale(1.05);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}
```

## 📦 构建部署

### 开发环境
```bash
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run lint         # 代码检查
npm run type-check   # 类型检查
```

### 生产部署
```bash
# 1. 构建项目
npm run build

# 2. 启动生产服务器
npm run start

# 3. 使用PM2管理进程
pm2 start npm --name "finance-ai-demo" -- start
```

### Docker部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🔍 测试指南

### 单元测试
```bash
npm run test         # 运行测试
npm run test:watch   # 监听模式
npm run test:coverage # 覆盖率报告
```

### E2E测试
```bash
npm run e2e          # 端到端测试
npm run e2e:headless # 无头模式测试
```

### 性能测试
```bash
npm run lighthouse   # 性能分析
npm run bundle-analyzer # 包大小分析
```

## 🐛 故障排除

### 常见问题

#### 1. 依赖安装失败
```bash
# 清除缓存重新安装
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

#### 2. 端口占用
```bash
# 查找占用进程
lsof -i :3000
# 杀死进程
kill -9 [PID]
```

#### 3. TypeScript错误
```bash
# 重新生成类型文件
npm run type-check
# 重启TypeScript服务
Ctrl+Shift+P -> "TypeScript: Restart TS Server"
```

#### 4. Dify API连接失败
- 检查网络连接
- 验证API密钥
- 确认API地址正确
- 查看控制台错误信息

### 日志调试
```typescript
// 开启调试模式
localStorage.setItem('debug', 'finance-ai:*');

// 查看网络请求
// 打开浏览器开发者工具 -> Network标签
```

## 📞 技术支持

### 联系方式
- **技术负责人**: [姓名] - [邮箱] - [电话]
- **项目经理**: [姓名] - [邮箱] - [电话]
- **产品经理**: [姓名] - [邮箱] - [电话]

### 文档资源
- **API文档**: [链接]
- **设计规范**: [链接]
- **部署指南**: [链接]
- **更新日志**: [链接]

### 社区支持
- **GitHub Issues**: [仓库地址]/issues
- **技术论坛**: [论坛地址]
- **微信群**: [群二维码]

## 📈 版本更新

### 当前版本: v1.0.0
- ✅ 完成六大核心功能模块
- ✅ 集成19个专业财务智能体
- ✅ 实现响应式设计和动画效果
- ✅ 完成Dify API集成
- ✅ 优化用户体验和界面设计

### 计划更新
- 🔄 v1.1.0: 增加移动端适配
- 🔄 v1.2.0: 新增数据导出功能
- 🔄 v1.3.0: 集成更多第三方系统
- 🔄 v2.0.0: 重构架构，性能优化

---

## 🎉 开始使用

恭喜！您已经成功了解了项目的启动流程。现在可以开始探索吉发集团财务智能体平台的强大功能了。

如果在使用过程中遇到任何问题，请随时联系技术支持团队。祝您使用愉快！

**最后更新**: 2024年12月28日  
**文档版本**: v1.0.0
