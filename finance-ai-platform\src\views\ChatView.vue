<template>
  <div class="min-h-screen bg-slate-50 flex">
    <!-- 左侧智能体列表 -->
    <aside class="w-80 bg-white shadow-sm border-r border-slate-200">
      <div class="p-4 border-b border-slate-200">
        <h2 class="text-lg font-bold text-slate-900">财务智能体</h2>
        <p class="text-sm text-slate-500">选择专业智能体协助您的工作</p>
      </div>
      
      <div class="p-4">
        <a-input
          v-model:value="searchAgent"
          placeholder="搜索智能体..."
          class="mb-4"
        >
          <template #prefix>
            <SearchOutlined class="text-slate-400" />
          </template>
        </a-input>
        
        <div class="space-y-2">
          <div
            v-for="category in agentCategories"
            :key="category.name"
            class="mb-4"
          >
            <h3 class="text-sm font-medium text-slate-700 mb-2">{{ category.name }}</h3>
            <div class="space-y-1">
              <div
                v-for="agent in category.agents"
                :key="agent.id"
                class="p-3 rounded-lg cursor-pointer transition-all duration-200 hover:bg-blue-50"
                :class="{ 'bg-blue-50 border border-blue-200': selectedAgent?.id === agent.id }"
                @click="selectAgent(agent)"
              >
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 rounded-lg flex items-center justify-center" :class="agent.bgColor">
                    <component :is="agent.icon" class="w-4 h-4" :class="agent.iconColor" />
                  </div>
                  <div class="flex-1">
                    <h4 class="text-sm font-medium text-slate-900">{{ agent.name }}</h4>
                    <p class="text-xs text-slate-500">{{ agent.description }}</p>
                  </div>
                  <div v-if="agent.isActive" class="w-2 h-2 bg-green-500 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </aside>

    <!-- 主聊天区域 -->
    <main class="flex-1 flex flex-col">
      <!-- 聊天头部 -->
      <header class="bg-white border-b border-slate-200 p-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div v-if="selectedAgent" class="w-10 h-10 rounded-lg flex items-center justify-center" :class="selectedAgent.bgColor">
              <component :is="selectedAgent.icon" class="w-5 h-5" :class="selectedAgent.iconColor" />
            </div>
            <div>
              <h1 class="text-lg font-bold text-slate-900">
                {{ selectedAgent ? selectedAgent.name : '财务智能助手' }}
              </h1>
              <p class="text-sm text-slate-500">
                {{ selectedAgent ? selectedAgent.description : '请选择一个智能体开始对话' }}
              </p>
            </div>
          </div>
          
          <div class="flex items-center space-x-2">
            <!-- 功能标签 -->
            <div v-if="activeTags.length > 0" class="flex space-x-1">
              <a-tag
                v-for="tag in activeTags"
                :key="tag"
                color="blue"
                closable
                @close="removeTag(tag)"
              >
                #{{ tag }}
              </a-tag>
            </div>
            
            <!-- 记忆芯片 -->
            <a-tooltip title="清空对话记忆">
              <a-button size="small" @click="clearMemory">
                <DeleteOutlined />
              </a-button>
            </a-tooltip>
          </div>
        </div>
      </header>

      <!-- 聊天消息区域 -->
      <div class="flex-1 overflow-hidden flex">
        <!-- 消息列表 -->
        <div class="flex-1 flex flex-col">
          <div ref="messagesContainer" class="flex-1 overflow-y-auto p-4 space-y-4">
            <div
              v-for="message in messages"
              :key="message.id"
              class="flex"
              :class="message.type === 'user' ? 'justify-end' : 'justify-start'"
            >
              <div
                class="max-w-3xl rounded-lg p-4"
                :class="message.type === 'user' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-white shadow-sm border border-slate-200'"
              >
                <div v-if="message.type === 'assistant'" class="flex items-center space-x-2 mb-2">
                  <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                    <RobotOutlined class="w-3 h-3 text-blue-600" />
                  </div>
                  <span class="text-sm font-medium text-slate-700">{{ message.agentName }}</span>
                </div>
                
                <div class="prose prose-sm max-w-none" v-html="message.content"></div>
                
                <!-- 附加组件 -->
                <div v-if="message.components" class="mt-4 space-y-3">
                  <div
                    v-for="component in message.components"
                    :key="component.id"
                    class="border border-slate-200 rounded-lg p-3"
                  >
                    <component :is="component.type" v-bind="component.props" />
                  </div>
                </div>
                
                <div class="text-xs opacity-70 mt-2">
                  {{ formatTime(message.timestamp) }}
                </div>
              </div>
            </div>
            
            <!-- 加载指示器 -->
            <div v-if="isLoading" class="flex justify-start">
              <div class="bg-white shadow-sm border border-slate-200 rounded-lg p-4">
                <div class="flex items-center space-x-2">
                  <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                    <RobotOutlined class="w-3 h-3 text-blue-600" />
                  </div>
                  <span class="text-sm text-slate-700">正在思考...</span>
                  <div class="flex space-x-1">
                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="border-t border-slate-200 bg-white p-4">
            <div class="flex items-end space-x-3">
              <!-- 文件上传 -->
              <a-upload
                :show-upload-list="false"
                :before-upload="handleFileUpload"
                accept=".pdf,.xlsx,.xls,.csv,.png,.jpg,.jpeg"
              >
                <a-button size="large" class="h-12">
                  <PaperClipOutlined />
                </a-button>
              </a-upload>
              
              <!-- 语音输入 -->
              <a-button
                size="large"
                class="h-12"
                :class="{ 'bg-red-500 text-white': isRecording }"
                @click="toggleRecording"
              >
                <AudioOutlined />
              </a-button>
              
              <!-- 文本输入 -->
              <div class="flex-1">
                <a-textarea
                  v-model:value="inputMessage"
                  placeholder="输入您的问题，或拖拽文件到此处..."
                  :rows="1"
                  :auto-size="{ minRows: 1, maxRows: 4 }"
                  class="resize-none"
                  @keydown.enter.exact="handleSend"
                  @keydown.enter.shift.exact.prevent="inputMessage += '\n'"
                />
              </div>
              
              <!-- 发送按钮 -->
              <a-button
                type="primary"
                size="large"
                class="h-12 px-6"
                :disabled="!inputMessage.trim() || isLoading"
                @click="handleSend"
              >
                <SendOutlined />
              </a-button>
            </div>
            
            <!-- 快速操作 -->
            <div class="mt-3 flex flex-wrap gap-2">
              <a-button
                v-for="suggestion in quickSuggestions"
                :key="suggestion"
                size="small"
                @click="inputMessage = suggestion"
              >
                {{ suggestion }}
              </a-button>
            </div>
          </div>
        </div>

        <!-- 右侧洞察面板 -->
        <aside v-if="showInsightPanel" class="w-96 bg-white border-l border-slate-200 p-4">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-bold text-slate-900">实时洞察</h3>
            <a-button size="small" @click="showInsightPanel = false">
              <CloseOutlined />
            </a-button>
          </div>
          
          <div class="space-y-4">
            <!-- 动态图表组件 -->
            <div v-for="insight in insights" :key="insight.id" class="border border-slate-200 rounded-lg p-3">
              <h4 class="font-medium text-slate-900 mb-2">{{ insight.title }}</h4>
              <component :is="insight.component" v-bind="insight.props" />
            </div>
          </div>
        </aside>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useAgentStore } from '@/stores/agentStore'
import {
  SearchOutlined,
  RobotOutlined,
  DeleteOutlined,
  PaperClipOutlined,
  AudioOutlined,
  SendOutlined,
  CloseOutlined,
  BarChartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  DollarOutlined,
  AlertOutlined,
  FileTextOutlined,
  CalculatorOutlined,
  BankOutlined,
  SafetyOutlined,
  BookOutlined,
  MessageOutlined,
  SoundOutlined,
  BulbOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const router = useRouter()
const agentStore = useAgentStore()
const searchAgent = ref('')
const selectedAgent = ref<any>(null)
const inputMessage = ref('')
const isLoading = ref(false)
const isRecording = ref(false)
const showInsightPanel = ref(false)
const messagesContainer = ref<HTMLDivElement>()
const messages = ref<any[]>([])
const activeTags = ref<string[]>([])
const insights = ref<any[]>([])

// 从store获取智能体数据
const agentCategories = computed(() => {
  const grouped: Record<string, any[]> = {}
  agentStore.enabledAgents.forEach(agent => {
    if (!grouped[agent.category]) {
      grouped[agent.category] = []
    }
    grouped[agent.category].push({
      id: agent.id,
      name: agent.name,
      description: agent.description,
      icon: getAgentIcon(agent.appType),
      bgColor: getAgentIconBg(agent.appType),
      iconColor: getAgentIconColor(agent.appType),
      isActive: true
    })
  })

  return Object.entries(grouped).map(([name, agents]) => ({
    name,
    agents
  }))
})

// 辅助函数
const getAgentIcon = (appType: string) => {
  switch (appType) {
    case 'chatflow': return BarChartOutlined
    case 'workflow': return LineChartOutlined
    case 'completion': return PieChartOutlined
    default: return MessageOutlined
  }
}

const getAgentIconBg = (appType: string) => {
  switch (appType) {
    case 'chatflow': return 'bg-blue-100'
    case 'workflow': return 'bg-green-100'
    case 'completion': return 'bg-purple-100'
    default: return 'bg-slate-100'
  }
}

const getAgentIconColor = (appType: string) => {
  switch (appType) {
    case 'chatflow': return 'text-blue-600'
    case 'workflow': return 'text-green-600'
    case 'completion': return 'text-purple-600'
    default: return 'text-slate-600'
  }
}

// 原有的智能体分类（作为fallback）
const defaultAgentCategories = ref([
  {
    name: '经营分析与预测',
    agents: [
      {
        id: 'financial-insight',
        name: '财务报表洞察',
        description: '一键生成报表摘要和图表',
        icon: BarChartOutlined,
        bgColor: 'bg-blue-100',
        iconColor: 'text-blue-600',
        isActive: true
      },
      {
        id: 'budget-planning',
        name: '智能预算编制',
        description: '基于历史数据的预算预测',
        icon: LineChartOutlined,
        bgColor: 'bg-green-100',
        iconColor: 'text-green-600',
        isActive: true
      },
      {
        id: 'cashflow-forecast',
        name: '现金流预测',
        description: '13周滚动现金流分析',
        icon: DollarOutlined,
        bgColor: 'bg-emerald-100',
        iconColor: 'text-emerald-600',
        isActive: true
      }
    ]
  },
  {
    name: '成本与运营管控',
    agents: [
      {
        id: 'cost-copilot',
        name: '成本算法助手',
        description: '产品成本分摊和敏感性分析',
        icon: CalculatorOutlined,
        bgColor: 'bg-orange-100',
        iconColor: 'text-orange-600',
        isActive: true
      },
      {
        id: 'expense-audit',
        name: '费用报销审核',
        description: 'OCR识别和合规性检查',
        icon: FileTextOutlined,
        bgColor: 'bg-purple-100',
        iconColor: 'text-purple-600',
        isActive: true
      }
    ]
  },
  {
    name: '风险合规与内控',
    agents: [
      {
        id: 'risk-radar',
        name: '财务风险雷达',
        description: '实时监控财务风险指标',
        icon: AlertOutlined,
        bgColor: 'bg-red-100',
        iconColor: 'text-red-600',
        isActive: true
      },
      {
        id: 'tax-planning',
        name: '税务筹划助手',
        description: '税负优化和申报协助',
        icon: BankOutlined,
        bgColor: 'bg-indigo-100',
        iconColor: 'text-indigo-600',
        isActive: true
      }
    ]
  },
  {
    name: '智能问答与助手',
    agents: [
      {
        id: 'knowledge-qa',
        name: '财务制度问答',
        description: 'RAG检索财务制度文档',
        icon: BookOutlined,
        bgColor: 'bg-teal-100',
        iconColor: 'text-teal-600',
        isActive: true
      },
      {
        id: 'chat-ops',
        name: 'Chat-Ops助手',
        description: '指令式智能体调度',
        icon: MessageOutlined,
        bgColor: 'bg-cyan-100',
        iconColor: 'text-cyan-600',
        isActive: true
      }
    ]
  }
])

// 快速建议
const quickSuggestions = ref([
  '生成2024Q4财务报表摘要',
  '预测下季度现金流',
  '分析当前风险指标',
  '查询固定资产折旧政策'
])

// 方法
const selectAgent = (agent: any) => {
  selectedAgent.value = agent
  messages.value = []
  activeTags.value = []

  // 创建新对话
  agentStore.createNewConversation(agent.id)

  // 添加欢迎消息
  messages.value.push({
    id: Date.now(),
    type: 'assistant',
    agentName: agent.name,
    content: `您好！我是${agent.name}，${agent.description}。请告诉我您需要什么帮助？`,
    timestamp: new Date(),
    components: []
  })

  scrollToBottom()
}

const handleSend = async () => {
  if (!inputMessage.value.trim() || isLoading.value || !selectedAgent.value) return

  const userMessage = {
    id: Date.now(),
    type: 'user',
    content: inputMessage.value,
    timestamp: new Date()
  }

  messages.value.push(userMessage)
  const currentInput = inputMessage.value
  inputMessage.value = ''
  isLoading.value = true

  scrollToBottom()

  try {
    // 使用真实的Dify API
    await agentStore.sendMessageToAgent(selectedAgent.value.id, currentInput)

    // 获取最新的对话消息
    const conversation = agentStore.conversations.find(c => c.agentId === selectedAgent.value.id)
    if (conversation && conversation.messages.length > 0) {
      const lastMessage = conversation.messages[conversation.messages.length - 1]
      if (lastMessage.type === 'assistant') {
        const response = {
          id: lastMessage.id,
          type: 'assistant',
          agentName: selectedAgent.value.name,
          content: lastMessage.content,
          timestamp: lastMessage.timestamp,
          components: []
        }
        messages.value.push(response)

        // 检测并添加功能标签
        detectAndAddTags(currentInput)

        // 更新洞察面板
        updateInsights(response)
      }
    }

  } catch (error: any) {
    message.error(error.message || '发送失败，请重试')
  } finally {
    isLoading.value = false
    scrollToBottom()
  }
}

const simulateAIResponse = async (input: string): Promise<any> => {
  // 这里模拟不同类型的AI响应
  const responses = {
    '财务报表': {
      content: '我已经为您分析了2024Q4的财务报表数据。以下是主要发现：<br><br>• 营业收入同比增长12.5%，达到45.2亿元<br>• 净利润率保持在19.3%的健康水平<br>• 流动比率为1.35，流动性充足<br>• 资产负债率65.2%，处于合理范围',
      components: [
        {
          id: 'chart1',
          type: 'FinanceChart',
          props: { type: 'revenue', data: [45.2, 40.1, 38.7, 42.3] }
        }
      ]
    },
    '现金流': {
      content: '基于当前数据，我为您预测了未来13周的现金流情况：<br><br>• 第6周可能出现资金紧张<br>• 建议提前安排2.5亿元的短期融资<br>• 应收账款回收需要加强管理',
      components: [
        {
          id: 'chart2',
          type: 'CashFlowChart',
          props: { weeks: 13, data: [] }
        }
      ]
    },
    '风险': {
      content: '当前风险评估结果如下：<br><br>• 整体风险等级：中等<br>• 流动性风险：低<br>• 信用风险：中等<br>• 市场风险：中等<br><br>建议重点关注应收账款管理。',
      components: []
    }
  }
  
  // 简单的关键词匹配
  for (const [key, response] of Object.entries(responses)) {
    if (input.includes(key)) {
      return {
        id: Date.now(),
        type: 'assistant',
        agentName: selectedAgent.value?.name || '财务助手',
        content: response.content,
        timestamp: new Date(),
        components: response.components
      }
    }
  }
  
  // 默认响应
  return {
    id: Date.now(),
    type: 'assistant',
    agentName: selectedAgent.value?.name || '财务助手',
    content: '我理解您的问题。让我为您分析一下...<br><br>基于当前的财务数据，我建议您关注以下几个方面：<br>• 现金流管理<br>• 成本控制<br>• 风险监控',
    timestamp: new Date(),
    components: []
  }
}

const detectAndAddTags = (input: string) => {
  const tagMap = {
    '报表': 'FinancialReport',
    '现金流': 'CashFlow',
    '预算': 'Budget',
    '风险': 'Risk',
    '成本': 'Cost',
    '税务': 'Tax'
  }
  
  for (const [keyword, tag] of Object.entries(tagMap)) {
    if (input.includes(keyword) && !activeTags.value.includes(tag)) {
      activeTags.value.push(tag)
    }
  }
}

const updateInsights = (response: any) => {
  if (response.components && response.components.length > 0) {
    showInsightPanel.value = true
    insights.value = response.components.map((comp: any) => ({
      id: comp.id,
      title: comp.props.type === 'revenue' ? '营收趋势' : '现金流预测',
      component: 'InsightChart',
      props: comp.props
    }))
  }
}

const removeTag = (tag: string) => {
  activeTags.value = activeTags.value.filter(t => t !== tag)
}

const clearMemory = () => {
  activeTags.value = []
  message.success('对话记忆已清空')
}

const handleFileUpload = (file: File) => {
  message.success(`文件 ${file.name} 上传成功，正在分析...`)
  
  // 模拟文件处理
  setTimeout(() => {
    messages.value.push({
      id: Date.now(),
      type: 'assistant',
      agentName: '文档分析助手',
      content: `我已经分析了您上传的文件 "${file.name}"。<br><br>检测到以下内容：<br>• 财务数据表格<br>• 发票信息<br>• 合规性检查通过`,
      timestamp: new Date(),
      components: []
    })
    scrollToBottom()
  }, 2000)
  
  return false // 阻止默认上传
}

const toggleRecording = () => {
  isRecording.value = !isRecording.value
  if (isRecording.value) {
    message.info('开始语音录制...')
  } else {
    message.success('语音录制完成')
    inputMessage.value = '请分析当前的财务风险状况'
  }
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 生命周期
onMounted(() => {
  // 初始化智能体store
  agentStore.initialize()

  // 默认选择第一个启用的智能体
  nextTick(() => {
    if (agentCategories.value[0]?.agents[0]) {
      selectAgent(agentCategories.value[0].agents[0])
    } else if (agentStore.enabledAgents.length === 0) {
      // 如果没有启用的智能体，提示用户配置
      message.warning('请先在智能体管理中配置和启用智能体')
    }
  })
})
</script>

<style scoped>
:deep(.ant-textarea) {
  border: none !important;
  box-shadow: none !important;
  resize: none;
}

:deep(.ant-textarea:focus) {
  border: none !important;
  box-shadow: none !important;
}

.prose {
  max-width: none;
}

.prose p {
  margin-bottom: 0.5rem;
}

.prose ul {
  margin: 0.5rem 0;
}
</style>
