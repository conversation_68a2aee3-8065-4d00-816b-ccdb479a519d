'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Navigation from './Navigation'

interface DashboardLayoutProps {
  children: React.ReactNode
}

const DashboardLayout = ({ children }: DashboardLayoutProps) => {
  const router = useRouter()

  useEffect(() => {
    // 检查登录状态
    const token = localStorage.getItem('finance_ai_token')
    if (!token) {
      router.push('/')
      return
    }
  }, [router])

  return (
    <div className="min-h-screen bg-slate-50">
      <Navigation />
      
      {/* 主内容区域 */}
      <div className="pl-64">
        <main className="min-h-screen">
          {children}
        </main>
      </div>
    </div>
  )
}

export default DashboardLayout
