<template>
  <div class="min-h-screen bg-black text-white relative overflow-hidden">
    <!-- 顶部控制栏 -->
    <header v-if="!isFullscreen" class="absolute top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-sm">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <router-link to="/dashboard" class="text-white/60 hover:text-white">
              <ArrowLeftOutlined class="w-5 h-5" />
            </router-link>
            <div>
              <h1 class="text-xl font-bold">董事会模式</h1>
              <p class="text-sm text-white/60">Boardroom Presentation Mode</p>
            </div>
          </div>
          
          <div class="flex items-center space-x-3">
            <a-button @click="toggleFullscreen" class="bg-white/10 border-white/20 text-white hover:bg-white/20">
              <FullscreenOutlined />
              全屏演示
            </a-button>
            <a-button @click="startAutoPlay" class="bg-blue-600 border-blue-600 text-white hover:bg-blue-700">
              <PlayCircleOutlined />
              自动播放
            </a-button>
          </div>
        </div>
      </div>
    </header>

    <!-- 主演示区域 -->
    <main class="relative w-full h-screen flex flex-col">
      <!-- 进度条 -->
      <div class="absolute top-0 left-0 right-0 z-40">
        <div class="w-full bg-white/10 h-1">
          <div 
            class="bg-blue-500 h-1 transition-all duration-500"
            :style="{ width: ((currentSlide + 1) / slides.length * 100) + '%' }"
          ></div>
        </div>
        
        <!-- 时间轴点 -->
        <div class="absolute top-2 left-0 right-0 flex justify-center">
          <div class="flex space-x-2">
            <div
              v-for="(slide, index) in slides"
              :key="index"
              class="w-3 h-3 rounded-full cursor-pointer transition-all duration-300"
              :class="index <= currentSlide ? 'bg-blue-500' : 'bg-white/30'"
              @click="goToSlide(index)"
            ></div>
          </div>
        </div>
      </div>

      <!-- 幻灯片内容 -->
      <div class="flex-1 flex items-center justify-center p-8" :class="{ 'pt-20': !isFullscreen }">
        <div class="w-full max-w-6xl">
          <component
            :is="slides[currentSlide].component"
            v-bind="slides[currentSlide].props"
            :key="currentSlide"
          />
        </div>
      </div>

      <!-- 底部控制栏 -->
      <footer class="absolute bottom-0 left-0 right-0 z-40 bg-black/80 backdrop-blur-sm">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <!-- 左侧：导航控制 -->
            <div class="flex items-center space-x-4">
              <a-button 
                @click="previousSlide" 
                :disabled="currentSlide === 0"
                class="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                <LeftOutlined />
                上一页
              </a-button>
              <span class="text-sm text-white/60">
                {{ currentSlide + 1 }} / {{ slides.length }}
              </span>
              <a-button 
                @click="nextSlide" 
                :disabled="currentSlide === slides.length - 1"
                class="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                下一页
                <RightOutlined />
              </a-button>
            </div>

            <!-- 中间：当前幻灯片标题 -->
            <div class="text-center">
              <h2 class="text-lg font-medium">{{ slides[currentSlide].title }}</h2>
              <p class="text-sm text-white/60">{{ slides[currentSlide].subtitle }}</p>
            </div>

            <!-- 右侧：功能按钮 -->
            <div class="flex items-center space-x-3">
              <a-button @click="toggleLaserPointer" class="bg-white/10 border-white/20 text-white hover:bg-white/20">
                <AimOutlined />
                激光笔
              </a-button>
              <a-button @click="openQAPanel" class="bg-white/10 border-white/20 text-white hover:bg-white/20">
                <QuestionCircleOutlined />
                提问
              </a-button>
              <a-button @click="toggleSubtitles" class="bg-white/10 border-white/20 text-white hover:bg-white/20">
                <SoundOutlined />
                字幕
              </a-button>
            </div>
          </div>
        </div>
      </footer>

      <!-- 实时字幕 -->
      <div v-if="showSubtitles" class="absolute bottom-20 left-0 right-0 z-30">
        <div class="text-center">
          <div class="inline-block bg-black/80 backdrop-blur-sm rounded-lg px-6 py-3 max-w-4xl">
            <p class="text-lg">{{ currentSubtitle }}</p>
          </div>
        </div>
      </div>

      <!-- 激光笔效果 -->
      <div
        v-if="laserPointerEnabled"
        class="absolute w-4 h-4 bg-red-500 rounded-full pointer-events-none z-50 transition-all duration-100"
        :style="{ left: laserPosition.x + 'px', top: laserPosition.y + 'px' }"
      ></div>
    </main>

    <!-- 问答面板 -->
    <a-drawer
      v-model:open="qaDrawerVisible"
      title="实时问答"
      placement="right"
      width="400"
      class="boardroom-drawer"
    >
      <div class="space-y-4">
        <div>
          <a-textarea
            v-model:value="currentQuestion"
            placeholder="输入您的问题..."
            :rows="3"
            class="mb-3"
          />
          <a-button type="primary" block @click="askQuestion">
            <SendOutlined />
            提问
          </a-button>
        </div>
        
        <div class="border-t border-slate-200 pt-4">
          <h3 class="font-medium mb-3">问答历史</h3>
          <div class="space-y-3 max-h-96 overflow-y-auto">
            <div
              v-for="qa in qaHistory"
              :key="qa.id"
              class="border border-slate-200 rounded-lg p-3"
            >
              <div class="font-medium text-sm mb-2">Q: {{ qa.question }}</div>
              <div class="text-sm text-slate-600">A: {{ qa.answer }}</div>
              <div class="text-xs text-slate-400 mt-1">{{ formatTime(qa.timestamp) }}</div>
            </div>
          </div>
        </div>
      </div>
    </a-drawer>

    <!-- 自动播放控制 -->
    <div v-if="isAutoPlaying" class="absolute top-4 right-4 z-50">
      <div class="bg-black/80 backdrop-blur-sm rounded-lg px-4 py-2 flex items-center space-x-3">
        <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
        <span class="text-sm">自动播放中</span>
        <a-button size="small" @click="stopAutoPlay">
          <PauseCircleOutlined />
          暂停
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  FullscreenOutlined,
  PlayCircleOutlined,
  LeftOutlined,
  RightOutlined,
  AimOutlined,
  QuestionCircleOutlined,
  SoundOutlined,
  SendOutlined,
  PauseCircleOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const currentSlide = ref(0)
const isFullscreen = ref(false)
const isAutoPlaying = ref(false)
const laserPointerEnabled = ref(false)
const laserPosition = ref({ x: 0, y: 0 })
const showSubtitles = ref(false)
const currentSubtitle = ref('')
const qaDrawerVisible = ref(false)
const currentQuestion = ref('')
const qaHistory = ref<any[]>([])
const autoPlayInterval = ref<number | null>(null)

// 幻灯片数据
const slides = ref([
  {
    title: '财务概览',
    subtitle: '2024年度财务表现总结',
    component: 'FinancialOverviewSlide',
    props: {
      revenue: '45.2亿',
      profit: '8.7亿',
      growth: '+12.5%'
    }
  },
  {
    title: '现金流分析',
    subtitle: '资金流动性与风险评估',
    component: 'CashFlowSlide',
    props: {
      cashFlow: '12.5亿',
      liquidityRatio: 1.35,
      riskLevel: 'low'
    }
  },
  {
    title: '投资组合',
    subtitle: '资产配置与收益分析',
    component: 'InvestmentSlide',
    props: {
      totalAssets: '156.8亿',
      roe: '15.8%',
      riskRating: 'AAA'
    }
  },
  {
    title: '风险管控',
    subtitle: '风险识别与应对策略',
    component: 'RiskManagementSlide',
    props: {
      riskFactors: ['流动性风险', '市场风险', '信用风险'],
      mitigationStrategies: ['多元化投资', '风险对冲', '严格审核']
    }
  },
  {
    title: '未来展望',
    subtitle: '2025年战略规划与目标',
    component: 'FutureOutlookSlide',
    props: {
      targets: {
        revenue: '52亿',
        profit: '10.2亿',
        roe: '17%'
      }
    }
  }
])

// 字幕内容
const subtitles = ref([
  '欢迎参加本次董事会会议，我将为大家汇报2024年度财务表现...',
  '首先看到营业收入达到45.2亿元，同比增长12.5%，超额完成年度目标...',
  '现金流方面，我们保持了良好的流动性，流动比率达到1.35...',
  '在投资组合管理上，我们实现了15.8%的股本回报率...',
  '风险管控方面，我们建立了完善的风险识别和应对机制...',
  '展望2025年，我们制定了更加积极的发展目标...'
])

// 方法
const nextSlide = () => {
  if (currentSlide.value < slides.value.length - 1) {
    currentSlide.value++
    updateSubtitle()
  }
}

const previousSlide = () => {
  if (currentSlide.value > 0) {
    currentSlide.value--
    updateSubtitle()
  }
}

const goToSlide = (index: number) => {
  currentSlide.value = index
  updateSubtitle()
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

const startAutoPlay = () => {
  isAutoPlaying.value = true
  autoPlayInterval.value = setInterval(() => {
    if (currentSlide.value < slides.value.length - 1) {
      nextSlide()
    } else {
      stopAutoPlay()
      message.success('演示播放完成')
    }
  }, 8000) // 每8秒切换一张幻灯片
  
  message.info('自动播放已开始')
}

const stopAutoPlay = () => {
  isAutoPlaying.value = false
  if (autoPlayInterval.value) {
    clearInterval(autoPlayInterval.value)
    autoPlayInterval.value = null
  }
}

const toggleLaserPointer = () => {
  laserPointerEnabled.value = !laserPointerEnabled.value
  message.info(laserPointerEnabled.value ? '激光笔已启用' : '激光笔已关闭')
}

const toggleSubtitles = () => {
  showSubtitles.value = !showSubtitles.value
  if (showSubtitles.value) {
    updateSubtitle()
  }
  message.info(showSubtitles.value ? '字幕已开启' : '字幕已关闭')
}

const updateSubtitle = () => {
  if (showSubtitles.value && subtitles.value[currentSlide.value]) {
    currentSubtitle.value = subtitles.value[currentSlide.value]
  }
}

const openQAPanel = () => {
  qaDrawerVisible.value = true
}

const askQuestion = async () => {
  if (!currentQuestion.value.trim()) {
    message.warning('请输入问题')
    return
  }
  
  const question = currentQuestion.value
  currentQuestion.value = ''
  
  // 模拟AI回答
  message.loading('AI正在分析问题...', 1)
  
  setTimeout(() => {
    const answer = generateAIAnswer(question)
    qaHistory.value.unshift({
      id: Date.now(),
      question,
      answer,
      timestamp: new Date()
    })
    
    message.success('问题已回答')
  }, 1500)
}

const generateAIAnswer = (question: string): string => {
  // 简单的问题匹配和回答生成
  const answers = {
    '现金流': '当前现金流状况良好，流动比率1.35，现金及等价物23.4亿元，足以支撑日常运营和投资需求。',
    '风险': '主要风险包括市场波动、汇率变化和流动性风险。我们已建立完善的风险管控体系，定期评估和调整策略。',
    '投资': '投资组合表现优异，ROE达到15.8%。我们坚持稳健投资策略，平衡收益与风险。',
    '增长': '营收同比增长12.5%，主要得益于核心业务扩张和新项目投产。预计明年将保持稳定增长。',
    '利润': '净利润8.7亿元，利润率19.3%，处于行业领先水平。成本控制和效率提升是关键因素。'
  }
  
  for (const [keyword, answer] of Object.entries(answers)) {
    if (question.includes(keyword)) {
      return answer
    }
  }
  
  return '感谢您的问题。基于当前财务数据，我们的整体表现良好，各项指标均在健康范围内。如需更详细信息，请会后单独沟通。'
}

const handleMouseMove = (event: MouseEvent) => {
  if (laserPointerEnabled.value) {
    laserPosition.value = {
      x: event.clientX - 8, // 减去激光点半径
      y: event.clientY - 8
    }
  }
}

const handleKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'ArrowRight':
    case ' ':
      event.preventDefault()
      nextSlide()
      break
    case 'ArrowLeft':
      event.preventDefault()
      previousSlide()
      break
    case 'Escape':
      if (isFullscreen.value) {
        toggleFullscreen()
      }
      break
    case 'f':
    case 'F':
      toggleFullscreen()
      break
  }
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('keydown', handleKeydown)
  
  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', () => {
    isFullscreen.value = !!document.fullscreenElement
  })
  
  // 初始化字幕
  if (showSubtitles.value) {
    updateSubtitle()
  }
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('keydown', handleKeydown)
  
  if (autoPlayInterval.value) {
    clearInterval(autoPlayInterval.value)
  }
})
</script>

<style scoped>
:deep(.boardroom-drawer .ant-drawer-content) {
  background: #1f2937;
  color: white;
}

:deep(.boardroom-drawer .ant-drawer-header) {
  background: #1f2937;
  border-bottom: 1px solid #374151;
}

:deep(.boardroom-drawer .ant-drawer-title) {
  color: white;
}

:deep(.boardroom-drawer .ant-drawer-close) {
  color: white;
}

:deep(.boardroom-drawer .ant-input) {
  background: #374151;
  border-color: #4b5563;
  color: white;
}

:deep(.boardroom-drawer .ant-input:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style>
