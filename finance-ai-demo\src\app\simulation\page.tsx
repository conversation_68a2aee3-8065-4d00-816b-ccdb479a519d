'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, Button, Slider, InputNumber, Select, Progress } from 'antd'
import {
  PlayCircleOutlined,
  PauseOutlined,
  ReloadOutlined,
  SettingOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons'
import DashboardLayout from '@/components/DashboardLayout'

export default function SimulationPage() {
  const [isRunning, setIsRunning] = useState(false)
  const [progress, setProgress] = useState(0)
  const [marketVolatility, setMarketVolatility] = useState(15)
  const [timeHorizon, setTimeHorizon] = useState(12)

  const scenarios = [
    {
      id: 1,
      name: '基准情景',
      description: '基于历史数据的标准市场环境',
      probability: 60,
      expectedReturn: 8.5,
      risk: 'low'
    },
    {
      id: 2,
      name: '乐观情景',
      description: '市场表现良好，经济增长强劲',
      probability: 25,
      expectedReturn: 15.2,
      risk: 'medium'
    },
    {
      id: 3,
      name: '悲观情景',
      description: '市场下行，经济面临挑战',
      probability: 15,
      expectedReturn: -2.8,
      risk: 'high'
    }
  ]

  const handleRunSimulation = () => {
    setIsRunning(true)
    setProgress(0)
    
    // 模拟进度
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsRunning(false)
          return 100
        }
        return prev + 10
      })
    }, 500)
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600'
      case 'medium': return 'text-yellow-600'
      case 'high': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getRiskIcon = (expectedReturn: number) => {
    return expectedReturn > 0 ?
      <RiseOutlined className="text-green-500" /> :
      <FallOutlined className="text-red-500" />
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        {/* 页面标题 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center space-x-3 mb-2">
            <PlayCircleOutlined className="text-2xl text-purple-600" />
            <h1 className="text-3xl font-bold text-gray-900">风险建模系统</h1>
          </div>
          <p className="text-gray-600">专业的财务风险建模与压力测试平台</p>
        </motion.div>

        {/* 控制面板 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <Card title="模拟参数" extra={<Button icon={<SettingOutlined />}>高级设置</Button>}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium mb-2">市场波动率 (%)</label>
                <Slider
                  min={5}
                  max={50}
                  value={marketVolatility}
                  onChange={setMarketVolatility}
                  marks={{ 5: '5%', 25: '25%', 50: '50%' }}
                />
                <InputNumber
                  min={5}
                  max={50}
                  value={marketVolatility}
                  onChange={(value) => setMarketVolatility(value || 15)}
                  className="mt-2 w-full"
                  addonAfter="%"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">时间跨度 (月)</label>
                <Slider
                  min={1}
                  max={60}
                  value={timeHorizon}
                  onChange={setTimeHorizon}
                  marks={{ 1: '1月', 12: '1年', 60: '5年' }}
                />
                <InputNumber
                  min={1}
                  max={60}
                  value={timeHorizon}
                  onChange={(value) => setTimeHorizon(value || 12)}
                  className="mt-2 w-full"
                  addonAfter="月"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">模拟类型</label>
                <Select
                  defaultValue="monte-carlo"
                  className="w-full mb-4"
                  options={[
                    { value: 'monte-carlo', label: '蒙特卡洛模拟' },
                    { value: 'stress-test', label: '压力测试' },
                    { value: 'scenario', label: '情景分析' }
                  ]}
                />
                <div className="space-x-2">
                  <Button 
                    type="primary" 
                    icon={isRunning ? <PauseOutlined /> : <PlayCircleOutlined />}
                    onClick={handleRunSimulation}
                    disabled={isRunning}
                  >
                    {isRunning ? '运行中' : '开始模拟'}
                  </Button>
                  <Button icon={<ReloadOutlined />}>重置</Button>
                </div>
              </div>
            </div>
            
            {isRunning && (
              <div className="mt-4">
                <Progress percent={progress} status="active" />
                <p className="text-sm text-gray-500 mt-2">正在运行模拟... {progress}%</p>
              </div>
            )}
          </Card>
        </motion.div>

        {/* 情景分析 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <Card title="情景分析">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {scenarios.map((scenario, index) => (
                <motion.div
                  key={scenario.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                >
                  <Card 
                    className="h-full cursor-pointer hover:shadow-lg transition-shadow"
                    bodyStyle={{ padding: '16px' }}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold">{scenario.name}</h3>
                      {getRiskIcon(scenario.expectedReturn)}
                    </div>
                    <p className="text-sm text-gray-600 mb-4">{scenario.description}</p>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">概率</span>
                        <span className="text-sm font-medium">{scenario.probability}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">预期收益</span>
                        <span className={`text-sm font-medium ${scenario.expectedReturn > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {scenario.expectedReturn > 0 ? '+' : ''}{scenario.expectedReturn}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">风险等级</span>
                        <span className={`text-sm font-medium ${getRiskColor(scenario.risk)}`}>
                          {scenario.risk === 'low' ? '低' : scenario.risk === 'medium' ? '中' : '高'}
                        </span>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </Card>
        </motion.div>

        {/* 3D数据可视化 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mb-8"
        >
          <Card title="🎮 3D数据可视化" className="shadow-lg">
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
              {/* 3D风险收益立体图 */}
              <div className="xl:col-span-2">
                <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-xl p-6 border border-purple-100">
                  <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">
                      📊
                    </span>
                    3D风险收益立体图
                  </h4>
                  <div className="relative h-80 bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg overflow-hidden">
                    {/* 3D坐标系背景 */}
                    <div className="absolute inset-0 opacity-20">
                      <div className="absolute inset-0" style={{
                        backgroundImage: `
                          linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                          linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
                        `,
                        backgroundSize: '20px 20px'
                      }} />
                    </div>

                    {/* 3D数据点 */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="relative w-full h-full">
                        {scenarios.map((scenario, index) => {
                          const x = 20 + (scenario.probability / 100) * 60; // X轴：概率
                          const y = 80 - ((scenario.expectedReturn + 10) / 25) * 60; // Y轴：收益
                          const z = index * 15; // Z轴：时间维度

                          return (
                            <motion.div
                              key={scenario.id}
                              initial={{ scale: 0, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              transition={{ delay: index * 0.2 }}
                              className="absolute"
                              style={{
                                left: `${x}%`,
                                top: `${y}%`,
                                transform: `translateZ(${z}px)`,
                                transformStyle: 'preserve-3d'
                              }}
                            >
                              <div className={`w-6 h-6 rounded-full shadow-lg cursor-pointer transform hover:scale-150 transition-all duration-300 ${
                                scenario.risk === 'low' ? 'bg-green-400 shadow-green-400/50' :
                                scenario.risk === 'medium' ? 'bg-yellow-400 shadow-yellow-400/50' :
                                'bg-red-400 shadow-red-400/50'
                              }`}>
                                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-white text-xs font-medium whitespace-nowrap opacity-0 hover:opacity-100 transition-opacity">
                                  {scenario.name}
                                </div>
                              </div>

                              {/* 连接线 */}
                              <div className={`absolute top-3 left-3 w-px h-20 ${
                                scenario.risk === 'low' ? 'bg-green-400' :
                                scenario.risk === 'medium' ? 'bg-yellow-400' :
                                'bg-red-400'
                              } opacity-60`} />
                            </motion.div>
                          );
                        })}

                        {/* 坐标轴标签 */}
                        <div className="absolute bottom-4 left-4 text-white text-xs">
                          <div>概率 →</div>
                        </div>
                        <div className="absolute top-4 left-4 text-white text-xs transform -rotate-90 origin-left">
                          <div>收益 ↑</div>
                        </div>
                        <div className="absolute bottom-4 right-4 text-white text-xs">
                          <div>← 时间</div>
                        </div>
                      </div>
                    </div>

                    {/* 3D效果装饰 */}
                    <div className="absolute inset-0 pointer-events-none">
                      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-50" />
                      <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-400 to-transparent opacity-50" />
                      <div className="absolute top-0 left-0 w-px h-full bg-gradient-to-b from-transparent via-green-400 to-transparent opacity-50" />
                      <div className="absolute top-0 right-0 w-px h-full bg-gradient-to-b from-transparent via-red-400 to-transparent opacity-50" />
                    </div>
                  </div>

                  {/* 图例 */}
                  <div className="mt-4 flex justify-center space-x-6">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-green-400 rounded-full" />
                      <span className="text-sm text-gray-600">低风险</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-yellow-400 rounded-full" />
                      <span className="text-sm text-gray-600">中风险</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-400 rounded-full" />
                      <span className="text-sm text-gray-600">高风险</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* AI优化建议 */}
              <div className="space-y-4">
                <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
                  <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">
                      🤖
                    </span>
                    AI优化建议
                  </h4>
                  <div className="space-y-4">
                    <div className="bg-white rounded-lg p-4 shadow-sm">
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                          1
                        </div>
                        <div>
                          <h5 className="font-medium text-gray-800 mb-1">资产配置优化</h5>
                          <p className="text-sm text-gray-600">建议将60%资金配置到基准情景，25%配置到乐观情景，15%作为风险缓冲</p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white rounded-lg p-4 shadow-sm">
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                          2
                        </div>
                        <div>
                          <h5 className="font-medium text-gray-800 mb-1">风险对冲策略</h5>
                          <p className="text-sm text-gray-600">在悲观情景下，建议增加债券配置比例至40%，降低股票敞口</p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white rounded-lg p-4 shadow-sm">
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                          3
                        </div>
                        <div>
                          <h5 className="font-medium text-gray-800 mb-1">动态调整机制</h5>
                          <p className="text-sm text-gray-600">建议每季度根据市场变化调整投资组合，保持风险收益平衡</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 pt-4 border-t border-green-200">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">AI信心度</span>
                      <div className="flex items-center space-x-2">
                        <Progress percent={92} size="small" className="w-20" strokeColor="#10B981" />
                        <span className="text-sm font-medium text-green-600">92%</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 实时市场数据 */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
                  <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">
                      📈
                    </span>
                    实时市场数据
                  </h4>
                  <div className="space-y-3">
                    {[
                      { name: '上证指数', value: '3,247.82', change: '+1.24%', trend: 'up' },
                      { name: '深证成指', value: '12,456.73', change: '+0.87%', trend: 'up' },
                      { name: '创业板指', value: '2,789.45', change: '-0.32%', trend: 'down' },
                      { name: '沪深300', value: '4,123.67', change: '+0.95%', trend: 'up' }
                    ].map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm">
                        <div>
                          <div className="font-medium text-gray-800">{item.name}</div>
                          <div className="text-sm text-gray-600">{item.value}</div>
                        </div>
                        <div className={`text-sm font-medium ${
                          item.trend === 'up' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {item.change}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* 实时结果可视化 */}
        {progress === 100 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mb-8"
          >
            <Card title="模拟结果">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 收益分布图 */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-100 p-6 rounded-lg">
                  <h4 className="font-medium mb-4 text-center">收益分布概率</h4>
                  <div className="space-y-3">
                    {scenarios.map((scenario, index) => (
                      <div key={scenario.id} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div
                            className={`w-4 h-4 rounded-full ${
                              scenario.risk === 'low' ? 'bg-green-500' :
                              scenario.risk === 'medium' ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                          />
                          <span className="text-sm">{scenario.name}</span>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">{scenario.probability}%</div>
                          <div className={`text-xs ${scenario.expectedReturn > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {scenario.expectedReturn > 0 ? '+' : ''}{scenario.expectedReturn}%
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 风险指标 */}
                <div className="bg-gradient-to-br from-purple-50 to-pink-100 p-6 rounded-lg">
                  <h4 className="font-medium mb-4 text-center">风险指标</h4>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">VaR (95%)</span>
                      <span className="font-medium text-red-600">-8.2%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">最大回撤</span>
                      <span className="font-medium text-red-600">-12.5%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">夏普比率</span>
                      <span className="font-medium text-green-600">1.34</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">波动率</span>
                      <span className="font-medium text-orange-600">{marketVolatility}%</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 资金流向图 */}
              <div className="mt-6 bg-gradient-to-r from-gray-50 to-gray-100 p-6 rounded-lg">
                <h4 className="font-medium mb-4 text-center">资金流向分析</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-blue-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                      <span className="text-white font-bold">股票</span>
                    </div>
                    <div className="text-sm text-gray-600">45%</div>
                    <div className="text-xs text-green-600">+2.3%</div>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-green-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                      <span className="text-white font-bold">债券</span>
                    </div>
                    <div className="text-sm text-gray-600">30%</div>
                    <div className="text-xs text-green-600">+1.8%</div>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-yellow-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                      <span className="text-white font-bold">现金</span>
                    </div>
                    <div className="text-sm text-gray-600">15%</div>
                    <div className="text-xs text-gray-600">+0.5%</div>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-purple-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                      <span className="text-white font-bold">其他</span>
                    </div>
                    <div className="text-sm text-gray-600">10%</div>
                    <div className="text-xs text-red-600">-0.8%</div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* 快照管理 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mb-8"
        >
          <Card title="场景快照" extra={<Button type="primary">保存当前场景</Button>}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[
                { name: '保守策略', date: '2024-12-20', return: '6.8%', risk: 'low' },
                { name: '平衡策略', date: '2024-12-18', return: '9.2%', risk: 'medium' },
                { name: '激进策略', date: '2024-12-15', return: '15.6%', risk: 'high' }
              ].map((snapshot, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  className="border rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow"
                >
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium">{snapshot.name}</h4>
                    <div className={`w-3 h-3 rounded-full ${
                      snapshot.risk === 'low' ? 'bg-green-500' :
                      snapshot.risk === 'medium' ? 'bg-yellow-500' : 'bg-red-500'
                    }`} />
                  </div>
                  <div className="text-sm text-gray-600 mb-1">{snapshot.date}</div>
                  <div className="text-lg font-bold text-green-600">{snapshot.return}</div>
                  <div className="flex space-x-2 mt-3">
                    <Button size="small" type="primary">加载</Button>
                    <Button size="small">分享</Button>
                  </div>
                </motion.div>
              ))}
            </div>
          </Card>
        </motion.div>
      </div>
    </DashboardLayout>
  )
}
