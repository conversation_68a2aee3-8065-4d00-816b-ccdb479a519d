'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Input, 
  Button, 
  Card, 
  Avatar, 
  Badge, 
  Progress,
  Select,
  Checkbox,
  Slider,
  Switch
} from 'antd'
import {
  SendOutlined,
  RobotOutlined,
  UserOutlined,
  RiseOutlined,
  LoadingOutlined
} from '@ant-design/icons'
import DashboardLayout from '@/components/DashboardLayout'
import { difyApi } from '@/services/difyApi'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'
import 'highlight.js/styles/github.css'
import '@/styles/markdown.css'

const { TextArea } = Input
const { Option } = Select

// 处理thinking内容的函数
const processThinkingContent = (content: string) => {
  // 检查是否包含完整的thinking标签
  if (content.includes('<details') && content.includes('Thinking...') && content.includes('</details>')) {
    // 分离thinking部分和正常内容
    const thinkingMatch = content.match(/<details[^>]*>([\s\S]*?)<\/details>([\s\S]*)/);
    if (thinkingMatch) {
      const thinkingContent = thinkingMatch[1].replace(/<summary[^>]*>.*?<\/summary>/g, '').trim();
      const normalContent = thinkingMatch[2].trim();

      return {
        hasThinking: true,
        isThinkingComplete: true,
        thinkingContent,
        normalContent
      };
    }
  }

  // 检查是否正在输出thinking（开始了但还没结束）
  if (content.includes('<details') && content.includes('Thinking...') && !content.includes('</details>')) {
    const thinkingStartMatch = content.match(/<details[^>]*>[\s\S]*?<summary[^>]*>.*?Thinking.*?<\/summary>([\s\S]*)/);
    if (thinkingStartMatch) {
      return {
        hasThinking: true,
        isThinkingComplete: false,
        thinkingContent: thinkingStartMatch[1],
        normalContent: ''
      };
    }
  }

  return {
    hasThinking: false,
    isThinkingComplete: false,
    thinkingContent: '',
    normalContent: content
  };
};

export default function TrendAnalysisPage() {
  const [messages, setMessages] = useState<any[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isStreaming, setIsStreaming] = useState(false)
  const [currentThinkingMessageId, setCurrentThinkingMessageId] = useState<number | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  // 配置状态
  const [analysisMetrics, setAnalysisMetrics] = useState(['revenue', 'cost', 'profit'])
  const [timeHorizon, setTimeHorizon] = useState('12-months')
  const [forecastPeriod, setForecastPeriod] = useState('6-months')
  const [confidenceLevel, setConfidenceLevel] = useState(95)
  const [includeSeasonality, setIncludeSeasonality] = useState(true)
  const [includeCyclical, setIncludeCyclical] = useState(false)
  const [modelType, setModelType] = useState('arima')

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage = {
      id: Date.now(),
      type: 'user' as const,
      content: inputValue,
      timestamp: new Date().toLocaleTimeString()
    }

    const currentInput = inputValue
    setInputValue('')
    setIsLoading(true)
    setMessages(prev => [...prev, userMessage])

    try {
      // 构建包含配置信息的完整提示
      const configInfo = `
当前趋势分析配置：
- 分析指标：${analysisMetrics.map(m => getMetricName(m)).join('、')}
- 历史数据时间：${getTimeHorizonName(timeHorizon)}
- 预测期间：${getForecastPeriodName(forecastPeriod)}
- 置信水平：${confidenceLevel}%
- 季节性分析：${includeSeasonality ? '包含' : '不包含'}
- 周期性分析：${includeCyclical ? '包含' : '不包含'}
- 模型类型：${getModelTypeName(modelType)}

用户问题：${currentInput}

请基于以上配置信息，为用户提供专业的财务趋势分析和预测建议。`

      const initialAssistantMessage = {
        id: Date.now() + 1,
        type: 'assistant' as const,
        content: '',
        timestamp: new Date().toLocaleTimeString(),
        isStreaming: true
      }

      setMessages(prev => [...prev, initialAssistantMessage])

      // 尝试使用流式API
      try {
        setIsStreaming(true)
        await difyApi.sendMessageStream(
          configInfo,
          'demo_user',
          // 处理流式数据块
          (chunk) => {
            console.log('📨 收到流式数据块:', chunk)
            if (chunk.event === 'message' && chunk.answer) {
              setMessages(prev => prev.map(msg => {
                if (msg.id === initialAssistantMessage.id) {
                  const newContent = msg.content + chunk.answer;

                  // 检查是否开始thinking
                  if (newContent.includes('<details') && newContent.includes('Thinking...') && !currentThinkingMessageId) {
                    setCurrentThinkingMessageId(initialAssistantMessage.id);
                  }

                  // 检查thinking是否结束
                  if (currentThinkingMessageId === initialAssistantMessage.id && newContent.includes('</details>')) {
                    setCurrentThinkingMessageId(null);
                  }

                  return { ...msg, content: newContent };
                }
                return msg;
              }))
            }
          },
          // 处理完成
          (finalData) => {
            setIsStreaming(false)
            setIsLoading(false)
            setCurrentThinkingMessageId(null)
            setMessages(prev => prev.map(msg => 
              msg.id === initialAssistantMessage.id 
                ? { ...msg, isStreaming: false }
                : msg
            ))
          }
        )
      } catch (streamError) {
        console.warn('流式API失败，尝试阻塞模式:', streamError)
        setIsStreaming(false)
        setCurrentThinkingMessageId(null)

        // 回退到阻塞模式
        try {
          const response = await difyApi.sendMessage(configInfo)
          
          // 模拟流式效果显示阻塞响应
          setIsStreaming(true)
          const text = response.answer
          let currentIndex = 0

          const typeWriter = () => {
            if (currentIndex < text.length) {
              setMessages(prev => prev.map(msg => 
                msg.id === initialAssistantMessage.id 
                  ? { ...msg, content: text.substring(0, currentIndex + 1) }
                  : msg
              ))
              currentIndex++
              setTimeout(typeWriter, 30)
            } else {
              setIsStreaming(false)
              setIsLoading(false)
              setCurrentThinkingMessageId(null)
              setMessages(prev => prev.map(msg => 
                msg.id === initialAssistantMessage.id 
                  ? { ...msg, isStreaming: false }
                  : msg
              ))
            }
          }

          typeWriter()
        } catch (blockingError) {
          console.error('阻塞模式也失败:', blockingError)
          setIsStreaming(false)
          setCurrentThinkingMessageId(null)
          throw blockingError
        }
      }
    } catch (error) {
      console.error('发送消息失败:', error)
      setIsStreaming(false)
      setCurrentThinkingMessageId(null)

      const errorMessage = {
        id: Date.now() + 1,
        type: 'assistant' as const,
        content: '抱歉，我遇到了一些技术问题。请稍后再试，或者检查网络连接。',
        timestamp: new Date().toLocaleTimeString(),
        isError: true
      }

      setMessages(prev => [...prev.slice(0, -1), errorMessage])
      setIsLoading(false)
    }
  }

  // 辅助函数
  const getMetricName = (metric: string) => {
    const metrics = {
      'revenue': '营业收入',
      'cost': '营业成本',
      'profit': '净利润',
      'cash-flow': '现金流',
      'assets': '总资产',
      'liabilities': '总负债'
    }
    return metrics[metric as keyof typeof metrics] || metric
  }

  const getTimeHorizonName = (horizon: string) => {
    const horizons = {
      '6-months': '最近6个月',
      '12-months': '最近12个月',
      '24-months': '最近24个月',
      '36-months': '最近36个月'
    }
    return horizons[horizon as keyof typeof horizons] || horizon
  }

  const getForecastPeriodName = (period: string) => {
    const periods = {
      '3-months': '未来3个月',
      '6-months': '未来6个月',
      '12-months': '未来12个月',
      '24-months': '未来24个月'
    }
    return periods[period as keyof typeof periods] || period
  }

  const getModelTypeName = (type: string) => {
    const types = {
      'arima': 'ARIMA时间序列模型',
      'lstm': 'LSTM神经网络',
      'prophet': 'Prophet预测模型',
      'linear': '线性回归模型'
    }
    return types[type as keyof typeof types] || type
  }

  return (
    <DashboardLayout>
      <div className="h-screen flex bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        {/* 左侧配置面板 */}
        <div className="w-1/3 bg-white shadow-lg border-r border-gray-200 overflow-y-auto">
          <div className="p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                <RiseOutlined className="text-white text-lg" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-800">趋势分析</h1>
                <p className="text-sm text-gray-600">深度财务趋势分析与预测</p>
              </div>
            </div>

            {/* 分析指标 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-700 mb-3">分析指标</h3>
              <Checkbox.Group
                value={analysisMetrics}
                onChange={setAnalysisMetrics}
                className="w-full"
              >
                <div className="space-y-2">
                  <div><Checkbox value="revenue">营业收入</Checkbox></div>
                  <div><Checkbox value="cost">营业成本</Checkbox></div>
                  <div><Checkbox value="profit">净利润</Checkbox></div>
                  <div><Checkbox value="cash-flow">现金流</Checkbox></div>
                  <div><Checkbox value="assets">总资产</Checkbox></div>
                  <div><Checkbox value="liabilities">总负债</Checkbox></div>
                </div>
              </Checkbox.Group>
            </div>

            {/* 历史数据时间 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-700 mb-3">历史数据时间</h3>
              <Select
                value={timeHorizon}
                onChange={setTimeHorizon}
                className="w-full"
                size="large"
              >
                <Option value="6-months">最近6个月</Option>
                <Option value="12-months">最近12个月</Option>
                <Option value="24-months">最近24个月</Option>
                <Option value="36-months">最近36个月</Option>
              </Select>
            </div>

            {/* 预测期间 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-700 mb-3">预测期间</h3>
              <Select
                value={forecastPeriod}
                onChange={setForecastPeriod}
                className="w-full"
                size="large"
              >
                <Option value="3-months">未来3个月</Option>
                <Option value="6-months">未来6个月</Option>
                <Option value="12-months">未来12个月</Option>
                <Option value="24-months">未来24个月</Option>
              </Select>
            </div>

            {/* 置信水平 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-700 mb-3">置信水平: {confidenceLevel}%</h3>
              <Slider
                value={confidenceLevel}
                onChange={setConfidenceLevel}
                min={80}
                max={99}
                step={1}
                marks={{
                  80: '80%',
                  90: '90%',
                  95: '95%',
                  99: '99%'
                }}
              />
            </div>

            {/* 高级选项 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-700 mb-3">高级选项</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">季节性分析</span>
                  <Switch checked={includeSeasonality} onChange={setIncludeSeasonality} />
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">周期性分析</span>
                  <Switch checked={includeCyclical} onChange={setIncludeCyclical} />
                </div>
              </div>
            </div>

            {/* 模型类型 */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-gray-700 mb-3">预测模型</h3>
              <Select
                value={modelType}
                onChange={setModelType}
                className="w-full"
                size="large"
              >
                <Option value="arima">ARIMA时间序列</Option>
                <Option value="lstm">LSTM神经网络</Option>
                <Option value="prophet">Prophet预测</Option>
                <Option value="linear">线性回归</Option>
              </Select>
            </div>
          </div>
        </div>

        {/* 右侧聊天面板 */}
        <div className="flex-1 flex flex-col">
          {/* 聊天头部 */}
          <div className="bg-white border-b border-gray-200 p-4">
            <div className="flex items-center space-x-3">
              <Avatar 
                icon={<RobotOutlined />} 
                className="bg-gradient-to-r from-green-500 to-green-600"
              />
              <div>
                <h2 className="font-semibold text-gray-800">趋势分析助手</h2>
                <p className="text-sm text-gray-600">基于您的配置进行专业趋势分析和预测</p>
              </div>
              <Badge status="processing" text="在线" className="ml-auto" />
            </div>
          </div>

          {/* 聊天消息区域 */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            <AnimatePresence>
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                    <div className={`flex items-start space-x-2 ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                      <Avatar 
                        icon={message.type === 'user' ? <UserOutlined /> : <RobotOutlined />}
                        className={message.type === 'user' ? 'bg-green-500' : 'bg-gradient-to-r from-green-500 to-green-600'}
                      />
                      <div className={`rounded-2xl px-4 py-3 ${
                        message.type === 'user' 
                          ? 'bg-green-500 text-white' 
                          : 'bg-white border border-gray-200 text-gray-800'
                      }`}>
                        {message.type === 'assistant' && (
                          <div className="markdown-content">
                            {(() => {
                              const processed = processThinkingContent(message.content);
                              const isCurrentlyThinking = currentThinkingMessageId === message.id;

                              // 如果正在thinking但还没有完整的details标签，特殊处理
                              if (isCurrentlyThinking && !processed.isThinkingComplete) {
                                // 提取thinking内容（去掉HTML标签）
                                const thinkingContent = message.content
                                  .replace(/<details[^>]*>/g, '')
                                  .replace(/<summary[^>]*>.*?<\/summary>/g, '')
                                  .trim();

                                return (
                                  <details
                                    className="thinking-section"
                                    style={{
                                      color: '#6b7280',
                                      backgroundColor: '#f8f9fa',
                                      padding: '12px',
                                      borderRadius: '8px',
                                      margin: '8px 0',
                                      border: '1px solid #e5e7eb'
                                    }}
                                    open
                                  >
                                    <summary
                                      style={{
                                        fontWeight: '600',
                                        color: '#4b5563',
                                        cursor: 'pointer',
                                        marginBottom: '8px'
                                      }}
                                    >
                                      🤔 AI思考过程
                                    </summary>
                                    <div
                                      style={{
                                        color: '#6b7280',
                                        fontSize: '14px',
                                        lineHeight: '1.6',
                                        whiteSpace: 'pre-wrap'
                                      }}
                                    >
                                      {thinkingContent}
                                    </div>
                                  </details>
                                );
                              }

                              return (
                                <>
                                  {/* 渲染thinking部分 */}
                                  {processed.hasThinking && processed.isThinkingComplete && (
                                    <details
                                      className="thinking-section"
                                      style={{
                                        color: '#6b7280',
                                        backgroundColor: '#f8f9fa',
                                        padding: '12px',
                                        borderRadius: '8px',
                                        margin: '8px 0',
                                        border: '1px solid #e5e7eb'
                                      }}
                                      open
                                    >
                                      <summary
                                        style={{
                                          fontWeight: '600',
                                          color: '#4b5563',
                                          cursor: 'pointer',
                                          marginBottom: '8px'
                                        }}
                                      >
                                        🤔 AI思考过程
                                      </summary>
                                      <div
                                        style={{
                                          color: '#6b7280',
                                          fontSize: '14px',
                                          lineHeight: '1.6',
                                          whiteSpace: 'pre-wrap'
                                        }}
                                      >
                                        {processed.thinkingContent}
                                      </div>
                                    </details>
                                  )}

                                  {/* 渲染正常内容 */}
                                  {(processed.normalContent || (!processed.hasThinking && message.content)) && (
                                    <ReactMarkdown
                                      remarkPlugins={[remarkGfm]}
                                      rehypePlugins={[rehypeHighlight]}
                                      components={{
                                        h1: ({children}) => <h1 className="text-xl font-bold mb-3 text-gray-900">{children}</h1>,
                                        h2: ({children}) => <h2 className="text-lg font-bold mb-2 text-gray-900">{children}</h2>,
                                        h3: ({children}) => <h3 className="text-base font-bold mb-2 text-gray-900">{children}</h3>,
                                        p: ({children}) => <p className="mb-2 text-gray-900">{children}</p>,
                                        ul: ({children}) => <ul className="list-disc list-inside mb-2 text-gray-900">{children}</ul>,
                                        ol: ({children}) => <ol className="list-decimal list-inside mb-2 text-gray-900">{children}</ol>,
                                        li: ({children}) => <li className="mb-1 text-gray-900">{children}</li>,
                                        strong: ({children}) => <strong className="font-bold text-gray-900">{children}</strong>,
                                        em: ({children}) => <em className="italic text-gray-900">{children}</em>,
                                        code: ({children}) => <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono text-gray-900">{children}</code>,
                                        pre: ({children}) => <pre className="bg-gray-100 p-3 rounded-lg overflow-x-auto mb-2">{children}</pre>,
                                        blockquote: ({children}) => <blockquote className="border-l-4 border-green-500 pl-4 italic text-gray-700 mb-2">{children}</blockquote>,
                                        table: ({children}) => <table className="border-collapse border border-gray-300 mb-2">{children}</table>,
                                        th: ({children}) => <th className="border border-gray-300 px-2 py-1 bg-gray-100 font-bold text-gray-900">{children}</th>,
                                        td: ({children}) => <td className="border border-gray-300 px-2 py-1 text-gray-900">{children}</td>,
                                      }}
                                    >
                                      {processed.normalContent || (!processed.hasThinking ? message.content : '')}
                                    </ReactMarkdown>
                                  )}
                                </>
                              );
                            })()}
                          </div>
                        )}
                        
                        {message.type === 'user' && (
                          <div className="text-white">{message.content}</div>
                        )}
                      </div>
                    </div>
                    <div className={`text-xs text-gray-500 mt-1 ${message.type === 'user' ? 'text-right' : 'text-left'}`}>
                      {message.timestamp}
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
            
            {/* 加载指示器 */}
            {isLoading && !isStreaming && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex justify-start"
              >
                <div className="flex items-center space-x-2 bg-white border border-gray-200 rounded-2xl px-4 py-3">
                  <LoadingOutlined className="text-green-500" />
                  <span className="text-gray-600">AI正在分析趋势...</span>
                </div>
              </motion.div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* 输入区域 */}
          <div className="bg-white border-t border-gray-200 p-4">
            <div className="flex space-x-3">
              <TextArea
                ref={inputRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="请描述您需要的趋势分析，我会基于您的配置进行专业预测..."
                autoSize={{ minRows: 1, maxRows: 4 }}
                onPressEnter={(e) => {
                  if (!e.shiftKey) {
                    e.preventDefault()
                    handleSendMessage()
                  }
                }}
                className="resize-none text-gray-900"
                styles={{
                  textarea: {
                    color: '#111827 !important',
                    fontSize: '16px',
                    backgroundColor: 'transparent'
                  }
                }}
              />
              <Button 
                type="primary"
                icon={<SendOutlined />}
                onClick={handleSendMessage}
                loading={isLoading}
                disabled={!inputValue.trim()}
                className="bg-green-500 hover:bg-green-600"
              >
                发送
              </Button>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
