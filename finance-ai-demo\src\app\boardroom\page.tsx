'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, Button, Avatar, Badge, Timeline, Statistic } from 'antd'
import {
  BarChartOutlined,
  UserOutlined,
  VideoCameraOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import DashboardLayout from '@/components/DashboardLayout'
import CountUp from 'react-countup'

export default function BoardroomPage() {
  const [meetingStatus, setMeetingStatus] = useState('scheduled')
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isPresenting, setIsPresenting] = useState(false)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [showLaserPointer, setShowLaserPointer] = useState(false)

  const boardMembers = [
    { name: '张董事长', role: '董事长', status: 'online', avatar: '张' },
    { name: '李总经理', role: '总经理', status: 'online', avatar: '李' },
    { name: '王财务总监', role: 'CFO', status: 'online', avatar: '王' },
    { name: '赵副总', role: '副总经理', status: 'offline', avatar: '赵' },
    { name: '陈独立董事', role: '独立董事', status: 'online', avatar: '陈' }
  ]

  const agenda = [
    {
      time: '14:00-14:15',
      title: '会议开始及议程确认',
      status: 'completed',
      presenter: '张董事长'
    },
    {
      time: '14:15-14:45',
      title: 'Q4财务报告审议',
      status: 'current',
      presenter: '王财务总监'
    },
    {
      time: '14:45-15:15',
      title: '2025年预算方案讨论',
      status: 'pending',
      presenter: '李总经理'
    },
    {
      time: '15:15-15:30',
      title: '重大投资项目审批',
      status: 'pending',
      presenter: '赵副总'
    }
  ]

  const kpiData = [
    { title: '营业收入', value: 15600, suffix: '万元', change: 12.3 },
    { title: '净利润', value: 3200, suffix: '万元', change: 15.8 },
    { title: 'ROE', value: 18.6, suffix: '%', change: 2.1 },
    { title: '资产负债率', value: 45.2, suffix: '%', change: -1.5 }
  ]

  // 演示幻灯片数据
  const presentationSlides = [
    {
      id: 1,
      title: '2024年第四季度董事会例会',
      subtitle: '吉发集团股份有限公司',
      content: '汇报人：张董事长 | 2024年12月28日',
      type: 'title'
    },
    {
      id: 2,
      title: '会议议程',
      type: 'agenda',
      items: [
        { time: '14:00-14:15', title: '会议开始及议程确认', presenter: '张董事长' },
        { time: '14:15-14:45', title: '2024年Q4财务报告', presenter: '王财务总监' },
        { time: '14:45-15:15', title: '重大投资项目进展', presenter: '李总经理' },
        { time: '15:15-15:30', title: '风险管控情况汇报', presenter: '赵副总' },
        { time: '15:30-15:45', title: '2025年发展规划', presenter: '张董事长' },
        { time: '15:45-16:00', title: '决议事项讨论与表决', presenter: '全体董事' }
      ]
    },
    {
      id: 3,
      title: '2024年Q4财务业绩概览',
      type: 'financial_overview',
      data: {
        revenue: { value: 15680, growth: 12.3, target: 15000 },
        profit: { value: 3200, growth: 15.8, target: 2800 },
        assets: { value: 89500, growth: 8.7, target: 85000 },
        roe: { value: 18.6, growth: 2.1, target: 16.5 }
      }
    },
    {
      id: 4,
      title: '核心财务指标',
      content: '营业收入、净利润、ROE等关键指标分析',
      type: 'kpi',
      data: kpiData
    },
    {
      id: 5,
      title: '业务板块表现分析',
      type: 'business_segments',
      segments: [
        { name: '房地产开发', revenue: 6800, growth: 8.5, profit: 1200, margin: 17.6 },
        { name: '金融投资', revenue: 4200, growth: 18.2, profit: 980, margin: 23.3 },
        { name: '基础设施', revenue: 3100, growth: 15.6, profit: 620, margin: 20.0 },
        { name: '新能源', revenue: 1580, growth: 35.8, profit: 400, margin: 25.3 }
      ]
    },
    {
      id: 6,
      title: '重大投资项目进展',
      type: 'projects',
      projects: [
        {
          name: '吉林新区智慧城市项目',
          investment: 28.5,
          progress: 75,
          status: '按计划推进',
          roi: 15.2,
          completion: '2025年6月'
        },
        {
          name: '长春金融科技园',
          investment: 15.8,
          progress: 60,
          status: '超前完成',
          roi: 18.7,
          completion: '2025年3月'
        },
        {
          name: '新能源产业基地',
          investment: 22.3,
          progress: 45,
          status: '按计划推进',
          roi: 22.1,
          completion: '2025年12月'
        }
      ]
    },
    {
      id: 7,
      title: '风险管控情况',
      type: 'risk_management',
      risks: [
        {
          category: '市场风险',
          level: '中等',
          description: '房地产市场调控政策影响',
          mitigation: '多元化业务布局，降低单一行业依赖',
          status: '可控'
        },
        {
          category: '信用风险',
          level: '较低',
          description: '应收账款回收风险',
          mitigation: '加强客户信用评估，建立风险预警机制',
          status: '良好'
        },
        {
          category: '流动性风险',
          level: '较低',
          description: '短期资金周转压力',
          mitigation: '优化资金配置，保持充足现金储备',
          status: '充足'
        },
        {
          category: '政策风险',
          level: '中等',
          description: '行业监管政策变化',
          mitigation: '密切关注政策动向，及时调整经营策略',
          status: '关注'
        }
      ]
    },
    {
      id: 8,
      title: '2025年发展规划',
      type: 'strategy_2025',
      goals: [
        { target: '营业收入', value: '180亿元', growth: '+15%' },
        { target: '净利润', value: '38亿元', growth: '+18%' },
        { target: 'ROE', value: '20%', growth: '+1.4%' },
        { target: '新增投资', value: '50亿元', growth: '+25%' }
      ],
      initiatives: [
        '加快数字化转型，提升运营效率',
        '深化产融结合，拓展金融业务',
        '布局新兴产业，培育增长动能',
        '强化风险管控，确保稳健发展'
      ]
    },
    {
      id: 9,
      title: '决议事项',
      type: 'resolutions',
      items: [
        {
          title: '关于2024年度利润分配预案的议案',
          description: '拟向全体股东每10股派发现金红利8.5元（含税）',
          status: '待表决'
        },
        {
          title: '关于增加2025年度投资预算的议案',
          description: '新增投资预算15亿元，用于新能源项目建设',
          status: '待表决'
        },
        {
          title: '关于聘任2025年度审计机构的议案',
          description: '续聘立信会计师事务所为公司2025年度审计机构',
          status: '待表决'
        }
      ]
    },
    {
      id: 10,
      title: '谢谢各位董事！',
      subtitle: '会议结束',
      content: '感谢各位董事的参与和支持',
      type: 'ending'
    }
  ]

  // 全屏演示控制
  const enterFullscreen = () => {
    setIsFullscreen(true)
    setIsPresenting(true)
    setCurrentSlide(0)
    document.documentElement.requestFullscreen?.()
  }

  const exitFullscreen = () => {
    setIsFullscreen(false)
    setIsPresenting(false)
    setShowLaserPointer(false)
    document.exitFullscreen?.()
  }

  const nextSlide = () => {
    if (currentSlide < presentationSlides.length - 1) {
      setCurrentSlide(currentSlide + 1)
    }
  }

  const prevSlide = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1)
    }
  }

  // 激光笔功能
  const handleMouseMove = (e: React.MouseEvent) => {
    if (isFullscreen && showLaserPointer) {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }
  }

  const toggleLaserPointer = () => {
    setShowLaserPointer(!showLaserPointer)
  }

  // 全屏演示组件
  const FullscreenPresentation = () => (
    <div
      className="fixed inset-0 bg-gray-900 text-white z-50 flex flex-col"
      onMouseMove={handleMouseMove}
      onKeyDown={(e) => {
        if (e.key === 'ArrowRight' || e.key === ' ') nextSlide()
        if (e.key === 'ArrowLeft') prevSlide()
        if (e.key === 'Escape') exitFullscreen()
        if (e.key === 'l' || e.key === 'L') toggleLaserPointer()
      }}
      tabIndex={0}
    >
      {/* 进度条 */}
      <div className="h-1 bg-gray-800">
        <div
          className="h-full bg-blue-500 transition-all duration-300"
          style={{ width: `${((currentSlide + 1) / presentationSlides.length) * 100}%` }}
        />
      </div>

      {/* 幻灯片内容 */}
      <div className="flex-1 flex items-center justify-center p-8">
        <motion.div
          key={currentSlide}
          initial={{ opacity: 0, x: 100 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -100 }}
          className="max-w-6xl w-full text-center"
        >
          {presentationSlides[currentSlide]?.type === 'title' && (
            <div>
              <h1 className="text-6xl font-bold mb-8">{presentationSlides[currentSlide].title}</h1>
              <h2 className="text-3xl text-gray-300 mb-4">{presentationSlides[currentSlide].subtitle}</h2>
              <p className="text-xl text-gray-400">{presentationSlides[currentSlide].content}</p>
            </div>
          )}

          {presentationSlides[currentSlide]?.type === 'kpi' && (
            <div>
              <h1 className="text-5xl font-bold mb-12">{presentationSlides[currentSlide].title}</h1>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                {kpiData.map((kpi, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.2 }}
                    className="bg-gray-800 p-6 rounded-lg"
                  >
                    <div className="text-3xl font-bold text-blue-400">
                      <CountUp end={kpi.value} duration={2} />
                      {kpi.suffix}
                    </div>
                    <div className="text-lg text-gray-300 mt-2">{kpi.title}</div>
                    <div className={`text-sm mt-1 ${kpi.change > 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {kpi.change > 0 ? '+' : ''}{kpi.change}% 同比
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {presentationSlides[currentSlide]?.type === 'agenda' && (
            <div>
              <h1 className="text-5xl font-bold mb-12">{presentationSlides[currentSlide].title}</h1>
              <div className="max-w-4xl mx-auto">
                {presentationSlides[currentSlide].items?.map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.2 }}
                    className="flex items-center justify-between p-4 mb-4 bg-gray-800 rounded-lg border border-gray-700"
                  >
                    <div className="flex items-center space-x-6">
                      <div className="text-blue-400 font-mono text-lg">{item.time}</div>
                      <div className="text-xl text-white">{item.title}</div>
                    </div>
                    <div className="text-gray-400">{item.presenter}</div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {presentationSlides[currentSlide]?.type === 'financial_overview' && (
            <div>
              <h1 className="text-5xl font-bold mb-12">{presentationSlides[currentSlide].title}</h1>
              <div className="grid grid-cols-2 gap-8 max-w-5xl mx-auto">
                {Object.entries(presentationSlides[currentSlide].data || {}).map(([key, data], index) => (
                  <motion.div
                    key={key}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.2 }}
                    className="bg-gradient-to-br from-gray-800 to-gray-900 p-6 rounded-xl border border-gray-700"
                  >
                    <div className="text-center">
                      <div className="text-4xl font-bold text-blue-400 mb-2">
                        <CountUp end={data.value} duration={2} />
                        {key === 'roe' ? '%' : key === 'revenue' || key === 'profit' ? '万元' : '万元'}
                      </div>
                      <div className="text-lg text-gray-300 mb-2">
                        {key === 'revenue' ? '营业收入' :
                         key === 'profit' ? '净利润' :
                         key === 'assets' ? '总资产' : 'ROE'}
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-green-400">增长: +{data.growth}%</span>
                        <span className="text-gray-400">目标: {data.target}{key === 'roe' ? '%' : '万元'}</span>
                      </div>
                      <div className="mt-3 bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-green-500 h-2 rounded-full transition-all duration-1000"
                          style={{ width: `${Math.min((data.value / data.target) * 100, 100)}%` }}
                        />
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {presentationSlides[currentSlide]?.type === 'business_segments' && (
            <div>
              <h1 className="text-5xl font-bold mb-12">{presentationSlides[currentSlide].title}</h1>
              <div className="grid grid-cols-2 gap-6 max-w-6xl mx-auto">
                {presentationSlides[currentSlide].segments?.map((segment, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.2 }}
                    className="bg-gray-800 p-6 rounded-xl border border-gray-700"
                  >
                    <h3 className="text-2xl font-bold text-white mb-4">{segment.name}</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-400">营业收入</span>
                        <span className="text-blue-400 font-bold">{segment.revenue}万元</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">净利润</span>
                        <span className="text-green-400 font-bold">{segment.profit}万元</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">利润率</span>
                        <span className="text-yellow-400 font-bold">{segment.margin}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">同比增长</span>
                        <span className="text-green-400 font-bold">+{segment.growth}%</span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {presentationSlides[currentSlide]?.type === 'projects' && (
            <div>
              <h1 className="text-5xl font-bold mb-12">{presentationSlides[currentSlide].title}</h1>
              <div className="space-y-6 max-w-6xl mx-auto">
                {presentationSlides[currentSlide].projects?.map((project, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -100 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.3 }}
                    className="bg-gray-800 p-6 rounded-xl border border-gray-700"
                  >
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-2xl font-bold text-white mb-2">{project.name}</h3>
                        <div className="flex space-x-6 text-sm">
                          <span className="text-gray-400">投资额: <span className="text-blue-400 font-bold">{project.investment}亿元</span></span>
                          <span className="text-gray-400">预期ROI: <span className="text-green-400 font-bold">{project.roi}%</span></span>
                          <span className="text-gray-400">完工时间: <span className="text-yellow-400 font-bold">{project.completion}</span></span>
                        </div>
                      </div>
                      <div className={`px-3 py-1 rounded-full text-sm font-bold ${
                        project.status === '超前完成' ? 'bg-green-600 text-white' :
                        project.status === '按计划推进' ? 'bg-blue-600 text-white' : 'bg-yellow-600 text-black'
                      }`}>
                        {project.status}
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className="text-gray-400 text-sm">进度:</span>
                      <div className="flex-1 bg-gray-700 rounded-full h-3">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-1000"
                          style={{ width: `${project.progress}%` }}
                        />
                      </div>
                      <span className="text-white font-bold">{project.progress}%</span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {presentationSlides[currentSlide]?.type === 'risk_management' && (
            <div>
              <h1 className="text-5xl font-bold mb-12">{presentationSlides[currentSlide].title}</h1>
              <div className="grid grid-cols-2 gap-6 max-w-6xl mx-auto">
                {presentationSlides[currentSlide].risks?.map((risk, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.2 }}
                    className="bg-gray-800 p-6 rounded-xl border border-gray-700"
                  >
                    <div className="flex justify-between items-start mb-4">
                      <h3 className="text-xl font-bold text-white">{risk.category}</h3>
                      <div className={`px-3 py-1 rounded-full text-sm font-bold ${
                        risk.level === '较低' ? 'bg-green-600 text-white' :
                        risk.level === '中等' ? 'bg-yellow-600 text-black' : 'bg-red-600 text-white'
                      }`}>
                        {risk.level}
                      </div>
                    </div>
                    <div className="space-y-3 text-sm">
                      <div>
                        <span className="text-gray-400">风险描述:</span>
                        <p className="text-gray-300 mt-1">{risk.description}</p>
                      </div>
                      <div>
                        <span className="text-gray-400">应对措施:</span>
                        <p className="text-blue-300 mt-1">{risk.mitigation}</p>
                      </div>
                      <div className="flex justify-between items-center pt-2">
                        <span className="text-gray-400">控制状态:</span>
                        <span className={`font-bold ${
                          risk.status === '良好' || risk.status === '充足' ? 'text-green-400' :
                          risk.status === '可控' || risk.status === '关注' ? 'text-yellow-400' : 'text-red-400'
                        }`}>
                          {risk.status}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {presentationSlides[currentSlide]?.type === 'strategy_2025' && (
            <div>
              <h1 className="text-5xl font-bold mb-12">{presentationSlides[currentSlide].title}</h1>
              <div className="max-w-6xl mx-auto">
                <div className="grid grid-cols-4 gap-6 mb-12">
                  {presentationSlides[currentSlide].goals?.map((goal, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 50 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.2 }}
                      className="bg-gradient-to-br from-blue-800 to-purple-800 p-6 rounded-xl text-center"
                    >
                      <div className="text-3xl font-bold text-white mb-2">{goal.value}</div>
                      <div className="text-lg text-gray-300 mb-2">{goal.target}</div>
                      <div className="text-green-400 font-bold">{goal.growth}</div>
                    </motion.div>
                  ))}
                </div>
                <div className="bg-gray-800 p-8 rounded-xl border border-gray-700">
                  <h3 className="text-2xl font-bold text-white mb-6 text-center">重点举措</h3>
                  <div className="grid grid-cols-2 gap-6">
                    {presentationSlides[currentSlide].initiatives?.map((initiative, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -50 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.8 + index * 0.2 }}
                        className="flex items-center space-x-3"
                      >
                        <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                          {index + 1}
                        </div>
                        <span className="text-gray-300">{initiative}</span>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {presentationSlides[currentSlide]?.type === 'resolutions' && (
            <div>
              <h1 className="text-5xl font-bold mb-12">{presentationSlides[currentSlide].title}</h1>
              <div className="space-y-6 max-w-5xl mx-auto">
                {presentationSlides[currentSlide].items?.map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.3 }}
                    className="bg-gray-800 p-6 rounded-xl border border-gray-700"
                  >
                    <div className="flex justify-between items-start mb-4">
                      <h3 className="text-xl font-bold text-white flex-1">{item.title}</h3>
                      <div className="px-3 py-1 bg-yellow-600 text-black rounded-full text-sm font-bold ml-4">
                        {item.status}
                      </div>
                    </div>
                    <p className="text-gray-300">{item.description}</p>
                    <div className="mt-4 flex space-x-4">
                      <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        赞成
                      </button>
                      <button className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                        反对
                      </button>
                      <button className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                        弃权
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {presentationSlides[currentSlide]?.type === 'ending' && (
            <div className="text-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.8 }}
                className="mb-8"
              >
                <div className="text-8xl mb-6">🎉</div>
                <h1 className="text-6xl font-bold mb-4">{presentationSlides[currentSlide].title}</h1>
                <h2 className="text-3xl text-gray-300 mb-4">{presentationSlides[currentSlide].subtitle}</h2>
                <p className="text-xl text-gray-400">{presentationSlides[currentSlide].content}</p>
              </motion.div>
              <div className="text-gray-500 text-lg">
                <p>吉发集团股份有限公司</p>
                <p>2024年12月28日</p>
              </div>
            </div>
          )}
        </motion.div>
      </div>

      {/* 控制栏 */}
      <div className="p-4 bg-gray-800 flex justify-between items-center">
        <div className="flex space-x-4">
          <Button
            onClick={prevSlide}
            disabled={currentSlide === 0}
            className="bg-gray-700 border-gray-600 text-white"
          >
            上一页
          </Button>
          <Button
            onClick={nextSlide}
            disabled={currentSlide === presentationSlides.length - 1}
            className="bg-gray-700 border-gray-600 text-white"
          >
            下一页
          </Button>
          <Button
            onClick={toggleLaserPointer}
            className={`border-gray-600 ${showLaserPointer ? 'bg-red-600 text-white' : 'bg-gray-700 text-white'}`}
          >
            激光笔 (L)
          </Button>
        </div>

        <div className="text-gray-400">
          {currentSlide + 1} / {presentationSlides.length}
        </div>

        <Button onClick={exitFullscreen} className="bg-gray-700 border-gray-600 text-white">
          退出演示 (ESC)
        </Button>
      </div>

      {/* 激光笔效果 */}
      {showLaserPointer && (
        <div
          className="fixed w-4 h-4 bg-red-500 rounded-full pointer-events-none z-50 shadow-lg"
          style={{
            left: mousePosition.x - 8,
            top: mousePosition.y - 8,
            boxShadow: '0 0 20px rgba(239, 68, 68, 0.8)'
          }}
        />
      )}
    </div>
  )

  if (isFullscreen) {
    return <FullscreenPresentation />
  }

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <div className="p-6">
          {/* 页面标题 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center space-x-4 mb-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                    <BarChartOutlined className="text-2xl text-white" />
                  </div>
                  <div>
                    <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                      决策支持系统
                    </h1>
                    <p className="text-gray-600 text-lg">高管决策支持与智能会议管理平台</p>
                  </div>
                </div>
              </div>
              <div className="flex space-x-3">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    type="primary"
                    size="large"
                    icon={<BarChartOutlined />}
                    onClick={enterFullscreen}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 border-0 shadow-lg hover:shadow-xl transition-all duration-300 h-12 px-6"
                  >
                    开始演示
                  </Button>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    type="primary"
                    size="large"
                    icon={<VideoCameraOutlined />}
                    className="bg-gradient-to-r from-green-500 to-emerald-600 border-0 shadow-lg hover:shadow-xl transition-all duration-300 h-12 px-6"
                  >
                    加入会议
                  </Button>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    size="large"
                    icon={<FileTextOutlined />}
                    className="bg-white border-gray-300 shadow-lg hover:shadow-xl transition-all duration-300 h-12 px-6"
                  >
                    会议纪要
                  </Button>
                </motion.div>
              </div>
            </div>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 左侧：会议信息 */}
            <div className="lg:col-span-2 space-y-8">
              {/* 会议状态 */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card
                  title={
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                        <VideoCameraOutlined className="text-white text-sm" />
                      </div>
                      <span className="text-lg font-semibold">当前会议</span>
                    </div>
                  }
                  extra={
                    <motion.div
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      <Badge status="processing" text="进行中" />
                    </motion.div>
                  }
                  className="shadow-lg border-0 bg-white/80 backdrop-blur-sm"
                  bodyStyle={{ padding: '24px' }}
                >
                  <div className="space-y-6">
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">2024年第四季度董事会例会</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-gray-600">
                        <div className="flex items-center space-x-2">
                          <ClockCircleOutlined className="text-blue-500" />
                          <span>2024年12月28日 14:00-16:00</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <UserOutlined className="text-green-500" />
                          <span>吉发集团总部会议室 / 线上会议</span>
                        </div>
                      </div>
                    </div>

                    {/* 参会人员 */}
                    <div>
                      <h4 className="font-semibold mb-4 text-gray-800 flex items-center space-x-2">
                        <span>参会人员</span>
                        <Badge
                          count={`${boardMembers.filter(m => m.status === 'online').length}/${boardMembers.length}`}
                          style={{ backgroundColor: '#52c41a' }}
                        />
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {boardMembers.map((member, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 0.2 + index * 0.1 }}
                            whileHover={{ scale: 1.02 }}
                            className={`flex items-center space-x-3 p-3 rounded-lg border transition-all duration-300 ${
                              member.status === 'online'
                                ? 'bg-green-50 border-green-200 shadow-sm'
                                : 'bg-gray-50 border-gray-200'
                            }`}
                          >
                            <div className="relative">
                              <Avatar
                                size={40}
                                className={`${member.status === 'online' ? 'bg-green-500' : 'bg-gray-400'}`}
                              >
                                {member.avatar}
                              </Avatar>
                              <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                                member.status === 'online' ? 'bg-green-500' : 'bg-gray-400'
                              }`} />
                            </div>
                            <div className="flex-1">
                              <div className="font-medium text-gray-900">{member.name}</div>
                              <div className="text-sm text-gray-600">{member.role}</div>
                            </div>
                            {member.status === 'online' && (
                              <motion.div
                                animate={{ opacity: [0.5, 1, 0.5] }}
                                transition={{ duration: 2, repeat: Infinity }}
                                className="w-2 h-2 bg-green-500 rounded-full"
                              />
                            )}
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>

              {/* 会议议程 */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card
                  title={
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                        <ClockCircleOutlined className="text-white text-sm" />
                      </div>
                      <span className="text-lg font-semibold">会议议程</span>
                    </div>
                  }
                  className="shadow-lg border-0 bg-white/80 backdrop-blur-sm"
                  bodyStyle={{ padding: '24px' }}
                >
                  <Timeline className="mt-4">
                    {agenda.map((item, index) => (
                      <Timeline.Item
                        key={index}
                        dot={
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 0.3 + index * 0.1 }}
                            whileHover={{ scale: 1.2 }}
                            className={`w-8 h-8 rounded-full flex items-center justify-center shadow-lg ${
                              item.status === 'completed' ? 'bg-green-500' :
                              item.status === 'current' ? 'bg-blue-500' : 'bg-gray-400'
                            }`}
                          >
                            {item.status === 'completed' ? <CheckCircleOutlined className="text-white text-sm" /> :
                             item.status === 'current' ? <ClockCircleOutlined className="text-white text-sm" /> :
                             <ExclamationCircleOutlined className="text-white text-sm" />}
                          </motion.div>
                        }
                        color="transparent"
                      >
                        <motion.div
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.4 + index * 0.1 }}
                          className={`p-4 rounded-lg border transition-all duration-300 ${
                            item.status === 'completed' ? 'bg-green-50 border-green-200' :
                            item.status === 'current' ? 'bg-blue-50 border-blue-200 shadow-md' :
                            'bg-gray-50 border-gray-200'
                          }`}
                        >
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className={`font-semibold ${
                                item.status === 'current' ? 'text-blue-700' : 'text-gray-800'
                              }`}>
                                {item.title}
                              </span>
                              {item.status === 'current' && (
                                <motion.div
                                  animate={{ scale: [1, 1.1, 1] }}
                                  transition={{ duration: 2, repeat: Infinity }}
                                >
                                  <Badge status="processing" text="进行中" />
                                </motion.div>
                              )}
                            </div>
                            <div className="flex items-center space-x-4 text-sm text-gray-600">
                              <div className="flex items-center space-x-1">
                                <ClockCircleOutlined />
                                <span>{item.time}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <UserOutlined />
                                <span>主讲人: {item.presenter}</span>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      </Timeline.Item>
                    ))}
                  </Timeline>
                </Card>
              </motion.div>
            </div>

            {/* 右侧：关键指标 */}
            <div className="space-y-8">
              {/* 关键财务指标 */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card
                  title={
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center">
                        <BarChartOutlined className="text-white text-sm" />
                      </div>
                      <span className="text-lg font-semibold">关键指标</span>
                    </div>
                  }
                  className="shadow-lg border-0 bg-white/80 backdrop-blur-sm"
                  bodyStyle={{ padding: '24px' }}
                >
                  <div className="space-y-6">
                    {kpiData.map((kpi, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 + index * 0.1 }}
                        whileHover={{ scale: 1.02 }}
                        className="bg-gradient-to-r from-gray-50 to-blue-50 p-4 rounded-lg border border-gray-200 hover:border-blue-300 transition-all duration-300"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-600">{kpi.title}</span>
                          <div className={`text-xs px-2 py-1 rounded-full ${
                            kpi.change > 0 ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                          }`}>
                            {kpi.change > 0 ? '+' : ''}{kpi.change}%
                          </div>
                        </div>
                        <div className="flex items-end space-x-2">
                          <span className="text-2xl font-bold text-gray-900">
                            <CountUp end={kpi.value} duration={2} />
                          </span>
                          <span className="text-sm text-gray-500 mb-1">{kpi.suffix}</span>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">同比增长</div>
                      </motion.div>
                    ))}
                  </div>
                </Card>
              </motion.div>

              {/* 快速操作 */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Card
                  title={
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                        <FileTextOutlined className="text-white text-sm" />
                      </div>
                      <span className="text-lg font-semibold">快速操作</span>
                    </div>
                  }
                  className="shadow-lg border-0 bg-white/80 backdrop-blur-sm"
                  bodyStyle={{ padding: '24px' }}
                >
                  <div className="space-y-3">
                    {[
                      { icon: <FileTextOutlined />, text: '生成决议草案', color: 'from-blue-500 to-blue-600' },
                      { icon: <BarChartOutlined />, text: '查看详细报告', color: 'from-green-500 to-green-600' },
                      { icon: <VideoCameraOutlined />, text: '录制会议', color: 'from-purple-500 to-purple-600' },
                      { icon: <UserOutlined />, text: '邀请参会者', color: 'from-orange-500 to-orange-600' }
                    ].map((action, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5 + index * 0.1 }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Button
                          block
                          icon={action.icon}
                          className={`h-12 bg-gradient-to-r ${action.color} border-0 text-white shadow-lg hover:shadow-xl transition-all duration-300`}
                        >
                          {action.text}
                        </Button>
                      </motion.div>
                    ))}
                  </div>
                </Card>
              </motion.div>

              {/* 会议提醒 */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Card
                  title={
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-yellow-500 to-amber-600 rounded-lg flex items-center justify-center">
                        <ClockCircleOutlined className="text-white text-sm" />
                      </div>
                      <span className="text-lg font-semibold">会议提醒</span>
                    </div>
                  }
                  className="shadow-lg border-0 bg-white/80 backdrop-blur-sm"
                  bodyStyle={{ padding: '24px' }}
                >
                  <div className="space-y-4">
                    {[
                      { icon: <ClockCircleOutlined />, text: '下次会议: 2025年1月15日', color: 'text-blue-500', bg: 'bg-blue-50' },
                      { icon: <FileTextOutlined />, text: '待审议文件: 3份', color: 'text-green-500', bg: 'bg-green-50' },
                      { icon: <ExclamationCircleOutlined />, text: '待决议事项: 2项', color: 'text-orange-500', bg: 'bg-orange-50' }
                    ].map((reminder, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.6 + index * 0.1 }}
                        whileHover={{ scale: 1.02 }}
                        className={`flex items-center space-x-3 p-3 rounded-lg ${reminder.bg} border border-gray-200 transition-all duration-300`}
                      >
                        <div className={`${reminder.color} text-lg`}>
                          {reminder.icon}
                        </div>
                        <span className="text-sm font-medium text-gray-700">{reminder.text}</span>
                      </motion.div>
                    ))}
                  </div>
                </Card>
              </motion.div>
            </div>
          </div>

          {/* 装饰性统计卡片 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="mt-12"
          >
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {[
                { title: '本月会议', value: 8, icon: <VideoCameraOutlined />, color: 'from-blue-500 to-blue-600' },
                { title: '决议通过', value: 15, icon: <CheckCircleOutlined />, color: 'from-green-500 to-green-600' },
                { title: '待办事项', value: 3, icon: <ClockCircleOutlined />, color: 'from-orange-500 to-orange-600' },
                { title: '参会率', value: 95, icon: <UserOutlined />, color: 'from-purple-500 to-purple-600', suffix: '%' }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.8 + index * 0.1 }}
                  whileHover={{ scale: 1.05, y: -5 }}
                  className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className={`w-12 h-12 bg-gradient-to-br ${stat.color} rounded-lg flex items-center justify-center text-white text-xl shadow-lg`}>
                      {stat.icon}
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900">
                        <CountUp end={stat.value} duration={2} />
                        {stat.suffix}
                      </div>
                      <div className="text-sm text-gray-600">{stat.title}</div>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${Math.min(stat.value, 100)}%` }}
                      transition={{ delay: 1 + index * 0.1, duration: 1 }}
                      className={`h-2 bg-gradient-to-r ${stat.color} rounded-full`}
                    />
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* 状态信息 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2 }}
            className="text-center mt-12 mb-8"
          >
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-gray-200">
              <div className="flex items-center justify-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <span className="text-2xl">🏛️</span>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">决策支持系统</h3>
                  <p className="text-gray-600">智能化会议管理与决策支持平台</p>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">AI驱动</div>
                  <div className="text-sm text-gray-600">智能决策支持</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">实时协作</div>
                  <div className="text-sm text-gray-600">多人在线会议</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">数据洞察</div>
                  <div className="text-sm text-gray-600">深度财务分析</div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* 背景装饰 */}
          <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
            <div className="absolute top-20 left-10 w-32 h-32 bg-blue-200 rounded-full opacity-20 animate-pulse" />
            <div className="absolute top-40 right-20 w-24 h-24 bg-purple-200 rounded-full opacity-20 animate-pulse" style={{ animationDelay: '1s' }} />
            <div className="absolute bottom-40 left-20 w-28 h-28 bg-green-200 rounded-full opacity-20 animate-pulse" style={{ animationDelay: '2s' }} />
            <div className="absolute bottom-20 right-10 w-20 h-20 bg-orange-200 rounded-full opacity-20 animate-pulse" style={{ animationDelay: '3s' }} />
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
