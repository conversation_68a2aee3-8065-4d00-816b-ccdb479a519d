// Dify API 服务层
export interface DifyConfig {
  baseUrl: string
  apiKey: string
  appType: 'chatflow' | 'workflow' | 'completion'
}

export interface DifyMessage {
  inputs: Record<string, any>
  query?: string
  response_mode: 'streaming' | 'blocking'
  conversation_id?: string
  user: string
  files?: Array<{
    type: 'image'
    transfer_method: 'remote_url' | 'local_file'
    url?: string
    upload_file_id?: string
  }>
  auto_generate_name?: boolean
}

export interface DifyResponse {
  event: string
  message_id?: string
  conversation_id?: string
  answer?: string
  created_at?: number
  data?: any
}

export interface DifyCompletionMessage {
  inputs: Record<string, any>
  response_mode: 'streaming' | 'blocking'
  user: string
}

export interface DifyWorkflowMessage {
  inputs: Record<string, any>
  response_mode: 'streaming' | 'blocking'
  user: string
  files?: Array<{
    type: 'image'
    transfer_method: 'remote_url' | 'local_file'
    url?: string
    upload_file_id?: string
  }>
}

export class DifyApiService {
  private config: DifyConfig

  constructor(config: DifyConfig) {
    this.config = config
  }

  // 聊天对话 API (Chatflow)
  async sendChatMessage(message: DifyMessage): Promise<ReadableStream<Uint8Array> | DifyResponse> {
    const url = `${this.config.baseUrl}/chat-messages`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message)
    })

    if (!response.ok) {
      throw new Error(`Dify API Error: ${response.status} ${response.statusText}`)
    }

    if (message.response_mode === 'streaming') {
      return response.body!
    } else {
      return await response.json()
    }
  }

  // 工作流 API (Workflow)
  async runWorkflow(message: DifyWorkflowMessage): Promise<ReadableStream<Uint8Array> | DifyResponse> {
    const url = `${this.config.baseUrl}/workflows/run`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message)
    })

    if (!response.ok) {
      throw new Error(`Dify API Error: ${response.status} ${response.statusText}`)
    }

    if (message.response_mode === 'streaming') {
      return response.body!
    } else {
      return await response.json()
    }
  }

  // 文本生成 API (Completion)
  async generateCompletion(message: DifyCompletionMessage): Promise<ReadableStream<Uint8Array> | DifyResponse> {
    const url = `${this.config.baseUrl}/completion-messages`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message)
    })

    if (!response.ok) {
      throw new Error(`Dify API Error: ${response.status} ${response.statusText}`)
    }

    if (message.response_mode === 'streaming') {
      return response.body!
    } else {
      return await response.json()
    }
  }

  // 停止生成
  async stopGeneration(taskId: string, user: string): Promise<{ result: string }> {
    const url = `${this.config.baseUrl}/chat-messages/${taskId}/stop`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ user })
    })

    if (!response.ok) {
      throw new Error(`Dify API Error: ${response.status} ${response.statusText}`)
    }

    return await response.json()
  }

  // 获取对话历史
  async getConversationHistory(conversationId: string, user: string, firstId?: string, limit = 20) {
    const params = new URLSearchParams({
      conversation_id: conversationId,
      user,
      limit: limit.toString()
    })
    
    if (firstId) {
      params.append('first_id', firstId)
    }

    const url = `${this.config.baseUrl}/messages?${params}`
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
      }
    })

    if (!response.ok) {
      throw new Error(`Dify API Error: ${response.status} ${response.statusText}`)
    }

    return await response.json()
  }

  // 获取对话列表
  async getConversations(user: string, lastId?: string, limit = 20, pinned?: boolean) {
    const params = new URLSearchParams({
      user,
      limit: limit.toString()
    })
    
    if (lastId) params.append('last_id', lastId)
    if (pinned !== undefined) params.append('pinned', pinned.toString())

    const url = `${this.config.baseUrl}/conversations?${params}`
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
      }
    })

    if (!response.ok) {
      throw new Error(`Dify API Error: ${response.status} ${response.statusText}`)
    }

    return await response.json()
  }

  // 上传文件
  async uploadFile(file: File, user: string) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('user', user)

    const url = `${this.config.baseUrl}/files/upload`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
      },
      body: formData
    })

    if (!response.ok) {
      throw new Error(`Dify API Error: ${response.status} ${response.statusText}`)
    }

    return await response.json()
  }

  // 提交反馈
  async submitFeedback(messageId: string, rating: 'like' | 'dislike' | null, user: string) {
    const url = `${this.config.baseUrl}/messages/${messageId}/feedbacks`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ rating, user })
    })

    if (!response.ok) {
      throw new Error(`Dify API Error: ${response.status} ${response.statusText}`)
    }

    return await response.json()
  }

  // 解析流式响应
  async *parseStreamResponse(stream: ReadableStream<Uint8Array>): AsyncGenerator<DifyResponse> {
    const reader = stream.getReader()
    const decoder = new TextDecoder()
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) break
        
        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim()
            if (data === '[DONE]') return
            
            try {
              const parsed = JSON.parse(data)
              yield parsed
            } catch (e) {
              console.warn('Failed to parse SSE data:', data)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }
  }
}

// 创建默认实例
export const createDifyService = (config: DifyConfig) => new DifyApiService(config)

// 预设的智能体配置
export const DEFAULT_AGENTS = [
  {
    id: 'financial-insight',
    name: '财务报表洞察',
    description: '一键生成报表摘要和图表',
    appType: 'workflow' as const,
    category: '经营分析与预测',
    apiKey: '',
    enabled: false
  },
  {
    id: 'budget-planning',
    name: '智能预算编制',
    description: '基于历史数据的预算预测',
    appType: 'chatflow' as const,
    category: '经营分析与预测',
    apiKey: '',
    enabled: false
  },
  {
    id: 'cashflow-forecast',
    name: '现金流预测',
    description: '13周滚动现金流分析',
    appType: 'workflow' as const,
    category: '经营分析与预测',
    apiKey: '',
    enabled: false
  },
  {
    id: 'cost-copilot',
    name: '成本算法助手',
    description: '产品成本分摊和敏感性分析',
    appType: 'chatflow' as const,
    category: '成本与运营管控',
    apiKey: '',
    enabled: false
  },
  {
    id: 'expense-audit',
    name: '费用报销审核',
    description: 'OCR识别和合规性检查',
    appType: 'workflow' as const,
    category: '成本与运营管控',
    apiKey: '',
    enabled: false
  },
  {
    id: 'risk-radar',
    name: '财务风险雷达',
    description: '实时监控财务风险指标',
    appType: 'chatflow' as const,
    category: '风险合规与内控',
    apiKey: '',
    enabled: false
  },
  {
    id: 'tax-planning',
    name: '税务筹划助手',
    description: '税负优化和申报协助',
    appType: 'chatflow' as const,
    category: '风险合规与内控',
    apiKey: '',
    enabled: false
  },
  {
    id: 'knowledge-qa',
    name: '财务制度问答',
    description: 'RAG检索财务制度文档',
    appType: 'chatflow' as const,
    category: '智能问答与助手',
    apiKey: '',
    enabled: false
  },
  {
    id: 'chat-ops',
    name: 'Chat-Ops助手',
    description: '指令式智能体调度',
    appType: 'workflow' as const,
    category: '智能问答与助手',
    apiKey: '',
    enabled: false
  }
]
