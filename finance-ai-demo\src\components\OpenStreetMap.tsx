'use client'

import { useEffect, useRef, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, Badge } from 'antd'
import { EnvironmentOutlined } from '@ant-design/icons'

// 吉发集团固定资产数据
const assetData = [
  {
    id: 1,
    name: '吉发集团总部大厦',
    type: 'office',
    lng: 126.5504,
    lat: 43.8436,
    value: 28000,
    area: 15000,
    status: 'active',
    description: '集团总部办公楼，地上32层',
    color: '#3b82f6',
    address: '吉林市船营区解放大路1号'
  },
  {
    id: 2,
    name: '吉林化工园区厂房',
    type: 'factory',
    lng: 126.6200,
    lat: 43.8800,
    value: 45000,
    area: 28000,
    status: 'active',
    description: '化工生产基地，占地280亩',
    color: '#10b981',
    address: '吉林市龙潭区化工园区'
  },
  {
    id: 3,
    name: '松花江物流中心',
    type: 'warehouse',
    lng: 126.5200,
    lat: 43.8200,
    value: 18000,
    area: 12000,
    status: 'active',
    description: '现代化物流仓储中心',
    color: '#f59e0b',
    address: '吉林市丰满区松花江大街'
  },
  {
    id: 4,
    name: '高新区研发中心',
    type: 'research',
    lng: 126.5800,
    lat: 43.8600,
    value: 22000,
    area: 8000,
    status: 'active',
    description: '技术研发与创新中心',
    color: '#8b5cf6',
    address: '吉林市高新技术开发区'
  },
  {
    id: 5,
    name: '龙潭区商业综合体',
    type: 'commercial',
    lng: 126.6100,
    lat: 43.8700,
    value: 35000,
    area: 20000,
    status: 'construction',
    description: '在建商业地产项目',
    color: '#ef4444',
    address: '吉林市龙潭区遵义东路'
  },
  {
    id: 6,
    name: '昌邑区住宅项目',
    type: 'residential',
    lng: 126.5300,
    lat: 43.8500,
    value: 42000,
    area: 35000,
    status: 'planning',
    description: '规划中的住宅开发项目',
    color: '#06b6d4',
    address: '吉林市昌邑区民主路'
  }
]

export default function OpenStreetMap() {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<any>(null)
  const [selectedAsset, setSelectedAsset] = useState<any>(null)
  const [totalValue, setTotalValue] = useState(0)
  const [mapLoaded, setMapLoaded] = useState(false)

  useEffect(() => {
    const total = assetData.reduce((sum, asset) => sum + asset.value, 0)
    setTotalValue(total)
  }, [])

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '运营中'
      case 'construction': return '建设中'
      case 'planning': return '规划中'
      default: return '未知'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success'
      case 'construction': return 'warning'
      case 'planning': return 'default'
      default: return 'default'
    }
  }

  useEffect(() => {
    // 动态加载Leaflet地图
    const loadLeafletMap = async () => {
      try {
        // 如果地图已经存在，先清理
        if (mapInstanceRef.current) {
          mapInstanceRef.current.remove()
          mapInstanceRef.current = null
        }

        // 动态导入Leaflet
        const L = await import('leaflet')

        // 加载CSS
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css'
        document.head.appendChild(link)

        if (!mapRef.current) return

        // 创建地图
        const map = L.map(mapRef.current).setView([43.84, 126.55], 12)
        mapInstanceRef.current = map

        // 添加OpenStreetMap瓦片层
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '© OpenStreetMap contributors'
        }).addTo(map)

        // 添加资产标记
        assetData.forEach((asset) => {
          // 创建自定义图标
          const customIcon = L.divIcon({
            html: `
              <div style="
                width: 30px; 
                height: 40px; 
                background: ${asset.color}; 
                border-radius: 50% 50% 50% 0; 
                transform: rotate(-45deg); 
                border: 3px solid white;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                position: relative;
              ">
                <div style="
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%) rotate(45deg);
                  color: white;
                  font-size: 12px;
                  font-weight: bold;
                ">●</div>
              </div>
            `,
            className: 'custom-marker',
            iconSize: [30, 40],
            iconAnchor: [15, 40],
            popupAnchor: [0, -40]
          })

          // 创建标记
          const marker = L.marker([asset.lat, asset.lng], { icon: customIcon })
            .addTo(map)

          // 创建弹出窗口内容
          const popupContent = `
            <div style="padding: 8px; min-width: 200px;">
              <h4 style="margin: 0 0 8px 0; color: ${asset.color}; font-size: 16px;">${asset.name}</h4>
              <p style="margin: 4px 0; font-size: 14px;"><strong>资产价值:</strong> ${asset.value.toLocaleString()}万元</p>
              <p style="margin: 4px 0; font-size: 14px;"><strong>占地面积:</strong> ${asset.area.toLocaleString()}㎡</p>
              <p style="margin: 4px 0; font-size: 14px;"><strong>状态:</strong> ${getStatusText(asset.status)}</p>
              <p style="margin: 4px 0; color: #666; font-size: 12px;">${asset.address}</p>
              <p style="margin: 8px 0 0 0; color: #888; font-size: 11px;">${asset.description}</p>
            </div>
          `

          // 绑定弹出窗口
          marker.bindPopup(popupContent)

          // 标记点击事件
          marker.on('click', () => {
            setSelectedAsset(asset)
          })
        })

        setMapLoaded(true)
        console.log('OpenStreetMap地图初始化成功')

      } catch (error) {
        console.error('地图加载失败:', error)
        setMapLoaded(false)
      }
    }

    loadLeafletMap()

    // 清理函数
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove()
        mapInstanceRef.current = null
      }
    }
  }, [])

  return (
    <div className="w-full h-full relative">
      {/* 地图容器 */}
      <div className="w-full h-96 bg-slate-100 rounded-lg overflow-hidden relative">
        <div ref={mapRef} className="w-full h-full" />
        
        {!mapLoaded && (
          <div className="absolute inset-0 flex items-center justify-center bg-slate-50">
            <div className="text-center">
              <div className="text-slate-500 mb-2">正在加载吉林市地图...</div>
              <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
              <div className="text-xs text-slate-400 mt-2">使用OpenStreetMap提供真实地图数据</div>
            </div>
          </div>
        )}
      </div>

      {/* 信息面板 */}
      <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* 总览信息 */}
        <Card className="shadow-lg">
          <div className="text-center">
            <h3 className="text-lg font-bold text-slate-800 mb-2">固定资产总览</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-2xl font-bold text-blue-600">{assetData.length}</div>
                <div className="text-sm text-slate-500">资产项目</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">{totalValue.toLocaleString()}</div>
                <div className="text-sm text-slate-500">总价值(万元)</div>
              </div>
            </div>
          </div>
        </Card>

        {/* 选中资产详情 */}
        <AnimatePresence>
          {selectedAsset && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="shadow-lg border-l-4" style={{ borderLeftColor: selectedAsset.color }}>
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-lg font-bold text-slate-800">{selectedAsset.name}</h3>
                  <Badge 
                    status={getStatusColor(selectedAsset.status) as any}
                    text={getStatusText(selectedAsset.status)}
                  />
                </div>
                <p className="text-sm text-slate-600 mb-3">{selectedAsset.description}</p>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <span className="text-slate-500">资产价值:</span>
                    <div className="font-bold text-blue-600">{selectedAsset.value.toLocaleString()}万元</div>
                  </div>
                  <div>
                    <span className="text-slate-500">占地面积:</span>
                    <div className="font-bold text-green-600">{selectedAsset.area.toLocaleString()}㎡</div>
                  </div>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 操作提示 */}
      <div className="mt-4 text-center text-sm text-slate-500">
        <EnvironmentOutlined className="mr-1" />
        点击地图标记查看详情 | 鼠标拖拽移动地图 | 滚轮缩放 | 基于OpenStreetMap真实地图数据
      </div>
    </div>
  )
}
