<template>
  <div class="min-h-screen bg-slate-50">
    <!-- 顶部工具栏 -->
    <header class="bg-white shadow-sm border-b border-slate-200">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <router-link to="/dashboard" class="text-slate-400 hover:text-slate-600">
              <ArrowLeftOutlined class="w-5 h-5" />
            </router-link>
            <div>
              <h1 class="text-xl font-bold text-slate-900">洞察工作室</h1>
              <p class="text-sm text-slate-500">Insight Studio - 可视化故事画布</p>
            </div>
          </div>
          
          <div class="flex items-center space-x-3">
            <a-button @click="saveCanvas">
              <SaveOutlined />
              保存画布
            </a-button>
            <a-button type="primary" @click="narrateStory">
              <PlayCircleOutlined />
              一键叙述
            </a-button>
            <a-dropdown>
              <a-button>
                <ExportOutlined />
                导出
                <DownOutlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="exportToPPT">导出为PPT</a-menu-item>
                  <a-menu-item @click="exportToVideo">导出为视频</a-menu-item>
                  <a-menu-item @click="exportToImage">导出为长图</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </div>
    </header>

    <div class="flex h-screen">
      <!-- 左侧组件库 -->
      <aside class="w-80 bg-white shadow-sm border-r border-slate-200">
        <div class="p-4 border-b border-slate-200">
          <h2 class="text-lg font-bold text-slate-900">洞察块库</h2>
          <p class="text-sm text-slate-500">拖拽组件到画布创建故事</p>
        </div>
        
        <div class="p-4">
          <a-input
            v-model:value="searchBlock"
            placeholder="搜索组件..."
            class="mb-4"
          >
            <template #prefix>
              <SearchOutlined class="text-slate-400" />
            </template>
          </a-input>
          
          <div class="space-y-4">
            <div
              v-for="category in blockCategories"
              :key="category.name"
              class="mb-4"
            >
              <h3 class="text-sm font-medium text-slate-700 mb-2">{{ category.name }}</h3>
              <div class="grid grid-cols-2 gap-2">
                <div
                  v-for="block in category.blocks"
                  :key="block.id"
                  class="p-3 border border-slate-200 rounded-lg cursor-move hover:border-blue-300 hover:shadow-sm transition-all duration-200"
                  draggable="true"
                  @dragstart="handleDragStart($event, block)"
                >
                  <div class="flex flex-col items-center space-y-2">
                    <div class="w-8 h-8 rounded-lg flex items-center justify-center" :class="block.bgColor">
                      <component :is="block.icon" class="w-4 h-4" :class="block.iconColor" />
                    </div>
                    <span class="text-xs font-medium text-slate-700 text-center">{{ block.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </aside>

      <!-- 主画布区域 -->
      <main class="flex-1 relative">
        <!-- 画布工具栏 -->
        <div class="absolute top-4 left-4 z-10 flex space-x-2">
          <a-button size="small" @click="zoomIn">
            <PlusOutlined />
          </a-button>
          <a-button size="small" @click="zoomOut">
            <MinusOutlined />
          </a-button>
          <a-button size="small" @click="resetZoom">
            <ReloadOutlined />
          </a-button>
          <span class="px-2 py-1 bg-white rounded text-sm">{{ Math.round(canvasZoom * 100) }}%</span>
        </div>

        <!-- 画布 -->
        <div
          ref="canvasContainer"
          class="w-full h-full overflow-hidden bg-slate-100 relative"
          @drop="handleDrop"
          @dragover.prevent
          @wheel="handleWheel"
        >
          <div
            ref="canvas"
            class="absolute bg-white shadow-lg"
            :style="{
              width: canvasWidth + 'px',
              height: canvasHeight + 'px',
              transform: `scale(${canvasZoom}) translate(${canvasOffsetX}px, ${canvasOffsetY}px)`,
              transformOrigin: 'top left'
            }"
          >
            <!-- 网格背景 -->
            <div class="absolute inset-0 opacity-10" style="background-image: radial-gradient(circle, #94a3b8 1px, transparent 1px); background-size: 20px 20px;"></div>
            
            <!-- 洞察块 -->
            <div
              v-for="block in canvasBlocks"
              :key="block.id"
              class="absolute border-2 border-transparent hover:border-blue-300 rounded-lg cursor-move"
              :style="{
                left: block.x + 'px',
                top: block.y + 'px',
                width: block.width + 'px',
                height: block.height + 'px'
              }"
              @mousedown="startDrag($event, block)"
              @click="selectBlock(block)"
              :class="{ 'border-blue-500': selectedBlock?.id === block.id }"
            >
              <component
                :is="block.component"
                v-bind="block.props"
                class="w-full h-full"
              />
              
              <!-- 连接点 -->
              <div
                v-if="selectedBlock?.id === block.id"
                class="absolute -right-2 top-1/2 w-4 h-4 bg-blue-500 rounded-full cursor-pointer transform -translate-y-1/2"
                @mousedown.stop="startConnection($event, block)"
              ></div>
            </div>
            
            <!-- 连接线 -->
            <svg class="absolute inset-0 pointer-events-none">
              <path
                v-for="connection in connections"
                :key="connection.id"
                :d="connection.path"
                stroke="#3b82f6"
                stroke-width="2"
                fill="none"
                marker-end="url(#arrowhead)"
              />
              <defs>
                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                  <polygon points="0 0, 10 3.5, 0 7" fill="#3b82f6" />
                </marker>
              </defs>
            </svg>
          </div>
        </div>
      </main>

      <!-- 右侧属性面板 -->
      <aside v-if="selectedBlock" class="w-80 bg-white shadow-sm border-l border-slate-200">
        <div class="p-4 border-b border-slate-200">
          <h2 class="text-lg font-bold text-slate-900">属性设置</h2>
          <p class="text-sm text-slate-500">{{ selectedBlock.name }}</p>
        </div>
        
        <div class="p-4 space-y-4">
          <!-- 基础属性 -->
          <div>
            <label class="block text-sm font-medium text-slate-700 mb-2">标题</label>
            <a-input v-model:value="selectedBlock.props.title" />
          </div>
          
          <div class="grid grid-cols-2 gap-2">
            <div>
              <label class="block text-sm font-medium text-slate-700 mb-2">宽度</label>
              <a-input-number v-model:value="selectedBlock.width" :min="200" :max="800" />
            </div>
            <div>
              <label class="block text-sm font-medium text-slate-700 mb-2">高度</label>
              <a-input-number v-model:value="selectedBlock.height" :min="150" :max="600" />
            </div>
          </div>
          
          <!-- 数据配置 -->
          <div v-if="selectedBlock.type === 'chart'">
            <label class="block text-sm font-medium text-slate-700 mb-2">图表类型</label>
            <a-select v-model:value="selectedBlock.props.chartType" class="w-full">
              <a-select-option value="line">折线图</a-select-option>
              <a-select-option value="bar">柱状图</a-select-option>
              <a-select-option value="pie">饼图</a-select-option>
              <a-select-option value="waterfall">瀑布图</a-select-option>
            </a-select>
          </div>
          
          <!-- AI生成按钮 -->
          <div class="pt-4 border-t border-slate-200">
            <a-button type="primary" block @click="generateInsight">
              <BulbOutlined />
              AI生成洞察
            </a-button>
          </div>
          
          <!-- 删除按钮 -->
          <div class="pt-2">
            <a-button danger block @click="deleteBlock">
              <DeleteOutlined />
              删除组件
            </a-button>
          </div>
        </div>
      </aside>
    </div>

    <!-- 叙述模式模态框 -->
    <a-modal
      v-model:open="narrateModalVisible"
      title="故事叙述模式"
      width="80%"
      :footer="null"
      class="narrate-modal"
    >
      <div class="bg-black text-white p-8 rounded-lg">
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold mb-4">{{ currentNarrativeTitle }}</h2>
          <div class="w-full bg-gray-700 rounded-full h-2 mb-4">
            <div class="bg-blue-600 h-2 rounded-full transition-all duration-1000" :style="{ width: narrativeProgress + '%' }"></div>
          </div>
        </div>
        
        <div class="text-lg leading-relaxed mb-8">
          {{ currentNarrativeText }}
        </div>
        
        <div class="flex justify-center space-x-4">
          <a-button @click="pauseNarrative">
            <PauseCircleOutlined />
            暂停
          </a-button>
          <a-button @click="stopNarrative">
            <StopOutlined />
            停止
          </a-button>
          <a-button type="primary" @click="nextNarrativeSlide">
            <StepForwardOutlined />
            下一页
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  ExportOutlined,
  DownOutlined,
  SearchOutlined,
  PlusOutlined,
  MinusOutlined,
  ReloadOutlined,
  BulbOutlined,
  DeleteOutlined,
  PauseCircleOutlined,
  StopOutlined,
  StepForwardOutlined,
  BarChartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  DollarOutlined,
  TrendingUpOutlined,
  AlertOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const searchBlock = ref('')
const canvasContainer = ref<HTMLDivElement>()
const canvas = ref<HTMLDivElement>()
const canvasZoom = ref(1)
const canvasOffsetX = ref(0)
const canvasOffsetY = ref(0)
const canvasWidth = ref(1200)
const canvasHeight = ref(800)
const canvasBlocks = ref<any[]>([])
const connections = ref<any[]>([])
const selectedBlock = ref<any>(null)
const narrateModalVisible = ref(false)
const currentNarrativeTitle = ref('')
const currentNarrativeText = ref('')
const narrativeProgress = ref(0)
const isDragging = ref(false)
const dragOffset = ref({ x: 0, y: 0 })

// 组件库分类
const blockCategories = ref([
  {
    name: '财务图表',
    blocks: [
      {
        id: 'revenue-chart',
        name: '营收趋势',
        icon: LineChartOutlined,
        bgColor: 'bg-blue-100',
        iconColor: 'text-blue-600',
        component: 'RevenueChart',
        type: 'chart',
        defaultProps: { title: '营业收入趋势', chartType: 'line' }
      },
      {
        id: 'profit-chart',
        name: '利润分析',
        icon: BarChartOutlined,
        bgColor: 'bg-green-100',
        iconColor: 'text-green-600',
        component: 'ProfitChart',
        type: 'chart',
        defaultProps: { title: '利润结构分析', chartType: 'bar' }
      },
      {
        id: 'cashflow-waterfall',
        name: '现金瀑布',
        icon: TrendingUpOutlined,
        bgColor: 'bg-emerald-100',
        iconColor: 'text-emerald-600',
        component: 'CashflowWaterfall',
        type: 'chart',
        defaultProps: { title: '现金流瀑布图', chartType: 'waterfall' }
      }
    ]
  },
  {
    name: '风险分析',
    blocks: [
      {
        id: 'risk-radar',
        name: '风险雷达',
        icon: AlertOutlined,
        bgColor: 'bg-red-100',
        iconColor: 'text-red-600',
        component: 'RiskRadar',
        type: 'analysis',
        defaultProps: { title: '财务风险雷达图' }
      },
      {
        id: 'sensitivity-heat',
        name: '敏感性热力',
        icon: PieChartOutlined,
        bgColor: 'bg-orange-100',
        iconColor: 'text-orange-600',
        component: 'SensitivityHeat',
        type: 'analysis',
        defaultProps: { title: '敏感性分析热力图' }
      }
    ]
  },
  {
    name: '文本组件',
    blocks: [
      {
        id: 'insight-text',
        name: '洞察文本',
        icon: FileTextOutlined,
        bgColor: 'bg-purple-100',
        iconColor: 'text-purple-600',
        component: 'InsightText',
        type: 'text',
        defaultProps: { title: '关键洞察', content: '在此输入洞察内容...' }
      }
    ]
  }
])

// 方法
const handleDragStart = (event: DragEvent, block: any) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify(block))
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  if (!event.dataTransfer) return
  
  const blockData = JSON.parse(event.dataTransfer.getData('application/json'))
  const rect = canvas.value?.getBoundingClientRect()
  if (!rect) return
  
  const x = (event.clientX - rect.left) / canvasZoom.value
  const y = (event.clientY - rect.top) / canvasZoom.value
  
  const newBlock = {
    id: Date.now().toString(),
    ...blockData,
    x,
    y,
    width: 300,
    height: 200,
    props: { ...blockData.defaultProps }
  }
  
  canvasBlocks.value.push(newBlock)
  selectedBlock.value = newBlock
  
  message.success(`已添加 ${blockData.name}`)
}

const selectBlock = (block: any) => {
  selectedBlock.value = block
}

const startDrag = (event: MouseEvent, block: any) => {
  if (event.button !== 0) return // 只处理左键
  
  isDragging.value = true
  selectedBlock.value = block
  
  const rect = canvas.value?.getBoundingClientRect()
  if (!rect) return
  
  dragOffset.value = {
    x: (event.clientX - rect.left) / canvasZoom.value - block.x,
    y: (event.clientY - rect.top) / canvasZoom.value - block.y
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const handleMouseMove = (event: MouseEvent) => {
  if (!isDragging.value || !selectedBlock.value) return
  
  const rect = canvas.value?.getBoundingClientRect()
  if (!rect) return
  
  selectedBlock.value.x = (event.clientX - rect.left) / canvasZoom.value - dragOffset.value.x
  selectedBlock.value.y = (event.clientY - rect.top) / canvasZoom.value - dragOffset.value.y
}

const handleMouseUp = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

const zoomIn = () => {
  canvasZoom.value = Math.min(canvasZoom.value * 1.2, 3)
}

const zoomOut = () => {
  canvasZoom.value = Math.max(canvasZoom.value / 1.2, 0.3)
}

const resetZoom = () => {
  canvasZoom.value = 1
  canvasOffsetX.value = 0
  canvasOffsetY.value = 0
}

const handleWheel = (event: WheelEvent) => {
  if (event.ctrlKey) {
    event.preventDefault()
    const delta = event.deltaY > 0 ? 0.9 : 1.1
    canvasZoom.value = Math.max(0.3, Math.min(3, canvasZoom.value * delta))
  }
}

const generateInsight = async () => {
  if (!selectedBlock.value) return
  
  message.loading('AI正在生成洞察...', 2)
  
  // 模拟AI生成
  setTimeout(() => {
    if (selectedBlock.value.type === 'chart') {
      selectedBlock.value.props.aiInsight = '基于当前数据趋势，预计下季度营收将增长15%，建议加强市场投入。'
    } else if (selectedBlock.value.type === 'text') {
      selectedBlock.value.props.content = 'AI生成的洞察：当前财务状况良好，现金流充足，建议适度扩张业务规模。'
    }
    message.success('AI洞察生成完成')
  }, 2000)
}

const deleteBlock = () => {
  if (!selectedBlock.value) return
  
  canvasBlocks.value = canvasBlocks.value.filter(block => block.id !== selectedBlock.value.id)
  selectedBlock.value = null
  message.success('组件已删除')
}

const saveCanvas = () => {
  const canvasData = {
    blocks: canvasBlocks.value,
    connections: connections.value,
    timestamp: new Date().toISOString()
  }
  
  localStorage.setItem('insight_studio_canvas', JSON.stringify(canvasData))
  message.success('画布已保存')
}

const narrateStory = () => {
  if (canvasBlocks.value.length === 0) {
    message.warning('请先添加一些组件到画布')
    return
  }
  
  narrateModalVisible.value = true
  currentNarrativeTitle.value = '财务洞察故事'
  currentNarrativeText.value = '欢迎来到财务洞察故事模式。我将为您逐一解读画布上的每个洞察组件...'
  narrativeProgress.value = 0
  
  // 开始自动播放
  startNarrative()
}

const startNarrative = () => {
  let currentIndex = 0
  const narratives = [
    '首先，让我们看看营收趋势图。从数据可以看出，公司营收呈现稳定增长态势...',
    '接下来是利润分析。净利润率保持在健康水平，说明成本控制得当...',
    '现金流瀑布图显示了资金的流入流出情况，整体现金流为正...',
    '风险雷达图提醒我们关注几个关键风险点，需要制定相应的应对策略...'
  ]
  
  const interval = setInterval(() => {
    if (currentIndex < narratives.length) {
      currentNarrativeText.value = narratives[currentIndex]
      narrativeProgress.value = ((currentIndex + 1) / narratives.length) * 100
      currentIndex++
    } else {
      clearInterval(interval)
      message.success('故事叙述完成')
    }
  }, 3000)
}

const pauseNarrative = () => {
  message.info('叙述已暂停')
}

const stopNarrative = () => {
  narrateModalVisible.value = false
  message.info('叙述已停止')
}

const nextNarrativeSlide = () => {
  narrativeProgress.value = Math.min(narrativeProgress.value + 25, 100)
}

const exportToPPT = () => {
  message.success('正在导出为PPT格式...')
}

const exportToVideo = () => {
  message.success('正在导出为视频格式...')
}

const exportToImage = () => {
  message.success('正在导出为长图格式...')
}

// 生命周期
onMounted(() => {
  // 加载保存的画布
  const savedCanvas = localStorage.getItem('insight_studio_canvas')
  if (savedCanvas) {
    const canvasData = JSON.parse(savedCanvas)
    canvasBlocks.value = canvasData.blocks || []
    connections.value = canvasData.connections || []
  }
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
})
</script>

<style scoped>
.narrate-modal :deep(.ant-modal-content) {
  background: transparent;
  box-shadow: none;
}

.narrate-modal :deep(.ant-modal-header) {
  display: none;
}

.narrate-modal :deep(.ant-modal-body) {
  padding: 0;
}
</style>
