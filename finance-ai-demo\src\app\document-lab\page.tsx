'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, Button, Upload, List, Tag, Progress, Modal } from 'antd'
import {
  FileTextOutlined,
  UploadOutlined,
  FilePdfOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  CloudUploadOutlined,
  FilePptOutlined,
  Bar<PERSON>hartOutlined
} from '@ant-design/icons'
import DashboardLayout from '@/components/DashboardLayout'

export default function DocumentLabPage() {
  const [uploadProgress, setUploadProgress] = useState(0)
  const [processingFiles, setProcessingFiles] = useState<any[]>([])
  const [showTicketWall, setShowTicketWall] = useState(false)
  const [ocrResults, setOcrResults] = useState<any[]>([])
  const [selectedFile, setSelectedFile] = useState<any>(null)

  // 生成50个文档的测试数据
  const generateDocuments = () => {
    const types = ['pdf', 'excel', 'word', 'ppt']
    const statuses = ['processed', 'processing', 'ready']
    const categories = [
      '财务报告', '审计报告', '现金流分析', '资产负债表', '利润表', '预算执行',
      '成本分析', '销售统计', '费用支出', '银行对账', '税务申报', '固定资产',
      '投资建议', '财务制度', '内控评估', '风险管理', '合规检查', '董事会决议',
      '股东大会', '财务分析', '可行性研究', '尽职调查', '供应商合同', '租赁合同',
      '银行贷款', '投资协议', '保险合同', '发票汇总', '报销单据', '采购清单',
      '招待费用', '税务通知', '工商年检', '环保检查', '生产许可', '营业执照',
      '工资表', '年终奖', '社保缴费', '员工福利', '培训预算', '信用评估',
      '市场调研', '竞争分析', '发展趋势', '转型规划', '战略规划', '业务流程',
      '组织架构', '人事制度'
    ]

    // 文档摘要模板
    const summaryTemplates = {
      '财务报告': [
        '本季度营业收入同比增长12.5%，净利润率提升至8.3%，现金流状况良好，资产负债率控制在合理范围内。',
        '年度财务表现稳健，主营业务收入增长显著，成本控制效果明显，投资回报率达到预期目标。',
        '季度财务指标整体向好，收入结构优化，盈利能力增强，为下一阶段发展奠定坚实基础。'
      ],
      '审计报告': [
        '内部控制制度健全有效，财务报告真实可靠，未发现重大违规事项，建议进一步完善风险管控机制。',
        '审计发现部分流程需要优化，整体合规性良好，财务数据准确性高，管理层配合度佳。',
        '年度审计结果显示公司治理结构完善，内控体系运行良好，财务透明度不断提升。'
      ],
      '现金流分析': [
        '经营活动现金流净额为正，投资活动现金流合理，筹资活动现金流稳定，整体流动性充足。',
        '现金流量表显示公司资金周转良好，回款能力强，投资项目现金流预测准确，风险可控。',
        '月度现金流分析表明资金使用效率提升，现金管理水平优化，短期偿债能力增强。'
      ],
      '资产负债表': [
        '总资产规模稳步增长，资产结构持续优化，负债水平合理，所有者权益稳定增加。',
        '流动资产占比适中，固定资产投资回报良好，负债结构健康，财务杠杆运用得当。',
        '资产质量不断提升，坏账准备充足，负债成本控制有效，资本充足率符合监管要求。'
      ],
      '利润表': [
        '营业收入持续增长，毛利率保持稳定，期间费用控制良好，净利润实现双位数增长。',
        '主营业务利润贡献突出，非经常性损益影响较小，盈利质量不断改善，可持续性强。',
        '收入确认政策合规，成本核算准确，利润分配方案合理，股东回报稳定增长。'
      ],
      '预算执行': [
        '预算执行率达95%以上，收入预算完成情况良好，成本控制在预算范围内，投资计划按期推进。',
        '年度预算目标基本实现，各部门预算管理意识增强，预算调整程序规范，执行监控有效。',
        '季度预算分析显示收支平衡，重点项目预算执行到位，预算管理体系不断完善。'
      ],
      '成本分析': [
        '生产成本结构合理，原材料成本占比下降，人工成本控制有效，制造费用分摊准确。',
        '单位成本同比下降3.2%，成本控制措施得力，成本核算体系完善，成本管理水平提升。',
        '成本费用分析表明运营效率改善，成本节约措施成效显著，成本竞争优势明显。'
      ],
      '销售统计': [
        '销售收入稳步增长，市场份额扩大，客户结构优化，销售渠道多元化发展良好。',
        '月度销售数据显示增长势头强劲，新产品销售贡献突出，客户满意度持续提升。',
        '销售业绩超额完成，销售团队执行力强，市场开拓成效明显，品牌影响力增强。'
      ],
      '费用支出': [
        '管理费用控制在合理范围，销售费用投入产出比良好，财务费用水平稳定，费用结构优化。',
        '各项费用支出符合预算，费用审批流程规范，费用管控措施有效，成本效益显著。',
        '费用分析显示支出结构合理，重点费用项目管控到位，费用使用效率不断提升。'
      ],
      '银行对账': [
        '银行账户余额核对无误，资金流水清晰，银行手续费合理，资金安全管理到位。',
        '月度银行对账完成及时，未达账项处理规范，银行关系维护良好，融资成本控制有效。',
        '银行存款管理规范，资金集中度适中，银行授信额度充足，资金使用计划合理。'
      ],
      '税务申报': [
        '各项税费申报及时准确，税务筹划合规有效，税负水平合理，税务风险控制良好。',
        '增值税、企业所得税等申报无误，享受税收优惠政策到位，税务合规性不断提升。',
        '税务管理制度完善，税务申报流程规范，税务风险评估定期开展，合规经营意识强。'
      ],
      '固定资产': [
        '固定资产管理制度健全，资产使用效率高，折旧政策合理，资产保值增值良好。',
        '固定资产投资回报率达预期，资产配置优化，维护保养到位，资产安全管理有效。',
        '资产盘点结果准确，资产处置程序规范，资产利用率不断提升，投资决策科学合理。'
      ],
      '投资建议': [
        '投资项目可行性分析充分，风险评估全面，投资回报预期合理，建议审慎推进实施。',
        '市场前景广阔，技术路线可行，财务测算合理，风险可控，建议适度投资布局。',
        '投资机会分析深入，竞争优势明显，盈利模式清晰，建议加大投资力度抢占先机。'
      ],
      '财务制度': [
        '财务管理制度体系完整，操作流程规范，内控机制健全，执行监督有效。',
        '制度修订及时，适应性强，员工培训到位，制度执行力不断提升。',
        '财务制度与业务发展匹配，风险防控措施完善，制度化管理水平持续改进。'
      ],
      '内控评估': [
        '内部控制设计合理，执行有效，风险识别全面，控制措施得当，整体评价良好。',
        '内控体系运行稳定，关键控制点管控到位，内控缺陷整改及时，内控文化建设加强。',
        '内控评估结果显示制度执行规范，风险管控能力提升，内控有效性不断增强。'
      ],
      '风险管理': [
        '风险识别体系完善，风险评估方法科学，风险应对措施有效，风险监控机制健全。',
        '市场风险、信用风险、操作风险等管控到位，风险预警系统运行良好，风险文化建设深入。',
        '风险管理策略清晰，风险承受能力适中，风险管控工具运用得当，风险管理水平提升。'
      ],
      '合规检查': [
        '合规管理体系健全，合规风险识别准确，合规培训覆盖全面，合规文化氛围浓厚。',
        '法律法规遵循良好，监管要求执行到位，合规检查发现问题及时整改，合规意识不断增强。',
        '合规管理制度完善，合规审查程序规范，合规监督检查有效，合规管理水平持续提升。'
      ],
      '董事会决议': [
        '董事会决策程序规范，决议内容合规，执行监督有效，公司治理水平不断提升。',
        '重大事项决策科学，风险评估充分，利益相关方权益保护到位，决策质量持续改善。',
        '董事会运作机制完善，独立董事作用发挥良好，决策透明度提高，治理效率不断优化。'
      ],
      '股东大会': [
        '股东大会召开程序合规，股东权益保护到位，信息披露充分，股东参与度不断提高。',
        '重大事项审议充分，股东意见采纳合理，决议执行有效，股东关系管理持续改善。',
        '股东大会运作规范，投资者关系维护良好，公司透明度提升，股东价值创造能力增强。'
      ],
      '财务分析': [
        '财务指标分析全面，趋势分析准确，同业对比客观，为管理决策提供有力支撑。',
        '盈利能力、偿债能力、运营能力等分析深入，财务状况评价客观，改进建议切实可行。',
        '财务数据挖掘深度，分析方法科学，结论可靠，为战略规划提供重要参考依据。'
      ]
    }

    // 通用摘要模板（用于其他类型文档）
    const generalSummaries = [
      '文档内容详实，数据准确，分析深入，结论可靠，为业务决策提供重要参考。',
      '报告结构清晰，逻辑严密，建议具体，操作性强，具有较高的实用价值。',
      '文档质量良好，信息完整，分析客观，建议合理，符合公司发展需要。',
      '内容全面系统，数据真实可靠，分析方法科学，结论具有指导意义。',
      '文档编制规范，信息披露充分，分析角度多元，为管理提升提供支撑。'
    ]

    return Array.from({ length: 50 }, (_, index) => {
      const type = types[Math.floor(Math.random() * types.length)]
      const status = statuses[Math.floor(Math.random() * statuses.length)]
      const category = categories[index % categories.length]
      const fileExtension = type === 'excel' ? 'xlsx' : type === 'word' ? 'docx' : type === 'ppt' ? 'pptx' : 'pdf'

      // 获取对应类别的摘要
      let summary = ''
      if (summaryTemplates[category]) {
        const templates = summaryTemplates[category]
        summary = templates[Math.floor(Math.random() * templates.length)]
      } else {
        summary = generalSummaries[Math.floor(Math.random() * generalSummaries.length)]
      }

      return {
        id: index + 1,
        name: `${category}_${String(index + 1).padStart(3, '0')}.${fileExtension}`,
        type,
        size: `${(Math.random() * 5 + 0.5).toFixed(1)} MB`,
        status,
        insights: status === 'processed' ? Math.floor(Math.random() * 30 + 5) : 0,
        uploadTime: getRandomTime(),
        summary: summary
      }
    })
  }

  const getRandomTime = () => {
    const times = [
      '30分钟前', '1小时前', '2小时前', '3小时前', '半天前',
      '1天前', '2天前', '3天前', '5天前', '1周前', '2周前', '3周前',
      '1个月前', '2个月前', '3个月前', '6个月前', '1年前'
    ]
    return times[Math.floor(Math.random() * times.length)]
  }

  const documents = generateDocuments()

  // 票据数据 - 使用真实发票图片
  const tickets = [
    {
      id: 1,
      name: '增值税专用发票_001.jpeg',
      imagePath: '/955c4b45eb49c0699c5ffc80971ddb33.jpeg',
      type: 'invoice',
      amount: 8850.00,
      date: '2024-12-20',
      status: 'normal',
      confidence: 98.5,
      fields: {
        vendor: '北京科技有限公司',
        taxNumber: '91110000123456789X',
        items: '技术服务费',
        invoiceCode: '011001800111',
        invoiceNumber: '12345678'
      }
    },
    {
      id: 2,
      name: '增值税普通发票_002.jpeg',
      imagePath: '/9570a56a65e18365a0506236ee0f3bd7.jpeg',
      type: 'invoice',
      amount: 3250.00,
      date: '2024-12-18',
      status: 'anomaly',
      confidence: 76.2,
      fields: {
        vendor: '上海商贸有限公司',
        taxNumber: '91310000987654321Y',
        items: '办公用品采购'
      },
      anomalies: ['金额异常', '税率计算错误']
    },
    {
      id: 3,
      name: '机动车销售发票_003.jpeg',
      imagePath: '/e9fa8d8585f5c53e67742320adc9f67f.jpeg',
      type: 'invoice',
      amount: 15680.00,
      date: '2024-12-15',
      status: 'normal',
      confidence: 95.8,
      fields: {
        vendor: '汽车销售有限公司',
        taxNumber: '91440000456789123Z',
        items: '车辆购置',
        vehicleType: '商务用车'
      }
    },
    {
      id: 4,
      name: '餐饮服务发票_004.jpeg',
      imagePath: '/fb8433d3c29e6f23bc2db946cc329d55.jpeg',
      type: 'invoice',
      amount: 1256.50,
      date: '2024-12-12',
      status: 'warning',
      confidence: 88.3,
      fields: {
        vendor: '高档餐厅',
        taxNumber: '91110000789123456W',
        items: '商务宴请费'
      },
      anomalies: ['超出标准限额']
    }
  ]

  // 模拟OCR处理
  const simulateOCR = (file: any) => {
    setProcessingFiles(prev => [...prev, { ...file, progress: 0 }])
    
    const interval = setInterval(() => {
      setProcessingFiles(prev => 
        prev.map(f => 
          f.name === file.name 
            ? { ...f, progress: Math.min(f.progress + 10, 100) }
            : f
        )
      )
    }, 200)

    setTimeout(() => {
      clearInterval(interval)
      setProcessingFiles(prev => prev.filter(f => f.name !== file.name))
      
      // 添加到OCR结果
      const mockResult = {
        id: Date.now(),
        fileName: file.name,
        confidence: 85 + Math.random() * 15,
        extractedText: '模拟提取的文本内容...',
        fields: {
          amount: (Math.random() * 5000).toFixed(2),
          date: new Date().toISOString().split('T')[0],
          vendor: '示例供应商'
        }
      }
      setOcrResults(prev => [...prev, mockResult])
    }, 2000)
  }

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf': return <FilePdfOutlined className="text-red-500" />
      case 'excel': return <FileExcelOutlined className="text-green-500" />
      case 'word': return <FileWordOutlined className="text-blue-500" />
      case 'ppt': return <FilePptOutlined className="text-orange-500" />
      default: return <FileTextOutlined />
    }
  }

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'processed': return <Tag color="success">已处理</Tag>
      case 'processing': return <Tag color="processing">处理中</Tag>
      case 'ready': return <Tag color="default">待处理</Tag>
      default: return <Tag>未知</Tag>
    }
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        {/* 页面标题 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center space-x-3 mb-2">
            <FileTextOutlined className="text-2xl text-green-600" />
            <h1 className="text-3xl font-bold text-gray-900">智能审核平台</h1>
          </div>
          <p className="text-gray-600">专业的财务文档智能审核与合规检查系统</p>
        </motion.div>

        {/* 上传区域 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <Card title="文档上传" className="mb-6">
            <Upload.Dragger
              name="files"
              multiple
              action="/api/upload"
              accept=".pdf,.jpg,.jpeg,.png,.xlsx,.docx"
              onChange={(info) => {
                if (info.file.status === 'uploading') {
                  setUploadProgress(info.file.percent || 0)
                }
                if (info.file.status === 'done') {
                  simulateOCR(info.file)
                }
              }}
              className="mb-4"
            >
              <p className="ant-upload-drag-icon">
                <CloudUploadOutlined className="text-4xl text-blue-500" />
              </p>
              <p className="ant-upload-text text-lg">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                支持 PDF、Excel、Word 等格式，单个文件不超过 10MB
              </p>
            </Upload.Dragger>
            
            {uploadProgress > 0 && uploadProgress < 100 && (
              <Progress percent={uploadProgress} status="active" />
            )}
          </Card>
        </motion.div>

        {/* 文档统计概览 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="text-center">
              <div className="text-3xl font-bold text-blue-600">{documents.length}</div>
              <div className="text-gray-600 mt-1">总文档数</div>
              <div className="text-sm text-gray-400 mt-2">
                <FilePdfOutlined className="mr-1" />
                {documents.filter(d => d.type === 'pdf').length} PDF
              </div>
            </Card>
            <Card className="text-center">
              <div className="text-3xl font-bold text-green-600">
                {documents.filter(d => d.status === 'processed').length}
              </div>
              <div className="text-gray-600 mt-1">已处理</div>
              <div className="text-sm text-gray-400 mt-2">
                <FileExcelOutlined className="mr-1" />
                {documents.filter(d => d.type === 'excel').length} Excel
              </div>
            </Card>
            <Card className="text-center">
              <div className="text-3xl font-bold text-orange-600">
                {documents.filter(d => d.status === 'processing').length}
              </div>
              <div className="text-gray-600 mt-1">处理中</div>
              <div className="text-sm text-gray-400 mt-2">
                <FileWordOutlined className="mr-1" />
                {documents.filter(d => d.type === 'word').length} Word
              </div>
            </Card>
            <Card className="text-center">
              <div className="text-3xl font-bold text-purple-600">
                {documents.reduce((sum, doc) => sum + doc.insights, 0)}
              </div>
              <div className="text-gray-600 mt-1">总洞察数</div>
              <div className="text-sm text-gray-400 mt-2">
                <FilePptOutlined className="mr-1" />
                {documents.filter(d => d.type === 'ppt').length} PPT
              </div>
            </Card>
          </div>
        </motion.div>

        {/* 文档分类摘要 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.25 }}
          className="mb-8"
        >
          <Card title="文档分类摘要">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* 财务报告类 */}
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-3 flex items-center">
                  <BarChartOutlined className="mr-2" />
                  财务报告类
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>季度报告</span>
                    <span className="font-medium">4份</span>
                  </div>
                  <div className="flex justify-between">
                    <span>年度审计</span>
                    <span className="font-medium">1份</span>
                  </div>
                  <div className="flex justify-between">
                    <span>财务分析</span>
                    <span className="font-medium">3份</span>
                  </div>
                  <div className="text-xs text-blue-600 mt-2">
                    最新: 2024年Q4财务报告
                  </div>
                </div>
              </div>

              {/* 合同协议类 */}
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg">
                <h4 className="font-semibold text-green-800 mb-3 flex items-center">
                  <FileTextOutlined className="mr-2" />
                  合同协议类
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>供应商合同</span>
                    <span className="font-medium">5份</span>
                  </div>
                  <div className="flex justify-between">
                    <span>投资协议</span>
                    <span className="font-medium">2份</span>
                  </div>
                  <div className="flex justify-between">
                    <span>租赁合同</span>
                    <span className="font-medium">3份</span>
                  </div>
                  <div className="text-xs text-green-600 mt-2">
                    最新: 供应商合同_科技公司
                  </div>
                </div>
              </div>

              {/* 票据发票类 */}
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg">
                <h4 className="font-semibold text-purple-800 mb-3 flex items-center">
                  <CloudUploadOutlined className="mr-2" />
                  票据发票类
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>发票汇总</span>
                    <span className="font-medium">8份</span>
                  </div>
                  <div className="flex justify-between">
                    <span>报销单据</span>
                    <span className="font-medium">6份</span>
                  </div>
                  <div className="flex justify-between">
                    <span>采购清单</span>
                    <span className="font-medium">4份</span>
                  </div>
                  <div className="text-xs text-purple-600 mt-2">
                    最新: 12月份发票汇总
                  </div>
                </div>
              </div>

              {/* 政府文件类 */}
              <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-lg">
                <h4 className="font-semibold text-orange-800 mb-3 flex items-center">
                  <FileExcelOutlined className="mr-2" />
                  政府文件类
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>税务文件</span>
                    <span className="font-medium">3份</span>
                  </div>
                  <div className="flex justify-between">
                    <span>工商文件</span>
                    <span className="font-medium">2份</span>
                  </div>
                  <div className="flex justify-between">
                    <span>许可证书</span>
                    <span className="font-medium">2份</span>
                  </div>
                  <div className="text-xs text-orange-600 mt-2">
                    最新: 税务稽查通知书
                  </div>
                </div>
              </div>

              {/* 人事薪酬类 */}
              <div className="bg-gradient-to-br from-red-50 to-red-100 p-4 rounded-lg">
                <h4 className="font-semibold text-red-800 mb-3 flex items-center">
                  <FileWordOutlined className="mr-2" />
                  人事薪酬类
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>工资表</span>
                    <span className="font-medium">2份</span>
                  </div>
                  <div className="flex justify-between">
                    <span>社保缴费</span>
                    <span className="font-medium">1份</span>
                  </div>
                  <div className="flex justify-between">
                    <span>福利统计</span>
                    <span className="font-medium">2份</span>
                  </div>
                  <div className="text-xs text-red-600 mt-2">
                    最新: 12月份工资表
                  </div>
                </div>
              </div>

              {/* 其他文档类 */}
              <div className="bg-gradient-to-br from-gray-50 to-gray-100 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
                  <FilePptOutlined className="mr-2" />
                  其他文档类
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>市场调研</span>
                    <span className="font-medium">2份</span>
                  </div>
                  <div className="flex justify-between">
                    <span>竞争分析</span>
                    <span className="font-medium">1份</span>
                  </div>
                  <div className="flex justify-between">
                    <span>发展规划</span>
                    <span className="font-medium">3份</span>
                  </div>
                  <div className="text-xs text-gray-600 mt-2">
                    最新: 数字化转型规划
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* 文档列表 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mb-8"
        >
          <Card
            title="文档库"
            extra={
              <div className="space-x-2">
                <Button
                  type="primary"
                  icon={<FileTextOutlined />}
                  onClick={() => setShowTicketWall(true)}
                  className="bg-purple-600 border-purple-600"
                >
                  票据识别墙
                </Button>
                <Button type="primary" icon={<UploadOutlined />}>
                  批量上传
                </Button>
              </div>
            }
          >
            <List
              itemLayout="horizontal"
              dataSource={documents}
              pagination={{
                pageSize: 15,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条文档`
              }}
              renderItem={(item, index) => (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.02 }}
                >
                  <List.Item className="!items-start">
                    <div className="flex w-full items-start">
                      {/* 左侧图标 */}
                      <div className="text-2xl mr-4 mt-1 flex-shrink-0">
                        {getFileIcon(item.type)}
                      </div>

                      {/* 左侧文档信息 */}
                      <div className="flex-shrink-0 mr-4" style={{ minWidth: '200px' }}>
                        {/* 文档标题和状态 */}
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="font-medium text-gray-900 truncate">{item.name}</span>
                          {getStatusTag(item.status)}
                        </div>

                        {/* 文档基本信息 */}
                        <div className="text-sm text-gray-500">
                          <div>大小: {item.size}</div>
                          <div>上传时间: {item.uploadTime}</div>
                          <div>提取洞察: {item.insights} 条</div>
                        </div>
                      </div>

                      {/* 中间文档摘要 */}
                      <div className="flex-1 min-w-0 mr-4">
                        <div className="bg-gray-50 rounded-lg p-3 h-full">
                          <div className="text-xs text-gray-600 mb-1 font-medium">📄 文档摘要</div>
                          <div className="text-sm text-gray-700 leading-relaxed">
                            {item.summary}
                          </div>
                        </div>
                      </div>

                      {/* 右侧操作按钮 */}
                      <div className="flex flex-col space-y-1 flex-shrink-0">
                        <Button size="small" type="link" className="text-blue-600">分析</Button>
                        <Button size="small" type="link" className="text-green-600">下载</Button>
                        <Button size="small" type="link" danger className="text-red-600">删除</Button>
                      </div>
                    </div>
                  </List.Item>
                </motion.div>
              )}
            />
          </Card>
        </motion.div>

        {/* OCR处理结果 */}
        {ocrResults.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mb-8"
          >
            <Card title="OCR识别结果">
              <div className="space-y-4">
                {ocrResults.map((result, index) => (
                  <motion.div
                    key={result.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="border rounded-lg p-4 bg-gray-50"
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-medium">{result.fileName}</h4>
                        <div className="text-sm text-gray-500">
                          置信度: {result.confidence.toFixed(1)}%
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button size="small" type="primary">审核</Button>
                        <Button size="small">编辑</Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">金额</label>
                        <div className="text-lg font-bold text-green-600">
                          ¥{result.fields.amount}
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">日期</label>
                        <div>{result.fields.date}</div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">供应商</label>
                        <div>{result.fields.vendor}</div>
                      </div>
                    </div>

                    <div className="mt-3">
                      <label className="text-sm font-medium text-gray-700">提取文本</label>
                      <div className="text-sm text-gray-600 bg-white p-2 rounded border mt-1">
                        {result.extractedText}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </Card>
          </motion.div>
        )}

        {/* 快速操作 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card
            title={
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm">⚡</span>
                </div>
                <span className="text-lg font-semibold">快速操作</span>
              </div>
            }
            className="shadow-lg border-0 bg-white/80 backdrop-blur-sm"
            bodyStyle={{ padding: '24px' }}
          >
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="group"
              >
                <Button
                  type="primary"
                  size="large"
                  className="w-full h-20 bg-gradient-to-br from-blue-500 to-blue-600 border-0 shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group-hover:from-blue-600 group-hover:to-blue-700"
                >
                  <UploadOutlined className="text-xl mr-3" />
                  <span className="text-sm font-medium">上传文档</span>
                </Button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="group"
              >
                <Button
                  size="large"
                  className="w-full h-20 bg-gradient-to-br from-green-500 to-green-600 border-0 shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-white hover:text-white group-hover:from-green-600 group-hover:to-green-700"
                >
                  <FileTextOutlined className="text-xl mr-3" />
                  <span className="text-sm font-medium">创建模板</span>
                </Button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="group"
              >
                <Button
                  size="large"
                  className="w-full h-20 bg-gradient-to-br from-purple-500 to-purple-600 border-0 shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-white hover:text-white group-hover:from-purple-600 group-hover:to-purple-700"
                >
                  <FilePdfOutlined className="text-xl mr-3" />
                  <span className="text-sm font-medium">生成报告</span>
                </Button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="group"
              >
                <Button
                  size="large"
                  className="w-full h-20 bg-gradient-to-br from-orange-500 to-orange-600 border-0 shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-white hover:text-white group-hover:from-orange-600 group-hover:to-orange-700"
                >
                  <FileExcelOutlined className="text-xl mr-3" />
                  <span className="text-sm font-medium">数据导出</span>
                </Button>
              </motion.div>
            </div>
          </Card>
        </motion.div>

        {/* 审计轨迹 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mb-8"
        >
          <Card title="📋 审计轨迹" className="shadow-lg">
            <div className="space-y-4">
              {/* 实时处理步骤 */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
                <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <span className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm mr-3">
                    🔄
                  </span>
                  实时处理步骤
                </h4>
                <div className="space-y-3">
                  {[
                    { step: '文档接收', time: '14:32:15', status: 'completed', desc: '成功接收 财务报告_001.pdf' },
                    { step: 'OCR识别', time: '14:32:18', status: 'completed', desc: '文字识别完成，准确率 98.5%' },
                    { step: '内容解析', time: '14:32:22', status: 'completed', desc: '提取关键财务数据和指标' },
                    { step: '异常检测', time: '14:32:25', status: 'processing', desc: '正在进行合规性检查...' },
                    { step: '质量评估', time: '14:32:28', status: 'pending', desc: '等待异常检测完成' },
                    { step: '结果输出', time: '14:32:30', status: 'pending', desc: '生成分析报告和建议' }
                  ].map((item, index) => (
                    <div key={index} className="flex items-center space-x-4 p-3 bg-white rounded-lg shadow-sm">
                      <div className={`w-3 h-3 rounded-full ${
                        item.status === 'completed' ? 'bg-green-500' :
                        item.status === 'processing' ? 'bg-blue-500 animate-pulse' :
                        'bg-gray-300'
                      }`} />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <span className="font-medium text-gray-800">{item.step}</span>
                          <span className="text-sm text-gray-500">{item.time}</span>
                        </div>
                        <div className="text-sm text-gray-600 mt-1">{item.desc}</div>
                      </div>
                      <div className={`px-2 py-1 rounded-full text-xs ${
                        item.status === 'completed' ? 'bg-green-100 text-green-700' :
                        item.status === 'processing' ? 'bg-blue-100 text-blue-700' :
                        'bg-gray-100 text-gray-600'
                      }`}>
                        {item.status === 'completed' ? '已完成' :
                         item.status === 'processing' ? '处理中' : '等待中'}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 复核模式 */}
              <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-100">
                <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <span className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-sm mr-3">
                    ⚠️
                  </span>
                  异常复核模式
                </h4>
                <div className="space-y-3">
                  {[
                    {
                      file: '增值税普通发票_002.jpeg',
                      issue: '金额异常',
                      detail: '发票金额 ¥3,250.00 超出同类采购平均值 30%',
                      severity: 'high',
                      action: '需要人工复核'
                    },
                    {
                      file: '餐饮服务发票_004.jpeg',
                      issue: '超出限额',
                      detail: '单次餐饮费用 ¥1,256.50 超出公司标准 ¥800.00',
                      severity: 'medium',
                      action: '建议核实业务合理性'
                    },
                    {
                      file: '办公用品发票_007.jpeg',
                      issue: '税率计算错误',
                      detail: '税率应为 13%，实际为 9%，可能存在计算错误',
                      severity: 'high',
                      action: '需要重新核算'
                    }
                  ].map((item, index) => (
                    <div key={index} className="bg-white rounded-lg p-4 shadow-sm border-l-4 border-orange-400">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="font-medium text-gray-800">{item.file}</span>
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              item.severity === 'high' ? 'bg-red-100 text-red-700' :
                              'bg-yellow-100 text-yellow-700'
                            }`}>
                              {item.severity === 'high' ? '高风险' : '中风险'}
                            </span>
                          </div>
                          <div className="text-sm text-gray-600 mb-1">
                            <strong>{item.issue}:</strong> {item.detail}
                          </div>
                          <div className="text-sm text-blue-600">{item.action}</div>
                        </div>
                        <div className="flex space-x-2 ml-4">
                          <Button size="small" type="primary" className="bg-green-500 border-green-500">
                            通过
                          </Button>
                          <Button size="small" danger>
                            拒绝
                          </Button>
                          <Button size="small">
                            详情
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-4 flex justify-between items-center">
                  <div className="text-sm text-gray-600">
                    共发现 <span className="font-semibold text-orange-600">3</span> 个异常项目，
                    需要复核 <span className="font-semibold text-red-600">2</span> 个高风险项目
                  </div>
                  <div className="space-x-2">
                    <Button size="small" type="primary" className="bg-green-500 border-green-500">
                      一键通过正常项目
                    </Button>
                    <Button size="small" className="bg-orange-500 text-white border-orange-500">
                      批量处理异常
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>

      {/* 票据墙组件 */}
      {showTicketWall && (
        <div className="fixed inset-0 bg-gray-900 z-50 overflow-auto">
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold text-white">票据识别墙</h1>
              <Button onClick={() => setShowTicketWall(false)} className="bg-gray-700 text-white">
                返回
              </Button>
            </div>

            {/* 处理中的文件 */}
            {processingFiles.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg text-white mb-4">正在处理中...</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {processingFiles.map((file, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="bg-gray-800 p-4 rounded-lg"
                    >
                      <div className="text-white mb-2">{file.name}</div>
                      <Progress percent={file.progress} status="active" />
                      <div className="text-sm text-gray-400 mt-2">
                        OCR识别中... {file.progress}%
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {/* 票据瀑布流 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {tickets.map((ticket, index) => (
                <motion.div
                  key={ticket.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{
                    opacity: 1,
                    y: 0,
                    // 异常票据抖动效果
                    x: ticket.status === 'anomaly' ? [0, -2, 2, -2, 2, 0] : 0
                  }}
                  transition={{
                    delay: index * 0.1,
                    x: { repeat: ticket.status === 'anomaly' ? Infinity : 0, duration: 0.5 }
                  }}
                  className={`bg-gray-800 rounded-lg p-4 cursor-pointer hover:bg-gray-700 transition-colors ${
                    ticket.status === 'anomaly' ? 'border-2 border-red-500' :
                    ticket.status === 'warning' ? 'border-2 border-yellow-500' :
                    'border border-gray-600'
                  }`}
                  onClick={() => setSelectedFile(ticket)}
                >
                  {/* 票据预览 */}
                  <div className="bg-gray-700 h-32 rounded mb-3 overflow-hidden">
                    <img
                      src={ticket.imagePath}
                      alt={ticket.name}
                      className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                    />
                  </div>

                  {/* 票据信息 */}
                  <div className="space-y-2">
                    <div className="text-white font-medium text-sm truncate">
                      {ticket.name}
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-green-400">
                        ¥{ticket.amount.toFixed(2)}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded ${
                        ticket.status === 'normal' ? 'bg-green-600 text-white' :
                        ticket.status === 'warning' ? 'bg-yellow-600 text-white' :
                        'bg-red-600 text-white'
                      }`}>
                        {ticket.status === 'normal' ? '正常' :
                         ticket.status === 'warning' ? '警告' : '异常'}
                      </span>
                    </div>
                    <div className="text-xs text-gray-400">
                      置信度: {ticket.confidence}%
                    </div>
                    <div className="text-xs text-gray-400">
                      {ticket.date}
                    </div>
                    {ticket.anomalies && (
                      <div className="text-xs text-red-400">
                        {ticket.anomalies.join(', ')}
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* 票据详情弹窗 */}
          <Modal
            title="票据详情"
            open={!!selectedFile}
            onCancel={() => setSelectedFile(null)}
            width={800}
            footer={[
              <Button key="edit" type="primary">
                编辑信息
              </Button>,
              <Button key="approve" type="primary" className="bg-green-600">
                审核通过
              </Button>,
              <Button key="reject" danger>
                驳回
              </Button>,
              <Button key="close" onClick={() => setSelectedFile(null)}>
                关闭
              </Button>
            ]}
          >
            {selectedFile && (
              <div className="space-y-4">
                {/* 发票图片 */}
                <div className="text-center">
                  <img
                    src={selectedFile.imagePath}
                    alt={selectedFile.name}
                    className="max-w-full max-h-96 object-contain border rounded"
                  />
                </div>

                {/* 基本信息 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="font-medium text-gray-700">文件名</label>
                    <div className="text-gray-900">{selectedFile.name}</div>
                  </div>
                  <div>
                    <label className="font-medium text-gray-700">金额</label>
                    <div className="text-2xl font-bold text-green-600">
                      ¥{selectedFile.amount?.toFixed(2)}
                    </div>
                  </div>
                  <div>
                    <label className="font-medium text-gray-700">日期</label>
                    <div className="text-gray-900">{selectedFile.date}</div>
                  </div>
                  <div>
                    <label className="font-medium text-gray-700">置信度</label>
                    <div className="text-gray-900">{selectedFile.confidence}%</div>
                  </div>
                </div>

                {/* 详细字段 */}
                <div>
                  <h4 className="font-medium text-gray-700 mb-2">提取信息</h4>
                  <div className="bg-gray-50 p-3 rounded space-y-2">
                    {Object.entries(selectedFile.fields || {}).map(([key, value]) => (
                      <div key={key} className="flex justify-between">
                        <span className="text-gray-600">{key}:</span>
                        <span className="text-gray-900">{value}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 异常信息 */}
                {selectedFile.anomalies && selectedFile.anomalies.length > 0 && (
                  <div>
                    <h4 className="font-medium text-red-600 mb-2">异常信息</h4>
                    <div className="space-y-1">
                      {selectedFile.anomalies.map((anomaly, index) => (
                        <div key={index} className="text-red-600 text-sm">
                          • {anomaly}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </Modal>
        </div>
      )}
    </DashboardLayout>
  )
}
