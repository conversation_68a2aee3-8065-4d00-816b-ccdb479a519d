<template>
  <div class="min-h-screen bg-slate-50">
    <!-- 顶部工具栏 -->
    <header class="bg-white shadow-sm border-b border-slate-200">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <router-link to="/dashboard" class="text-slate-400 hover:text-slate-600">
              <ArrowLeftOutlined class="w-5 h-5" />
            </router-link>
            <div>
              <h1 class="text-xl font-bold text-slate-900">文档实验室</h1>
              <p class="text-sm text-slate-500">Document Lab - OCR识别与智能审核</p>
            </div>
          </div>
          
          <div class="flex items-center space-x-3">
            <a-badge :count="processedDocuments.length" class="mr-2">
              <a-button @click="showProcessedDocs">
                <FileTextOutlined />
                已处理文档
              </a-button>
            </a-badge>
            <a-button @click="clearAll">
              <ClearOutlined />
              清空全部
            </a-button>
            <a-button type="primary" @click="batchProcess">
              <ThunderboltOutlined />
              批量处理
            </a-button>
          </div>
        </div>
      </div>
    </header>

    <div class="flex h-screen">
      <!-- 左侧上传区域 -->
      <div class="w-1/3 p-6">
        <!-- 拖拽上传区 -->
        <div class="mb-6">
          <a-upload-dragger
            v-model:file-list="fileList"
            name="files"
            :multiple="true"
            :before-upload="handleBeforeUpload"
            :show-upload-list="false"
            accept=".pdf,.jpg,.jpeg,.png,.xlsx,.xls,.csv"
            class="upload-dragger"
          >
            <div class="p-8">
              <div class="flex justify-center mb-4">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                  <CloudUploadOutlined class="w-8 h-8 text-blue-600" />
                </div>
              </div>
              <h3 class="text-lg font-medium text-slate-900 mb-2">拖拽文件到此处</h3>
              <p class="text-sm text-slate-500 mb-4">
                支持 PDF、图片、Excel 等格式<br>
                单个文件最大 10MB，最多 50 个文件
              </p>
              <a-button type="primary">
                <UploadOutlined />
                选择文件
              </a-button>
            </div>
          </a-upload-dragger>
        </div>

        <!-- 处理进度 -->
        <div v-if="uploadProgress.length > 0" class="space-y-3">
          <h3 class="text-lg font-medium text-slate-900">处理进度</h3>
          <div
            v-for="progress in uploadProgress"
            :key="progress.id"
            class="bg-white rounded-lg p-4 shadow-sm border border-slate-200"
          >
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-slate-700">{{ progress.name }}</span>
              <span class="text-xs text-slate-500">{{ progress.status }}</span>
            </div>
            <div class="w-full bg-slate-200 rounded-full h-2">
              <div
                class="h-2 rounded-full transition-all duration-300"
                :class="progress.error ? 'bg-red-500' : 'bg-blue-600'"
                :style="{ width: progress.percent + '%' }"
              ></div>
            </div>
            <div v-if="progress.error" class="text-xs text-red-600 mt-1">
              {{ progress.error }}
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="mt-6">
          <h3 class="text-lg font-medium text-slate-900 mb-3">快速操作</h3>
          <div class="space-y-2">
            <a-button block @click="uploadSampleInvoices">
              <FileTextOutlined />
              上传示例发票
            </a-button>
            <a-button block @click="uploadSampleContracts">
              <FilePdfOutlined />
              上传示例合同
            </a-button>
            <a-button block @click="uploadSampleReports">
              <FileExcelOutlined />
              上传示例报表
            </a-button>
          </div>
        </div>
      </div>

      <!-- 中间票据墙 -->
      <div class="flex-1 p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-bold text-slate-900">票据墙</h2>
          <div class="flex items-center space-x-2">
            <a-select v-model:value="filterStatus" placeholder="筛选状态" class="w-32">
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="processing">处理中</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
              <a-select-option value="error">异常</a-select-option>
            </a-select>
            <a-input
              v-model:value="searchKeyword"
              placeholder="搜索文档..."
              class="w-48"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>
          </div>
        </div>

        <!-- 瀑布流文档展示 -->
        <div class="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 max-h-screen overflow-y-auto">
          <div
            v-for="doc in filteredDocuments"
            :key="doc.id"
            class="bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden cursor-pointer transition-all duration-200 hover:shadow-lg"
            :class="{
              'border-red-300 animate-pulse': doc.status === 'error',
              'border-yellow-300': doc.status === 'processing',
              'border-green-300': doc.status === 'completed'
            }"
            @click="selectDocument(doc)"
          >
            <!-- 文档预览 -->
            <div class="aspect-[3/4] bg-slate-100 relative">
              <img
                v-if="doc.thumbnail"
                :src="doc.thumbnail"
                :alt="doc.name"
                class="w-full h-full object-cover"
              />
              <div v-else class="w-full h-full flex items-center justify-center">
                <component :is="getFileIcon(doc.type)" class="w-12 h-12 text-slate-400" />
              </div>
              
              <!-- 状态指示器 -->
              <div class="absolute top-2 right-2">
                <div
                  class="w-3 h-3 rounded-full"
                  :class="{
                    'bg-red-500': doc.status === 'error',
                    'bg-yellow-500': doc.status === 'processing',
                    'bg-green-500': doc.status === 'completed'
                  }"
                ></div>
              </div>
              
              <!-- 异常抖动效果 -->
              <div
                v-if="doc.hasAnomalies"
                class="absolute inset-0 border-2 border-red-500 rounded animate-pulse"
              ></div>
            </div>
            
            <!-- 文档信息 -->
            <div class="p-3">
              <h4 class="font-medium text-slate-900 text-sm truncate">{{ doc.name }}</h4>
              <p class="text-xs text-slate-500 mt-1">{{ doc.type }} · {{ formatFileSize(doc.size) }}</p>
              <div class="flex items-center justify-between mt-2">
                <span class="text-xs" :class="getStatusColor(doc.status)">
                  {{ getStatusText(doc.status) }}
                </span>
                <span class="text-xs text-slate-400">{{ formatTime(doc.uploadTime) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧审计轨迹 -->
      <aside class="w-80 bg-white shadow-sm border-l border-slate-200">
        <div class="p-4 border-b border-slate-200">
          <h2 class="text-lg font-bold text-slate-900">审计轨迹</h2>
          <p class="text-sm text-slate-500">实时处理日志</p>
        </div>
        
        <div class="p-4 h-full overflow-y-auto">
          <div v-if="selectedDocument" class="mb-6">
            <h3 class="font-medium text-slate-900 mb-3">{{ selectedDocument.name }}</h3>
            
            <!-- OCR结果 -->
            <div class="mb-4">
              <h4 class="text-sm font-medium text-slate-700 mb-2">OCR识别结果</h4>
              <div class="bg-slate-50 rounded-lg p-3 text-sm">
                <div v-if="selectedDocument.ocrResult">
                  <p><strong>发票号码:</strong> {{ selectedDocument.ocrResult.invoiceNumber }}</p>
                  <p><strong>金额:</strong> ¥{{ selectedDocument.ocrResult.amount }}</p>
                  <p><strong>开票日期:</strong> {{ selectedDocument.ocrResult.date }}</p>
                  <p><strong>供应商:</strong> {{ selectedDocument.ocrResult.vendor }}</p>
                </div>
                <div v-else class="text-slate-400">
                  正在识别中...
                </div>
              </div>
            </div>
            
            <!-- 合规检查 -->
            <div class="mb-4">
              <h4 class="text-sm font-medium text-slate-700 mb-2">合规性检查</h4>
              <div class="space-y-2">
                <div
                  v-for="check in selectedDocument.complianceChecks"
                  :key="check.rule"
                  class="flex items-center justify-between text-sm"
                >
                  <span>{{ check.rule }}</span>
                  <span :class="check.passed ? 'text-green-600' : 'text-red-600'">
                    {{ check.passed ? '✓' : '✗' }}
                  </span>
                </div>
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="space-y-2">
              <a-button type="primary" block @click="approveDocument">
                <CheckOutlined />
                通过审核
              </a-button>
              <a-button danger block @click="rejectDocument">
                <CloseOutlined />
                驳回
              </a-button>
            </div>
          </div>
          
          <!-- 处理日志 -->
          <div>
            <h3 class="font-medium text-slate-900 mb-3">处理日志</h3>
            <div class="space-y-3">
              <div
                v-for="log in auditLogs"
                :key="log.id"
                class="text-sm border-l-2 pl-3"
                :class="getLogBorderColor(log.type)"
              >
                <div class="flex items-center justify-between">
                  <span class="font-medium">{{ log.action }}</span>
                  <span class="text-xs text-slate-400">{{ formatTime(log.timestamp) }}</span>
                </div>
                <p class="text-slate-600 mt-1">{{ log.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </aside>
    </div>

    <!-- 复核模式模态框 -->
    <a-modal
      v-model:open="reviewModalVisible"
      title="文档复核"
      width="80%"
      :footer="null"
    >
      <div v-if="reviewDocument" class="grid grid-cols-2 gap-6">
        <!-- 左侧：原始文档 -->
        <div>
          <h3 class="text-lg font-medium mb-3">原始文档</h3>
          <div class="border border-slate-200 rounded-lg p-4 bg-slate-50">
            <img
              v-if="reviewDocument.thumbnail"
              :src="reviewDocument.thumbnail"
              :alt="reviewDocument.name"
              class="w-full h-auto rounded"
            />
          </div>
        </div>
        
        <!-- 右侧：识别结果对比 -->
        <div>
          <h3 class="text-lg font-medium mb-3">识别结果 vs ERP记录</h3>
          <div class="space-y-4">
            <div
              v-for="field in reviewDocument.diffFields"
              :key="field.name"
              class="border rounded-lg p-3"
              :class="field.match ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'"
            >
              <div class="font-medium text-sm mb-2">{{ field.label }}</div>
              <div class="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span class="text-slate-500">OCR识别:</span>
                  <div class="font-mono">{{ field.ocrValue }}</div>
                </div>
                <div>
                  <span class="text-slate-500">ERP记录:</span>
                  <div class="font-mono">{{ field.erpValue }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="mt-6 flex space-x-3">
            <a-button type="primary" @click="approveReview">通过</a-button>
            <a-button danger @click="rejectReview">驳回</a-button>
            <a-button @click="reviewModalVisible = false">取消</a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  FileTextOutlined,
  ClearOutlined,
  ThunderboltOutlined,
  CloudUploadOutlined,
  UploadOutlined,
  FilePdfOutlined,
  FileExcelOutlined,
  SearchOutlined,
  CheckOutlined,
  CloseOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const fileList = ref<any[]>([])
const uploadProgress = ref<any[]>([])
const documents = ref<any[]>([])
const processedDocuments = ref<any[]>([])
const selectedDocument = ref<any>(null)
const reviewModalVisible = ref(false)
const reviewDocument = ref<any>(null)
const filterStatus = ref('')
const searchKeyword = ref('')
const auditLogs = ref<any[]>([])

// 计算属性
const filteredDocuments = computed(() => {
  let filtered = documents.value
  
  if (filterStatus.value) {
    filtered = filtered.filter(doc => doc.status === filterStatus.value)
  }
  
  if (searchKeyword.value) {
    filtered = filtered.filter(doc => 
      doc.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  
  return filtered
})

// 方法
const handleBeforeUpload = (file: File) => {
  // 文件大小检查
  if (file.size > 10 * 1024 * 1024) {
    message.error('文件大小不能超过10MB')
    return false
  }
  
  // 开始处理文件
  processFile(file)
  return false // 阻止默认上传
}

const processFile = async (file: File) => {
  const fileId = Date.now().toString()
  
  // 添加到进度列表
  const progress = {
    id: fileId,
    name: file.name,
    status: '上传中...',
    percent: 0,
    error: null
  }
  uploadProgress.value.push(progress)
  
  // 创建文档对象
  const document = {
    id: fileId,
    name: file.name,
    type: getFileType(file.name),
    size: file.size,
    status: 'processing',
    uploadTime: new Date(),
    thumbnail: null,
    hasAnomalies: false,
    ocrResult: null,
    complianceChecks: []
  }
  
  documents.value.push(document)
  
  try {
    // 模拟文件处理过程
    await simulateFileProcessing(progress, document)
    
    // 移除进度项
    uploadProgress.value = uploadProgress.value.filter(p => p.id !== fileId)
    
    message.success(`${file.name} 处理完成`)
  } catch (error) {
    progress.error = '处理失败'
    progress.status = '错误'
    document.status = 'error'
  }
}

const simulateFileProcessing = async (progress: any, document: any) => {
  // 阶段1: 上传
  progress.status = '上传中...'
  progress.percent = 20
  await new Promise(resolve => setTimeout(resolve, 500))
  
  // 阶段2: OCR识别
  progress.status = 'OCR识别中...'
  progress.percent = 50
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 模拟OCR结果
  document.ocrResult = {
    invoiceNumber: 'INV-' + Math.random().toString(36).substr(2, 9).toUpperCase(),
    amount: (Math.random() * 10000 + 1000).toFixed(2),
    date: new Date().toISOString().split('T')[0],
    vendor: '示例供应商有限公司'
  }
  
  // 阶段3: 合规检查
  progress.status = '合规检查中...'
  progress.percent = 80
  await new Promise(resolve => setTimeout(resolve, 800))
  
  // 模拟合规检查结果
  document.complianceChecks = [
    { rule: '发票真伪验证', passed: true },
    { rule: '金额范围检查', passed: true },
    { rule: '供应商白名单', passed: Math.random() > 0.3 },
    { rule: '重复发票检查', passed: Math.random() > 0.2 }
  ]
  
  // 检查是否有异常
  document.hasAnomalies = document.complianceChecks.some(check => !check.passed)
  
  // 阶段4: 完成
  progress.status = '处理完成'
  progress.percent = 100
  document.status = 'completed'
  
  // 添加审计日志
  addAuditLog('文档处理', `${document.name} 处理完成`, 'success')
}

const getFileType = (filename: string) => {
  const ext = filename.split('.').pop()?.toLowerCase()
  switch (ext) {
    case 'pdf': return 'PDF'
    case 'jpg':
    case 'jpeg':
    case 'png': return '图片'
    case 'xlsx':
    case 'xls': return 'Excel'
    case 'csv': return 'CSV'
    default: return '未知'
  }
}

const getFileIcon = (type: string) => {
  switch (type) {
    case 'PDF': return FilePdfOutlined
    case 'Excel': return FileExcelOutlined
    default: return FileTextOutlined
  }
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed': return 'text-green-600'
    case 'processing': return 'text-yellow-600'
    case 'error': return 'text-red-600'
    default: return 'text-slate-500'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'processing': return '处理中'
    case 'error': return '异常'
    default: return '未知'
  }
}

const getLogBorderColor = (type: string) => {
  switch (type) {
    case 'success': return 'border-green-500'
    case 'warning': return 'border-yellow-500'
    case 'error': return 'border-red-500'
    default: return 'border-blue-500'
  }
}

const selectDocument = (doc: any) => {
  selectedDocument.value = doc
  
  if (doc.hasAnomalies) {
    // 如果有异常，打开复核模式
    reviewDocument.value = {
      ...doc,
      diffFields: [
        {
          name: 'amount',
          label: '发票金额',
          ocrValue: doc.ocrResult?.amount,
          erpValue: (parseFloat(doc.ocrResult?.amount || '0') * 0.95).toFixed(2),
          match: false
        },
        {
          name: 'vendor',
          label: '供应商名称',
          ocrValue: doc.ocrResult?.vendor,
          erpValue: doc.ocrResult?.vendor,
          match: true
        }
      ]
    }
    reviewModalVisible.value = true
  }
}

const approveDocument = () => {
  if (!selectedDocument.value) return
  
  selectedDocument.value.status = 'approved'
  processedDocuments.value.push(selectedDocument.value)
  addAuditLog('审核通过', `${selectedDocument.value.name} 审核通过`, 'success')
  message.success('文档审核通过')
}

const rejectDocument = () => {
  if (!selectedDocument.value) return
  
  selectedDocument.value.status = 'rejected'
  addAuditLog('审核驳回', `${selectedDocument.value.name} 审核驳回`, 'error')
  message.warning('文档已驳回')
}

const approveReview = () => {
  reviewModalVisible.value = false
  approveDocument()
}

const rejectReview = () => {
  reviewModalVisible.value = false
  rejectDocument()
}

const addAuditLog = (action: string, description: string, type: string = 'info') => {
  auditLogs.value.unshift({
    id: Date.now(),
    action,
    description,
    type,
    timestamp: new Date()
  })
  
  // 保持最多50条日志
  if (auditLogs.value.length > 50) {
    auditLogs.value = auditLogs.value.slice(0, 50)
  }
}

const uploadSampleInvoices = () => {
  // 模拟上传示例发票
  const sampleFiles = [
    { name: '电费发票_202401.pdf', size: 1024 * 500 },
    { name: '办公用品发票_202401.jpg', size: 1024 * 800 },
    { name: '差旅费发票_202401.pdf', size: 1024 * 600 }
  ]
  
  sampleFiles.forEach(file => {
    processFile(file as File)
  })
  
  message.success('已添加示例发票')
}

const uploadSampleContracts = () => {
  message.success('已添加示例合同')
}

const uploadSampleReports = () => {
  message.success('已添加示例报表')
}

const showProcessedDocs = () => {
  message.info(`共有 ${processedDocuments.value.length} 个已处理文档`)
}

const clearAll = () => {
  documents.value = []
  uploadProgress.value = []
  selectedDocument.value = null
  auditLogs.value = []
  message.success('已清空所有文档')
}

const batchProcess = () => {
  const pendingDocs = documents.value.filter(doc => doc.status === 'processing')
  if (pendingDocs.length === 0) {
    message.warning('没有待处理的文档')
    return
  }
  
  message.success(`正在批量处理 ${pendingDocs.length} 个文档...`)
  
  // 模拟批量处理
  pendingDocs.forEach(doc => {
    setTimeout(() => {
      doc.status = 'completed'
      addAuditLog('批量处理', `${doc.name} 批量处理完成`, 'success')
    }, Math.random() * 3000)
  })
}

// 生命周期
onMounted(() => {
  // 初始化一些示例日志
  addAuditLog('系统启动', '文档实验室已启动', 'info')
})
</script>

<style scoped>
.upload-dragger :deep(.ant-upload-drag) {
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  background: #f8fafc;
  transition: all 0.3s ease;
}

.upload-dragger :deep(.ant-upload-drag:hover) {
  border-color: #3b82f6;
  background: #eff6ff;
}

.upload-dragger :deep(.ant-upload-drag-hover) {
  border-color: #3b82f6 !important;
  background: #eff6ff !important;
}
</style>
