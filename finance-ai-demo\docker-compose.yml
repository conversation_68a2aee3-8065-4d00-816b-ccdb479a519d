version: '3.8'

services:
  finance-ai-demo:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: finance-ai-demo
    ports:
      - "5666:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
      # Dify API配置
      - DIFY_API_URL=https://ai.hongtai-idi.com/v1
      - DIFY_API_KEY=app-xuReGCaYZmkWMn0JGAAxqBvg
    volumes:
      # 挂载智能体数据目录，确保数据持久化
      - ./data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - finance-ai-network

networks:
  finance-ai-network:
    driver: bridge
