# 🪟 Windows系统部署指南

## 📋 Windows部署方式总览

### 🏠 **本地部署（推荐新手）**
在Windows电脑上直接运行，适合开发测试

### 🌐 **Windows服务器部署**
部署到Windows Server，适合生产环境

---

## 🏠 本地部署（Windows电脑）

### 📦 **前提条件**

#### 1. 安装Docker Desktop
```powershell
# 1. 下载Docker Desktop for Windows
# 访问: https://www.docker.com/products/docker-desktop/

# 2. 安装并重启电脑

# 3. 确保WSL2已启用
# 在PowerShell中运行:
wsl --install
```

#### 2. 验证安装
```cmd
# 打开命令提示符，验证Docker安装
docker --version
docker-compose --version
```

### 🚀 **一键部署**

#### 方式一：使用批处理脚本（最简单）
```cmd
# 1. 打开命令提示符
# 2. 进入项目目录
cd finance-ai-demo

# 3. 运行部署脚本
deploy.bat
```

#### 方式二：手动命令
```cmd
# 1. 停止现有容器
docker-compose down --remove-orphans

# 2. 构建镜像
docker-compose build --no-cache

# 3. 启动服务
docker-compose up -d

# 4. 验证部署
# 在浏览器访问: http://localhost:5666
```

### ✅ **验证部署成功**
- 🌐 **主页**: http://localhost:5666
- 💬 **智能对话**: http://localhost:5666/chat
- 🤖 **智能体管理**: http://localhost:5666/agent-library
- 🏥 **健康检查**: http://localhost:5666/api/health

---

## 🌐 Windows服务器部署

### 🖥️ **服务器要求**

#### 硬件配置
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 20GB以上
- **网络**: 稳定的互联网连接

#### 软件要求
- **操作系统**: Windows Server 2019/2022 或 Windows 10/11
- **Docker Desktop**: 最新版本
- **PowerShell**: 5.0以上

### 📦 **服务器环境准备**

#### 1. 安装Docker Desktop
```powershell
# 在服务器上安装Docker Desktop
# 1. 下载安装包
# 2. 以管理员身份运行安装
# 3. 重启服务器
# 4. 启动Docker Desktop
```

#### 2. 配置防火墙
```powershell
# 以管理员身份运行PowerShell

# 允许HTTP端口
New-NetFirewallRule -DisplayName "HTTP" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow

# 允许应用端口
New-NetFirewallRule -DisplayName "Finance-AI" -Direction Inbound -Protocol TCP -LocalPort 5666 -Action Allow

# 允许HTTPS端口（可选）
New-NetFirewallRule -DisplayName "HTTPS" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow
```

### 🚀 **部署步骤**

#### 1. 上传项目文件
```powershell
# 方式一：使用Git（如果服务器有Git）
git clone <repository-url>
cd finance-ai-demo

# 方式二：手动上传
# 1. 将项目文件压缩为zip
# 2. 上传到服务器
# 3. 解压到 C:\finance-ai-demo
```

#### 2. 执行部署
```cmd
# 进入项目目录
cd C:\finance-ai-demo

# 运行部署脚本
deploy.bat
```

#### 3. 验证部署
```powershell
# 检查服务状态
docker-compose ps

# 测试访问
# 在浏览器访问: http://服务器IP:5666
```

### 🔧 **Windows服务器管理命令**

#### 查看服务状态
```cmd
# 查看容器状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 查看资源使用
docker stats
```

#### 重启服务
```cmd
# 重启应用
docker-compose restart

# 完全重新部署
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

#### 备份数据
```cmd
# 备份智能体配置
copy data\agents.json data\agents_backup_%date:~0,4%%date:~5,2%%date:~8,2%.json
```

---

## 🌐 域名和HTTPS配置（Windows）

### 📝 **域名配置**

#### 1. 购买域名
- 在域名注册商购买域名
- 配置DNS A记录指向服务器IP

#### 2. 修改端口映射
```yaml
# 编辑 docker-compose.yml
services:
  finance-ai-demo:
    ports:
      - "80:3000"  # 使用80端口
```

### 🔒 **SSL证书配置**

#### 1. 获取SSL证书
```powershell
# 方式一：使用Let's Encrypt (需要安装Certbot)
# 方式二：从云服务商获取免费证书
# 方式三：购买商业证书
```

#### 2. 配置Nginx（可选）
```yaml
# 使用 docker-compose.prod.yml
docker-compose -f docker-compose.prod.yml up -d
```

---

## 🛠️ Windows特有问题解决

### ❌ **常见问题**

#### 1. Docker Desktop启动失败
```powershell
# 解决方案：
# 1. 确保Hyper-V已启用
# 2. 确保WSL2已安装
# 3. 重启电脑
# 4. 以管理员身份运行Docker Desktop
```

#### 2. 端口被占用
```cmd
# 查看端口占用
netstat -ano | findstr :3000

# 结束占用进程
taskkill /PID <进程ID> /F
```

#### 3. 防火墙阻止访问
```powershell
# 临时关闭防火墙测试
Set-NetFirewallProfile -Profile Domain,Public,Private -Enabled False

# 重新启用防火墙
Set-NetFirewallProfile -Profile Domain,Public,Private -Enabled True
```

#### 4. 内存不足
```cmd
# 清理Docker缓存
docker system prune -f

# 重启Docker Desktop
```

### 💡 **Windows优化建议**

#### 1. 性能优化
```powershell
# 增加Docker内存限制
# 在Docker Desktop设置中调整内存分配

# 关闭不必要的Windows服务
# 使用服务管理器(services.msc)
```

#### 2. 安全配置
```powershell
# 启用Windows Defender
# 配置自动更新
# 定期备份数据
```

---

## 📞 Windows部署支持

### 🆘 **获取帮助**

#### 技术支持
- **文档**: 查看项目README.md
- **日志**: 检查Docker容器日志
- **社区**: 搜索相关问题解决方案

#### 常用资源
- **Docker Desktop文档**: https://docs.docker.com/desktop/windows/
- **Windows Server文档**: https://docs.microsoft.com/windows-server/
- **PowerShell文档**: https://docs.microsoft.com/powershell/

### ✅ **部署检查清单**

- [ ] Docker Desktop已安装并运行
- [ ] 防火墙端口已开放
- [ ] 项目文件已上传到服务器
- [ ] deploy.bat脚本执行成功
- [ ] 容器状态正常
- [ ] 应用可以正常访问
- [ ] 智能体功能正常
- [ ] 数据备份已配置

---

## 🎉 Windows部署完成！

恭喜！您已成功在Windows系统上部署了吉发集团财务AI平台。

**访问地址**: http://localhost:3000 (本地) 或 http://服务器IP:3000 (服务器)

如有任何问题，请参考上述故障排除指南或联系技术支持。
