'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { motion } from 'framer-motion'
import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer } from 'antd'
import {
  DashboardOutlined,
  MessageOutlined,
  ExperimentOutlined,
  FileTextOutlined,
  PlayCircleOutlined,
  BarChartOutlined,
  MenuOutlined,
  LogoutOutlined,
  UserOutlined
} from '@ant-design/icons'

const Navigation = () => {
  const router = useRouter()
  const pathname = usePathname()
  const [drawerVisible, setDrawerVisible] = useState(false)

  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '高管仪表板',
      description: 'Executive Dashboard'
    },
    {
      key: '/chat',
      icon: <MessageOutlined />,
      label: '智能助手',
      description: 'Finance Copilot Chat'
    },
    {
      key: '/insight-studio',
      icon: <ExperimentOutlined />,
      label: '财务分析中心',
      description: 'Financial Analysis Center'
    },
    {
      key: '/document-lab',
      icon: <FileTextOutlined />,
      label: '智能审核平台',
      description: 'Intelligent Audit Platform'
    },
    {
      key: '/simulation',
      icon: <PlayCircleOutlined />,
      label: '风险建模系统',
      description: 'Risk Modeling System'
    },
    {
      key: '/boardroom',
      icon: <BarChartOutlined />,
      label: '决策支持系统',
      description: 'Decision Support System'
    }
  ]

  const handleMenuClick = (key: string) => {
    console.log('Navigation clicked:', key)
    router.push(key)
    setDrawerVisible(false)
  }

  const handleLogout = () => {
    localStorage.removeItem('finance_ai_token')
    localStorage.removeItem('finance_ai_user')
    router.push('/')
  }

  const [currentUser, setCurrentUser] = useState({ username: '用户' })

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const user = JSON.parse(localStorage.getItem('finance_ai_user') || '{"username":"用户"}')
      setCurrentUser(user)
    }
  }, [])

  return (
    <>
      {/* 桌面端侧边栏 */}
      <div className="flex flex-col w-64 fixed inset-y-0 bg-white border-r border-slate-200 shadow-lg z-50">
        {/* Logo区域 */}
        <div className="flex items-center h-16 px-6 border-b border-slate-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-slate-600 to-slate-700 rounded-lg flex items-center justify-center">
              <DashboardOutlined className="text-white text-sm" />
            </div>
            <div>
              <div className="font-bold text-slate-800">财务智能体</div>
              <div className="text-xs text-slate-500">吉发集团</div>
            </div>
          </div>
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 px-4 py-6 space-y-2">
          {menuItems.map((item) => (
            <motion.div
              key={item.key}
              whileHover={{ x: 4 }}
              whileTap={{ scale: 0.98 }}
            >
              <button
                onClick={() => handleMenuClick(item.key)}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-all duration-200 ${
                  pathname === item.key
                    ? 'bg-blue-50 text-blue-600 border-l-4 border-blue-600'
                    : 'text-slate-600 hover:bg-slate-50 hover:text-slate-800'
                }`}
              >
                <span className="text-lg mr-3">{item.icon}</span>
                <div>
                  <div className="font-medium">{item.label}</div>
                  <div className="text-xs opacity-75">{item.description}</div>
                </div>
              </button>
            </motion.div>
          ))}
        </nav>

        {/* 用户信息和退出 */}
        <div className="p-4 border-t border-slate-200">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-8 h-8 bg-slate-200 rounded-full flex items-center justify-center">
              <UserOutlined className="text-slate-600" />
            </div>
            <div>
              <div className="text-sm font-medium text-slate-800">{currentUser.username}</div>
              <div className="text-xs text-slate-500">财务管理员</div>
            </div>
          </div>
          <Button
            type="text"
            icon={<LogoutOutlined />}
            onClick={handleLogout}
            className="w-full text-left text-slate-600 hover:text-red-600"
          >
            退出登录
          </Button>
        </div>
      </div>

      {/* 移动端顶部导航 */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm">
        <div className="flex items-center justify-between h-16 px-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-slate-600 to-slate-700 rounded-lg flex items-center justify-center">
              <DashboardOutlined className="text-white text-sm" />
            </div>
            <div>
              <div className="font-bold text-slate-800">财务智能体</div>
              <div className="text-xs text-slate-500">吉发集团</div>
            </div>
          </div>
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={() => setDrawerVisible(true)}
            className="text-slate-600"
          />
        </div>
      </div>

      {/* 移动端抽屉菜单 */}
      <Drawer
        title="导航菜单"
        placement="left"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        className="lg:hidden"
      >
        <div className="space-y-2">
          {menuItems.map((item) => (
            <button
              key={item.key}
              onClick={() => handleMenuClick(item.key)}
              className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-all duration-200 ${
                pathname === item.key
                  ? 'bg-blue-50 text-blue-600'
                  : 'text-slate-600 hover:bg-slate-50'
              }`}
            >
              <span className="text-lg mr-3">{item.icon}</span>
              <div>
                <div className="font-medium">{item.label}</div>
                <div className="text-xs opacity-75">{item.description}</div>
              </div>
            </button>
          ))}
        </div>
        
        <div className="mt-8 pt-4 border-t border-slate-200">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-slate-200 rounded-full flex items-center justify-center">
              <UserOutlined className="text-slate-600" />
            </div>
            <div>
              <div className="text-sm font-medium text-slate-800">{currentUser.username}</div>
              <div className="text-xs text-slate-500">财务管理员</div>
            </div>
          </div>
          <Button
            type="text"
            icon={<LogoutOutlined />}
            onClick={handleLogout}
            className="w-full text-left text-slate-600 hover:text-red-600"
          >
            退出登录
          </Button>
        </div>
      </Drawer>
    </>
  )
}

export default Navigation
