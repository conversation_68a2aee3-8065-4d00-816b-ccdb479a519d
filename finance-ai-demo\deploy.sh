#!/bin/bash

# 吉发集团财务AI平台 Docker部署脚本
# 作者: Augment Agent
# 日期: 2025-07-09

set -e

echo "🚀 开始部署吉发集团财务AI平台..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查智能体数据文件
if [ ! -f "./data/agents.json" ]; then
    echo "❌ 智能体数据文件不存在: ./data/agents.json"
    echo "请确保已配置好20个智能体数据"
    exit 1
fi

# 显示智能体数量
AGENT_COUNT=$(grep -c '"id":' ./data/agents.json)
echo "📊 检测到 $AGENT_COUNT 个智能体配置"

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down --remove-orphans

# 构建新镜像
echo "🔨 构建Docker镜像..."
docker-compose build --no-cache

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

echo ""
echo "🎉 部署完成！"
echo "📱 访问地址: http://localhost:5666"
echo "🏥 健康检查: http://localhost:5666/api/health"
echo "📊 智能体管理: http://localhost:5666/agent-library"
echo "💬 智能对话: http://localhost:5666/chat"
echo ""
echo "📋 常用命令:"
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart"
echo "  查看状态: docker-compose ps"