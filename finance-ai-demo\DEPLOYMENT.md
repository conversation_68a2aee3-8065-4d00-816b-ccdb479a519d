# 吉发集团财务AI平台 Docker部署指南

## 📋 部署概述

本文档介绍如何使用Docker部署吉发集团财务AI平台，包含预配置的20个智能体。

## 🛠️ 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 10GB可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **Docker**: 20.10.0+
- **Docker Compose**: 1.29.0+
- **操作系统**: 
  - Linux (推荐 Ubuntu 20.04+)
  - Windows 10/11 with WSL2
  - macOS 10.15+

## 📦 部署包内容

```
finance-ai-demo/
├── Dockerfile              # Docker镜像构建文件
├── docker-compose.yml      # Docker Compose配置
├── deploy.sh               # Linux/macOS部署脚本
├── deploy.bat              # Windows部署脚本
├── data/
│   └── agents.json         # 预配置的20个智能体数据
├── src/                    # 应用源代码
├── public/                 # 静态资源
└── DEPLOYMENT.md           # 本文档
```

## 🚀 快速部署

### Linux/macOS 部署

1. **克隆或下载项目**
   ```bash
   # 如果从Git仓库克隆
   git clone <repository-url>
   cd finance-ai-demo
   
   # 或者解压部署包
   tar -xzf finance-ai-demo.tar.gz
   cd finance-ai-demo
   ```

2. **设置执行权限**
   ```bash
   chmod +x deploy.sh
   ```

3. **运行部署脚本**
   ```bash
   ./deploy.sh
   ```

### Windows 部署

1. **解压部署包**
   - 解压 `finance-ai-demo.zip` 到目标目录

2. **运行部署脚本**
   - 双击 `deploy.bat` 文件
   - 或在命令提示符中运行：
     ```cmd
     deploy.bat
     ```

## 🔧 手动部署

如果自动部署脚本无法运行，可以手动执行以下步骤：

1. **停止现有容器**
   ```bash
   docker-compose down --remove-orphans
   ```

2. **构建镜像**
   ```bash
   docker-compose build --no-cache
   ```

3. **启动服务**
   ```bash
   docker-compose up -d
   ```

4. **检查状态**
   ```bash
   docker-compose ps
   ```

## 🌐 访问应用

部署完成后，可以通过以下地址访问：

- **主页**: http://localhost:3000
- **智能体管理**: http://localhost:3000/agent-library
- **智能对话**: http://localhost:3000/chat
- **仪表板**: http://localhost:3000/dashboard
- **健康检查**: http://localhost:3000/api/health

## 📊 预配置智能体

系统预配置了20个专业的财务智能体：

1. 财务智能助手
2. 财务报表洞察智能体
3. 智能预算编制智能体
4. 现金流预测与调度智能体
5. 项目投后管理智能体
6. 投资组合优化智能体
7. 成本算法智能体
8. 费用报销审核智能体
9. 供应链金融智能体
10. 招投标财务评分智能体
11. 财务风险雷达智能体
12. 税务筹划与申报智能体
13. 国资委报表助手智能体
14. IFRS/新准则转换智能体
15. ESG财务披露智能体
16. 内部审计智能体
17. 财务制度知识库问答智能体
18. 会议纪要和任务智能体
19. 知识蒸馏与训练智能体
20. 财务-企业微信联动智能体

## ⚙️ 配置说明

### 环境变量配置

在 `docker-compose.yml` 中可以修改以下配置：

```yaml
environment:
  - NODE_ENV=production
  - PORT=3000
  - HOSTNAME=0.0.0.0
  # Dify API配置
  - DIFY_API_URL=https://ai.hongtai-idi.com/v1
  - DIFY_API_KEY=app-xuReGCaYZmkWMn0JGAAxqBvg
```

### 端口配置

默认端口为3000，如需修改：

```yaml
ports:
  - "8080:3000"  # 将外部端口改为8080
```

### 数据持久化

智能体数据通过卷挂载实现持久化：

```yaml
volumes:
  - ./data:/app/data
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :3000
   
   # 修改docker-compose.yml中的端口映射
   ports:
     - "3001:3000"
   ```

2. **Docker服务未启动**
   ```bash
   # Linux
   sudo systemctl start docker
   
   # Windows
   # 启动Docker Desktop
   ```

3. **权限问题**
   ```bash
   # Linux/macOS
   sudo chown -R $USER:$USER ./data
   chmod 755 ./data
   ```

### 查看日志

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f finance-ai-demo
```

### 重启服务

```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart finance-ai-demo
```

## 🛡️ 安全建议

1. **修改默认API密钥**
   - 在生产环境中，请修改 `DIFY_API_KEY` 为您自己的密钥

2. **网络安全**
   - 如果部署在公网，建议配置防火墙和SSL证书
   - 使用反向代理（如Nginx）进行负载均衡

3. **数据备份**
   ```bash
   # 备份智能体数据
   cp ./data/agents.json ./data/agents.json.backup
   ```

## 📞 技术支持

如遇到部署问题，请检查：

1. Docker和Docker Compose版本是否符合要求
2. 系统资源是否充足
3. 网络连接是否正常
4. 端口是否被占用

更多技术支持，请联系开发团队。
