#!/bin/bash

# 生产环境部署脚本
# 使用方法: ./deploy-production.sh [服务器IP] [用户名]

set -e

SERVER_IP=${1:-"your-server-ip"}
USERNAME=${2:-"ubuntu"}
APP_NAME="finance-ai-demo"
DEPLOY_PATH="/opt/$APP_NAME"

echo "🚀 开始部署到生产服务器..."
echo "📍 服务器: $USERNAME@$SERVER_IP"
echo "📁 部署路径: $DEPLOY_PATH"

# 检查参数
if [ "$SERVER_IP" = "your-server-ip" ]; then
    echo "❌ 请提供服务器IP地址"
    echo "用法: ./deploy-production.sh <服务器IP> [用户名]"
    exit 1
fi

# 创建部署包
echo "📦 创建部署包..."
tar --exclude='node_modules' \
    --exclude='.next' \
    --exclude='.git' \
    --exclude='*.log' \
    -czf ${APP_NAME}.tar.gz .

# 上传到服务器
echo "📤 上传文件到服务器..."
scp ${APP_NAME}.tar.gz $USERNAME@$SERVER_IP:~/

# 在服务器上执行部署
echo "🔧 在服务器上执行部署..."
ssh $USERNAME@$SERVER_IP << EOF
    set -e
    
    echo "📁 准备部署目录..."
    sudo mkdir -p $DEPLOY_PATH
    sudo chown $USER:$USER $DEPLOY_PATH
    
    echo "📦 解压应用文件..."
    cd $DEPLOY_PATH
    tar -xzf ~/${APP_NAME}.tar.gz
    rm ~/${APP_NAME}.tar.gz
    
    echo "🛑 停止现有服务..."
    docker-compose -f docker-compose.prod.yml down || true
    
    echo "🔨 构建新镜像..."
    docker-compose -f docker-compose.prod.yml build --no-cache
    
    echo "🚀 启动服务..."
    docker-compose -f docker-compose.prod.yml up -d
    
    echo "⏳ 等待服务启动..."
    sleep 15
    
    echo "🔍 检查服务状态..."
    docker-compose -f docker-compose.prod.yml ps
    
    echo "🏥 健康检查..."
    curl -f http://localhost:5666/api/health || echo "⚠️ 健康检查失败，请检查日志"
    
    echo "✅ 部署完成！"
EOF

# 清理本地文件
rm ${APP_NAME}.tar.gz

echo ""
echo "🎉 生产环境部署完成！"
echo "🌐 访问地址: http://$SERVER_IP:5666"
echo "🏥 健康检查: http://$SERVER_IP:5666/api/health"
echo ""
echo "📋 常用命令:"
echo "  查看日志: ssh $USERNAME@$SERVER_IP 'cd $DEPLOY_PATH && docker-compose -f docker-compose.prod.yml logs -f'"
echo "  重启服务: ssh $USERNAME@$SERVER_IP 'cd $DEPLOY_PATH && docker-compose -f docker-compose.prod.yml restart'"
echo "  停止服务: ssh $USERNAME@$SERVER_IP 'cd $DEPLOY_PATH && docker-compose -f docker-compose.prod.yml down'"
