import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

// 智能体数据文件路径
const AGENTS_FILE_PATH = path.join(process.cwd(), 'data', 'agents.json')
const DATA_DIR = path.join(process.cwd(), 'data')

// 确保数据目录存在
const ensureDataDir = () => {
  if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR, { recursive: true })
    console.log('📁 创建数据目录:', DATA_DIR)
  }
}

// 智能体数据结构
interface Agent {
  id: string
  name: string
  description: string
  department: string
  dataScope: string
  permissionLimit: string
  apiKey: string
  enabled: boolean
  createTime: string
  updateTime: string
}

// 默认智能体数据
const defaultAgents: Agent[] = [
  {
    id: '1',
    name: '财务智能助手',
    description: '专业的财务数据分析和报告生成，支持多维度财务指标解读',
    department: '财务部',
    dataScope: '全部财务数据',
    permissionLimit: '无限制',
    apiKey: 'app-xuReGCaYZmkWMn0JGAAxqBvg',
    enabled: true,
    createTime: '2025-01-01 10:00:00',
    updateTime: '2025-01-01 10:00:00'
  }
]

// 读取智能体数据
const readAgentsFromFile = (): Agent[] => {
  try {
    ensureDataDir()
    
    if (!fs.existsSync(AGENTS_FILE_PATH)) {
      // 如果文件不存在，创建默认数据
      console.log('📄 智能体数据文件不存在，创建默认数据')
      writeAgentsToFile(defaultAgents)
      return defaultAgents
    }
    
    const fileContent = fs.readFileSync(AGENTS_FILE_PATH, 'utf-8')
    const data = JSON.parse(fileContent)
    
    console.log('✅ 从文件读取了', data.agents?.length || 0, '个智能体')
    return data.agents || []
  } catch (error) {
    console.error('❌ 读取智能体数据失败:', error)
    return defaultAgents
  }
}

// 写入智能体数据
const writeAgentsToFile = (agents: Agent[]): void => {
  try {
    ensureDataDir()
    
    const data = {
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      agents: agents
    }
    
    // 先写入临时文件，然后重命名，确保原子性操作
    const tempFilePath = AGENTS_FILE_PATH + '.tmp'
    fs.writeFileSync(tempFilePath, JSON.stringify(data, null, 2), 'utf-8')
    fs.renameSync(tempFilePath, AGENTS_FILE_PATH)
    
    console.log('💾 成功保存了', agents.length, '个智能体到文件')
  } catch (error) {
    console.error('❌ 保存智能体数据失败:', error)
    throw error
  }
}

// GET - 获取所有智能体
export async function GET(request: NextRequest) {
  try {
    console.log('📖 获取智能体列表请求')
    
    const agents = readAgentsFromFile()
    
    return NextResponse.json({
      success: true,
      data: agents,
      count: agents.length,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('❌ 获取智能体列表失败:', error)
    
    return NextResponse.json({
      success: false,
      error: '获取智能体列表失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

// POST - 保存智能体列表
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { agents } = body
    
    console.log('💾 保存智能体列表请求，数量:', agents?.length || 0)
    
    if (!Array.isArray(agents)) {
      return NextResponse.json({
        success: false,
        error: '智能体数据格式错误'
      }, { status: 400 })
    }
    
    // 验证智能体数据结构
    for (const agent of agents) {
      if (!agent.id || !agent.name || !agent.apiKey) {
        return NextResponse.json({
          success: false,
          error: '智能体数据缺少必要字段'
        }, { status: 400 })
      }
    }
    
    writeAgentsToFile(agents)
    
    return NextResponse.json({
      success: true,
      message: '智能体数据保存成功',
      count: agents.length,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('❌ 保存智能体数据失败:', error)
    
    return NextResponse.json({
      success: false,
      error: '保存智能体数据失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

// PUT - 更新单个智能体
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updates } = body
    
    console.log('🔄 更新智能体请求，ID:', id)
    
    if (!id) {
      return NextResponse.json({
        success: false,
        error: '缺少智能体ID'
      }, { status: 400 })
    }
    
    const agents = readAgentsFromFile()
    const agentIndex = agents.findIndex(agent => agent.id === id)
    
    if (agentIndex === -1) {
      return NextResponse.json({
        success: false,
        error: '智能体不存在'
      }, { status: 404 })
    }
    
    // 更新智能体数据
    agents[agentIndex] = {
      ...agents[agentIndex],
      ...updates,
      updateTime: new Date().toLocaleString()
    }
    
    writeAgentsToFile(agents)
    
    return NextResponse.json({
      success: true,
      message: '智能体更新成功',
      data: agents[agentIndex]
    })
  } catch (error) {
    console.error('❌ 更新智能体失败:', error)
    
    return NextResponse.json({
      success: false,
      error: '更新智能体失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

// DELETE - 删除智能体
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const id = url.searchParams.get('id')
    
    console.log('🗑️ 删除智能体请求，ID:', id)
    
    if (!id) {
      return NextResponse.json({
        success: false,
        error: '缺少智能体ID'
      }, { status: 400 })
    }
    
    const agents = readAgentsFromFile()
    const filteredAgents = agents.filter(agent => agent.id !== id)
    
    if (filteredAgents.length === agents.length) {
      return NextResponse.json({
        success: false,
        error: '智能体不存在'
      }, { status: 404 })
    }
    
    writeAgentsToFile(filteredAgents)
    
    return NextResponse.json({
      success: true,
      message: '智能体删除成功',
      count: filteredAgents.length
    })
  } catch (error) {
    console.error('❌ 删除智能体失败:', error)
    
    return NextResponse.json({
      success: false,
      error: '删除智能体失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}
