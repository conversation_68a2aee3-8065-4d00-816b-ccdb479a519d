import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('../views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/chat',
      name: 'chat',
      component: () => import('../views/ChatView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/insight-studio',
      name: 'insight-studio',
      component: () => import('../views/InsightStudioView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/document-lab',
      name: 'document-lab',
      component: () => import('../views/DocumentLabView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/simulation',
      name: 'simulation',
      component: () => import('../views/SimulationView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/boardroom',
      name: 'boardroom',
      component: () => import('../views/BoardroomView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/agent-management',
      name: 'agent-management',
      component: () => import('../views/AgentManagementView.vue'),
      meta: { requiresAuth: true }
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const isAuthenticated = localStorage.getItem('finance_ai_token')

  if (to.meta.requiresAuth && !isAuthenticated) {
    next({ name: 'login' })
  } else if (to.name === 'login' && isAuthenticated) {
    next({ name: 'dashboard' })
  } else {
    next()
  }
})

export default router
