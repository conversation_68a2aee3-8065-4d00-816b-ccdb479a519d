'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { <PERSON>, Button, Badge, Progress } from 'antd'
import {
  ExperimentOutlined,
  BulbOutlined,
  BarChartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  RiseOutlined
} from '@ant-design/icons'
import DashboardLayout from '@/components/DashboardLayout'

export default function InsightStudioPage() {
  const router = useRouter()
  const [activeInsight, setActiveInsight] = useState<any>(null)
  const [activeModule, setActiveModule] = useState<string | null>(null)

  const insights = [
    {
      id: 1,
      title: '现金流预测模型',
      description: '基于历史数据和市场趋势的智能现金流预测',
      status: 'active',
      accuracy: 94,
      icon: <LineChartOutlined />
    },
    {
      id: 2,
      title: '风险评估引擎',
      description: '多维度风险因子分析和预警系统',
      status: 'training',
      accuracy: 87,
      icon: <Bar<PERSON>hartOutlined />
    },
    {
      id: 3,
      title: '投资组合优化',
      description: '智能资产配置和收益最大化建议',
      status: 'ready',
      accuracy: 91,
      icon: <PieChartOutlined />
    }
  ]

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <div className="p-8">
          {/* 页面标题 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-12"
          >
            <div className="text-center">
              <div className="flex items-center justify-center space-x-4 mb-4">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <ExperimentOutlined className="text-3xl text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    财务分析中心
                  </h1>
                  <p className="text-lg text-gray-600 mt-2">专业的财务数据分析与智能洞察平台</p>
                </div>
              </div>
              <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto"></div>
            </div>
          </motion.div>

          {/* 洞察模型卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {insights.map((insight, index) => (
              <motion.div
                key={insight.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.15 }}
                whileHover={{ scale: 1.05, y: -5 }}
                className="group"
              >
                <div
                  className="bg-white rounded-2xl p-6 cursor-pointer shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 group-hover:border-blue-200 h-full"
                  onClick={() => setActiveInsight(insight)}
                >
                  <div className="flex items-center justify-between mb-6">
                    <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white text-2xl shadow-lg group-hover:shadow-xl transition-shadow">
                      {insight.icon}
                    </div>
                    <div className="flex flex-col items-end">
                      <Badge 
                        status={insight.status === 'active' ? 'success' : insight.status === 'training' ? 'processing' : 'default'}
                        text={insight.status === 'active' ? '运行中' : insight.status === 'training' ? '训练中' : '就绪'}
                        className="mb-2"
                      />
                      <div className="text-xs text-gray-500">模型状态</div>
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors">
                    {insight.title}
                  </h3>
                  <p className="text-gray-600 text-sm mb-6 leading-relaxed">
                    {insight.description}
                  </p>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center text-sm">
                      <span className="font-medium text-gray-700">准确率</span>
                      <span className="font-bold text-lg text-blue-600">{insight.accuracy}%</span>
                    </div>
                    <div className="relative">
                      <Progress 
                        percent={insight.accuracy} 
                        size="small"
                        strokeColor={{
                          '0%': '#3B82F6',
                          '100%': '#8B5CF6',
                        }}
                        trailColor="#E5E7EB"
                        className="group-hover:scale-105 transition-transform"
                      />
                    </div>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>性能评级</span>
                      <span className={`px-2 py-1 rounded-full ${
                        insight.accuracy >= 90 ? 'bg-green-100 text-green-700' :
                        insight.accuracy >= 80 ? 'bg-yellow-100 text-yellow-700' :
                        'bg-red-100 text-red-700'
                      }`}>
                        {insight.accuracy >= 90 ? '优秀' : insight.accuracy >= 80 ? '良好' : '一般'}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* 快速操作中心 */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mb-12"
          >
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-2">快速操作中心</h2>
                <p className="text-gray-600">选择您需要的分析工具，开始智能财务洞察之旅</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                {[
                  {
                    key: 'create-insight',
                    icon: <BulbOutlined />,
                    title: '创建洞察',
                    desc: '智能生成财务洞察',
                    color: 'from-blue-500 to-blue-600',
                    bgColor: 'bg-blue-50',
                    hoverColor: 'hover:bg-blue-100'
                  },
                  {
                    key: 'trend-analysis',
                    icon: <RiseOutlined />,
                    title: '趋势分析',
                    desc: '深度趋势预测',
                    color: 'from-green-500 to-green-600',
                    bgColor: 'bg-green-50',
                    hoverColor: 'hover:bg-green-100'
                  },
                  {
                    key: 'data-modeling',
                    icon: <BarChartOutlined />,
                    title: '数据建模',
                    desc: '机器学习建模',
                    color: 'from-purple-500 to-purple-600',
                    bgColor: 'bg-purple-50',
                    hoverColor: 'hover:bg-purple-100'
                  },
                  {
                    key: 'experiment-management',
                    icon: <ExperimentOutlined />,
                    title: '实验管理',
                    desc: 'A/B测试管理',
                    color: 'from-orange-500 to-orange-600',
                    bgColor: 'bg-orange-50',
                    hoverColor: 'hover:bg-orange-100'
                  }
                ].map((item, index) => (
                  <motion.div
                    key={item.key}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 + index * 0.1 }}
                    whileHover={{ scale: 1.05, y: -5 }}
                    className="group cursor-pointer"
                    onClick={() => {
                      if (item.key === 'create-insight') {
                        router.push('/create-insight')
                      } else if (item.key === 'trend-analysis') {
                        router.push('/trend-analysis')
                      } else if (item.key === 'data-modeling') {
                        router.push('/data-modeling')
                      } else if (item.key === 'experiment-management') {
                        router.push('/experiment-management')
                      } else {
                        setActiveModule(item.key)
                      }
                    }}
                  >
                    <div className={`${item.bgColor} ${item.hoverColor} rounded-2xl p-6 transition-all duration-300 border-2 border-transparent group-hover:border-gray-200 group-hover:shadow-lg h-full`}>
                      <div className="text-center">
                        <div className={`w-16 h-16 bg-gradient-to-br ${item.color} rounded-2xl flex items-center justify-center text-white text-2xl mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-shadow`}>
                          {item.icon}
                        </div>
                        <h3 className="text-lg font-bold text-gray-800 mb-2 group-hover:text-gray-900">
                          {item.title}
                        </h3>
                        <p className="text-sm text-gray-600 leading-relaxed">
                          {item.desc}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* 状态信息 */}
          {!activeModule && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="text-center"
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 max-w-2xl mx-auto">
                <div className="text-6xl mb-4">🎨</div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">开始您的洞察之旅</h3>
                <p className="text-gray-600 leading-relaxed">
                  选择上方的任意功能模块，体验AI驱动的财务分析工具。
                  从数据建模到趋势预测，从风险评估到投资优化，一切尽在掌握。
                </p>
              </div>
            </motion.div>
          )}

          {/* 功能模块界面 */}
          {activeModule && (
            <div className="fixed inset-0 bg-gradient-to-br from-slate-50 to-blue-50 z-50 p-6">
              <div className="bg-white rounded-2xl h-full flex flex-col shadow-2xl border border-gray-100">
                <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-purple-50 rounded-t-2xl">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white text-xl shadow-lg">
                        {activeModule === 'create-insight' && '💡'}
                        {activeModule === 'trend-analysis' && '📈'}
                        {activeModule === 'data-modeling' && '📊'}
                        {activeModule === 'experiment-management' && '🧪'}
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-gray-800">
                          {activeModule === 'create-insight' && '创建洞察'}
                          {activeModule === 'trend-analysis' && '趋势分析'}
                          {activeModule === 'data-modeling' && '数据建模'}
                          {activeModule === 'experiment-management' && '实验管理'}
                        </h2>
                        <p className="text-gray-600 text-sm">
                          {activeModule === 'create-insight' && 'AI驱动的智能财务洞察生成'}
                          {activeModule === 'trend-analysis' && '深度财务趋势分析与预测'}
                          {activeModule === 'data-modeling' && '专业数据建模与机器学习'}
                          {activeModule === 'experiment-management' && '实验设计与A/B测试管理'}
                        </p>
                      </div>
                    </div>
                    <Button
                      onClick={() => setActiveModule(null)}
                      size="large"
                      className="bg-white border-gray-200 hover:bg-gray-50 shadow-md"
                    >
                      关闭
                    </Button>
                  </div>
                </div>
                <div className="flex-1 p-8 overflow-y-auto bg-gray-50">
                  {/* 创建洞察模块 */}
                  {activeModule === 'create-insight' && (
                    <div className="space-y-8">
                      {/* 洞察类型选择 */}
                      <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                        <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <span className="text-blue-600">🎯</span>
                          </div>
                          选择洞察类型
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {[
                            {
                              name: '财务趋势洞察',
                              desc: '深度分析收入、成本、利润等核心财务指标的变化趋势',
                              icon: '📈',
                              color: 'from-blue-500 to-blue-600',
                              bgColor: 'bg-blue-50',
                              textColor: 'text-blue-700'
                            },
                            {
                              name: '风险预警洞察',
                              desc: '智能识别潜在财务风险点，提供预警和防控建议',
                              icon: '⚠️',
                              color: 'from-red-500 to-red-600',
                              bgColor: 'bg-red-50',
                              textColor: 'text-red-700'
                            },
                            {
                              name: '投资机会洞察',
                              desc: '发现优质投资机会，提供资产配置优化方案',
                              icon: '💰',
                              color: 'from-green-500 to-green-600',
                              bgColor: 'bg-green-50',
                              textColor: 'text-green-700'
                            },
                            {
                              name: '成本优化洞察',
                              desc: '全面分析成本结构，识别降本增效的关键路径',
                              icon: '⚡',
                              color: 'from-purple-500 to-purple-600',
                              bgColor: 'bg-purple-50',
                              textColor: 'text-purple-700'
                            }
                          ].map((type, index) => (
                            <motion.div
                              key={index}
                              whileHover={{ scale: 1.02, y: -2 }}
                              className={`p-6 rounded-xl cursor-pointer transition-all duration-300 border-2 border-transparent hover:border-gray-200 hover:shadow-lg ${type.bgColor}`}
                            >
                              <div className="flex items-start space-x-4">
                                <div className={`w-12 h-12 bg-gradient-to-br ${type.color} rounded-xl flex items-center justify-center text-white text-xl shadow-lg`}>
                                  {type.icon}
                                </div>
                                <div className="flex-1">
                                  <h4 className={`font-semibold text-lg mb-2 ${type.textColor}`}>{type.name}</h4>
                                  <p className="text-gray-600 text-sm leading-relaxed">{type.desc}</p>
                                </div>
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      </div>

                      {/* 配置面板 */}
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* 数据源配置 */}
                        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                          <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                              <span className="text-green-600">🗄️</span>
                            </div>
                            数据源配置
                          </h3>
                          <div className="space-y-6">
                            <div>
                              <label className="block text-sm font-semibold text-gray-700 mb-3">选择数据源</label>
                              <div className="space-y-3">
                                {[
                                  { name: '财务报表数据', desc: 'ERP系统财务数据', checked: true },
                                  { name: '银行流水数据', desc: '银行账户交易记录', checked: true },
                                  { name: '业务系统数据', desc: 'CRM、OA等业务数据', checked: false },
                                  { name: '外部市场数据', desc: '行业指数、宏观数据', checked: false }
                                ].map((source, index) => (
                                  <label key={index} className="flex items-center p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                                    <input
                                      type="checkbox"
                                      className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                                      defaultChecked={source.checked}
                                    />
                                    <div className="ml-3">
                                      <div className="text-sm font-medium text-gray-900">{source.name}</div>
                                      <div className="text-xs text-gray-500">{source.desc}</div>
                                    </div>
                                  </label>
                                ))}
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-semibold text-gray-700 mb-3">分析时间范围</label>
                              <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option>最近3个月</option>
                                <option>最近6个月</option>
                                <option>最近1年</option>
                                <option>最近2年</option>
                              </select>
                            </div>
                          </div>
                        </div>

                        {/* 生成设置 */}
                        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                          <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                              <span className="text-purple-600">⚙️</span>
                            </div>
                            生成设置
                          </h3>
                          <div className="space-y-6">
                            <div>
                              <label className="block text-sm font-semibold text-gray-700 mb-3">分析深度</label>
                              <div className="space-y-2">
                                {['快速分析 (5分钟)', '标准分析 (15分钟)', '深度分析 (30分钟)'].map((option, index) => (
                                  <label key={index} className="flex items-center">
                                    <input type="radio" name="depth" className="w-4 h-4 text-blue-600" defaultChecked={index === 1} />
                                    <span className="ml-2 text-sm text-gray-700">{option}</span>
                                  </label>
                                ))}
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-semibold text-gray-700 mb-3">输出格式</label>
                              <div className="space-y-2">
                                {['图表报告', '文字总结', 'PPT演示', '全部格式'].map((format, index) => (
                                  <label key={index} className="flex items-center">
                                    <input type="checkbox" className="w-4 h-4 text-blue-600 rounded" defaultChecked={index < 2} />
                                    <span className="ml-2 text-sm text-gray-700">{format}</span>
                                  </label>
                                ))}
                              </div>
                            </div>
                            <Button
                              type="primary"
                              size="large"
                              className="w-full h-12 bg-gradient-to-r from-blue-500 to-purple-600 border-none text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all"
                            >
                              🚀 开始生成洞察
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
