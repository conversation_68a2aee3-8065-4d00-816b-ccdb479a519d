<template>
  <div class="min-h-screen bg-slate-50">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-slate-200">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
              <div>
                <h1 class="text-xl font-bold text-slate-900">财务智能体工作台</h1>
                <p class="text-sm text-slate-500">Executive Dashboard</p>
              </div>
            </div>
          </div>
          
          <div class="flex items-center space-x-4">
            <!-- AI叙述栏 -->
            <div class="hidden lg:block bg-blue-50 px-4 py-2 rounded-lg max-w-md">
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span class="text-sm text-blue-700 font-medium">AI洞察:</span>
              </div>
              <p class="text-sm text-blue-600 mt-1 animate-pulse">{{ aiNarrative }}</p>
            </div>
            
            <!-- 用户信息 -->
            <a-dropdown>
              <div class="flex items-center space-x-2 cursor-pointer hover:bg-slate-50 px-3 py-2 rounded-lg">
                <a-avatar size="small" class="bg-blue-600">{{ userInfo.username.charAt(0).toUpperCase() }}</a-avatar>
                <span class="text-sm font-medium text-slate-700">{{ userInfo.username }}</span>
                <DownOutlined class="text-xs text-slate-400" />
              </div>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="logout">
                    <LogoutOutlined />
                    退出登录
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </div>
    </header>

    <div class="flex">
      <!-- 侧边栏 -->
      <aside class="w-64 bg-white shadow-sm border-r border-slate-200 min-h-screen">
        <nav class="p-4">
          <div class="space-y-2">
            <router-link
              v-for="item in menuItems"
              :key="item.path"
              :to="item.path"
              class="flex items-center space-x-3 px-3 py-2 rounded-lg text-slate-700 hover:bg-blue-50 hover:text-blue-700 transition-colors"
              :class="{ 'bg-blue-50 text-blue-700': $route.path === item.path }"
            >
              <component :is="item.icon" class="w-5 h-5" />
              <span class="font-medium">{{ item.name }}</span>
            </router-link>
          </div>
        </nav>
      </aside>

      <!-- 主内容区 -->
      <main class="flex-1 p-6">
        <!-- Hero区域 - 3D资金池 -->
        <div class="mb-8">
          <div class="finance-card relative overflow-hidden h-64">
            <div class="absolute inset-0 finance-gradient"></div>
            <div class="relative z-10 p-6 text-white">
              <h2 class="text-2xl font-bold mb-2">资金池实时监控</h2>
              <p class="text-blue-100 mb-4">Dynamic Cash Pool Monitoring</p>
              <div class="flex items-end space-x-8">
                <div>
                  <p class="text-sm text-blue-200">当前净现金流</p>
                  <p class="text-3xl font-bold">¥{{ formatNumber(cashFlow) }}万</p>
                </div>
                <div>
                  <p class="text-sm text-blue-200">流动性比率</p>
                  <p class="text-2xl font-bold" :class="liquidityRatio >= 1.2 ? 'text-green-300' : 'text-yellow-300'">
                    {{ liquidityRatio.toFixed(2) }}
                  </p>
                </div>
              </div>
            </div>
            <!-- 3D效果占位 -->
            <div ref="liquidityCanvas" class="absolute bottom-0 right-0 w-48 h-32 opacity-30"></div>
          </div>
        </div>

        <!-- KPI卡片组 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div
            v-for="kpi in kpiCards"
            :key="kpi.title"
            class="finance-card hover:shadow-finance cursor-pointer transition-all duration-300"
            @mouseenter="showSparkline(kpi.id)"
            @mouseleave="hideSparkline"
          >
            <div class="flex items-center justify-between mb-4">
              <div>
                <h3 class="text-lg font-semibold text-slate-900">{{ kpi.title }}</h3>
                <p class="text-sm text-slate-500">{{ kpi.subtitle }}</p>
              </div>
              <div class="w-12 h-12 rounded-lg flex items-center justify-center" :class="kpi.bgColor">
                <component :is="kpi.icon" class="w-6 h-6" :class="kpi.iconColor" />
              </div>
            </div>
            <div class="flex items-end justify-between">
              <div>
                <p class="text-2xl font-bold" :class="kpi.valueColor">{{ kpi.value }}</p>
                <p class="text-sm" :class="kpi.changeColor">{{ kpi.change }}</p>
              </div>
              <!-- 迷你趋势线占位 -->
              <div class="w-16 h-8 opacity-0 transition-opacity duration-300" :class="{ 'opacity-100': hoveredKpi === kpi.id }">
                <div class="w-full h-full bg-gradient-to-r from-blue-400 to-blue-600 rounded"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 告警中心 -->
        <div class="mb-8">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-bold text-slate-900">风险告警中心</h2>
            <a-badge :count="alerts.length" class="mr-2">
              <BellOutlined class="text-xl text-slate-400" />
            </a-badge>
          </div>
          
          <div class="space-y-3">
            <div
              v-for="alert in alerts"
              :key="alert.id"
              class="finance-card border-l-4 cursor-pointer hover:shadow-lg transition-all duration-300"
              :class="alert.borderColor"
              @click="handleAlertClick(alert)"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 rounded-full flex items-center justify-center" :class="alert.bgColor">
                    <component :is="alert.icon" class="w-4 h-4" :class="alert.iconColor" />
                  </div>
                  <div>
                    <h4 class="font-medium text-slate-900">{{ alert.title }}</h4>
                    <p class="text-sm text-slate-500">{{ alert.description }}</p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="text-sm font-medium" :class="alert.valueColor">{{ alert.value }}</p>
                  <p class="text-xs text-slate-400">{{ alert.time }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <a-button
            v-for="action in quickActions"
            :key="action.name"
            size="large"
            class="h-16 finance-button finance-button-secondary flex flex-col items-center justify-center space-y-1"
            @click="handleQuickAction(action.action)"
          >
            <component :is="action.icon" class="w-5 h-5" />
            <span class="text-sm font-medium">{{ action.name }}</span>
          </a-button>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  DashboardOutlined,
  MessageOutlined,
  ExperimentOutlined,
  FileTextOutlined,
  PlayCircleOutlined,
  PresentationChartLineOutlined,
  SettingOutlined,
  DownOutlined,
  LogoutOutlined,
  BellOutlined,
  DollarOutlined,
  TrendingUpOutlined,
  AlertOutlined,
  BarChartOutlined,
  PieChartOutlined,
  LineChartOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const liquidityCanvas = ref<HTMLDivElement>()
const hoveredKpi = ref<string | null>(null)

// 用户信息
const userInfo = computed(() => {
  const user = localStorage.getItem('finance_ai_user')
  return user ? JSON.parse(user) : { username: 'Admin' }
})

// AI叙述内容
const aiNarrative = ref('2024Q4资产负债表显示流动性充足，建议关注应收账款回收周期...')

// 菜单项
const menuItems = [
  { name: '高管仪表板', path: '/dashboard', icon: DashboardOutlined },
  { name: '智能助手', path: '/chat', icon: MessageOutlined },
  { name: '洞察工作室', path: '/insight-studio', icon: ExperimentOutlined },
  { name: '文档实验室', path: '/document-lab', icon: FileTextOutlined },
  { name: '模拟游乐场', path: '/simulation', icon: PlayCircleOutlined },
  { name: '董事会模式', path: '/boardroom', icon: PresentationChartLineOutlined },
  { name: '智能体管理', path: '/agent-management', icon: SettingOutlined },
]

// 模拟数据
const cashFlow = ref(12580)
const liquidityRatio = ref(1.35)

// KPI卡片数据
const kpiCards = ref([
  {
    id: 'revenue',
    title: '营业收入',
    subtitle: 'Revenue',
    value: '¥45.2亿',
    change: '+12.5% 同比',
    icon: DollarOutlined,
    bgColor: 'bg-green-100',
    iconColor: 'text-green-600',
    valueColor: 'text-slate-900',
    changeColor: 'text-green-600'
  },
  {
    id: 'profit',
    title: '净利润',
    subtitle: 'Net Profit',
    value: '¥8.7亿',
    change: '+8.3% 同比',
    icon: TrendingUpOutlined,
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-600',
    valueColor: 'text-slate-900',
    changeColor: 'text-blue-600'
  },
  {
    id: 'debt',
    title: '资产负债率',
    subtitle: 'Debt Ratio',
    value: '65.2%',
    change: '-2.1% 环比',
    icon: BarChartOutlined,
    bgColor: 'bg-yellow-100',
    iconColor: 'text-yellow-600',
    valueColor: 'text-slate-900',
    changeColor: 'text-green-600'
  },
  {
    id: 'roe',
    title: 'ROE',
    subtitle: 'Return on Equity',
    value: '15.8%',
    change: '+1.2% 同比',
    icon: PieChartOutlined,
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600',
    valueColor: 'text-slate-900',
    changeColor: 'text-green-600'
  },
  {
    id: 'cash',
    title: '现金及等价物',
    subtitle: 'Cash & Equivalents',
    value: '¥23.4亿',
    change: '+5.7% 环比',
    icon: LineChartOutlined,
    bgColor: 'bg-indigo-100',
    iconColor: 'text-indigo-600',
    valueColor: 'text-slate-900',
    changeColor: 'text-green-600'
  },
  {
    id: 'risk',
    title: '风险评级',
    subtitle: 'Risk Rating',
    value: 'AAA',
    change: '稳定',
    icon: AlertOutlined,
    bgColor: 'bg-emerald-100',
    iconColor: 'text-emerald-600',
    valueColor: 'text-slate-900',
    changeColor: 'text-emerald-600'
  }
])

// 告警数据
const alerts = ref([
  {
    id: 1,
    title: '流动比率预警',
    description: '预计6周后现金池将下穿警戒线',
    value: '1.18',
    time: '2分钟前',
    icon: AlertOutlined,
    bgColor: 'bg-red-100',
    iconColor: 'text-red-600',
    valueColor: 'text-red-600',
    borderColor: 'border-l-red-500'
  },
  {
    id: 2,
    title: '应收账款异常',
    description: '某子公司应收账款周转天数超过90天',
    value: '¥2.3亿',
    time: '15分钟前',
    icon: AlertOutlined,
    bgColor: 'bg-yellow-100',
    iconColor: 'text-yellow-600',
    valueColor: 'text-yellow-600',
    borderColor: 'border-l-yellow-500'
  }
])

// 快速操作
const quickActions = [
  { name: '生成月报', action: 'generate-report', icon: FileTextOutlined },
  { name: '现金流预测', action: 'cash-forecast', icon: TrendingUpOutlined },
  { name: '风险分析', action: 'risk-analysis', icon: AlertOutlined },
  { name: '智能问答', action: 'ai-chat', icon: MessageOutlined }
]

// 方法
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

const showSparkline = (kpiId: string) => {
  hoveredKpi.value = kpiId
}

const hideSparkline = () => {
  hoveredKpi.value = null
}

const handleAlertClick = (alert: any) => {
  message.info(`正在分析: ${alert.title}`)
  // 这里可以跳转到详细分析页面或打开模态框
}

const handleQuickAction = (action: string) => {
  switch (action) {
    case 'generate-report':
      message.success('正在生成月度财务报告...')
      break
    case 'cash-forecast':
      message.success('正在进行现金流预测分析...')
      break
    case 'risk-analysis':
      message.success('正在执行风险评估...')
      break
    case 'ai-chat':
      router.push('/chat')
      break
  }
}

const logout = () => {
  localStorage.removeItem('finance_ai_token')
  localStorage.removeItem('finance_ai_user')
  message.success('已安全退出')
  router.push('/')
}

// 生命周期
onMounted(() => {
  // 这里可以初始化3D效果
  // initLiquidityVisualization()
  
  // 定期更新AI叙述
  setInterval(() => {
    const narratives = [
      '2024Q4资产负债表显示流动性充足，建议关注应收账款回收周期...',
      '现金流预测显示下季度资金需求增加，建议提前安排融资计划...',
      '风险评级保持AAA级别，各项财务指标均在健康范围内...',
      '投资组合表现良好，建议适当增加低风险固收类产品配置...'
    ]
    aiNarrative.value = narratives[Math.floor(Math.random() * narratives.length)]
  }, 10000)
})
</script>
