import { NextRequest, NextResponse } from 'next/server'

// Dify API配置
const DIFY_BASE_URL = process.env.DIFY_API_URL || 'https://ai.hongtai-idi.com/v1'
const DIFY_API_KEY = process.env.DIFY_API_KEY || 'app-xuReGCaYZmkWMn0JGAAxqBvg'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { endpoint, customApiKey, ...requestData } = body

    // 使用自定义API密钥或默认密钥
    const apiKey = customApiKey || DIFY_API_KEY

    console.log('🔧 Dify API代理请求:', {
      endpoint,
      baseUrl: DIFY_BASE_URL,
      apiKey: apiKey.substring(0, 10) + '...',
      usingCustomKey: !!customApiKey,
      requestData: {
        ...requestData,
        query: requestData.query?.substring(0, 50) + '...'
      }
    })

    // 构建完整的API URL
    const apiUrl = `${DIFY_BASE_URL}/${endpoint}`

    // 准备请求头
    const headers: Record<string, string> = {
      'Authorization': `Bearer ${apiKey}`,
    }

    // 判断请求方法和内容类型
    let method = 'POST'
    let requestBody: string | undefined = undefined

    // 如果endpoint包含parameters或conversations，使用GET请求
    if (endpoint.includes('parameters') || endpoint.includes('conversations')) {
      method = 'GET'
      headers['Accept'] = 'application/json'
    } else {
      // 其他情况使用POST请求
      headers['Content-Type'] = 'application/json'
      requestBody = JSON.stringify(requestData)

      // 如果是流式请求，设置相应的Accept头
      if (requestData.response_mode === 'streaming') {
        headers['Accept'] = 'text/event-stream'
      } else {
        headers['Accept'] = 'application/json'
      }
    }

    console.log('📤 发送到Dify API:', {
      url: apiUrl,
      method,
      headers: {
        ...headers,
        'Authorization': 'Bearer ' + apiKey.substring(0, 10) + '...'
      }
    })

    // 发送请求到Dify API
    const response = await fetch(apiUrl, {
      method,
      headers,
      body: requestBody
    })

    console.log('📥 Dify API响应:', {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    })

    // 检查响应状态
    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Dify API错误:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      })

      return NextResponse.json(
        { 
          error: 'Dify API调用失败',
          details: `${response.status} - ${errorText}`,
          code: 'dify_api_error'
        },
        { status: response.status }
      )
    }

    // 处理流式响应
    if (requestData.response_mode === 'streaming') {
      console.log('🌊 处理流式响应')
      
      // 创建一个新的ReadableStream来转发数据
      const stream = new ReadableStream({
        start(controller) {
          const reader = response.body?.getReader()
          if (!reader) {
            controller.close()
            return
          }

          function pump(): Promise<void> {
            return reader.read().then(({ done, value }) => {
              if (done) {
                controller.close()
                return
              }
              controller.enqueue(value)
              return pump()
            })
          }

          return pump()
        }
      })

      return new NextResponse(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      })
    } else {
      // 处理普通JSON响应
      const data = await response.json()
      console.log('✅ Dify API响应成功')
      
      return NextResponse.json(data, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      })
    }

  } catch (error) {
    console.error('❌ Dify API代理错误:', error)
    
    return NextResponse.json(
      { 
        error: '服务器内部错误',
        details: error instanceof Error ? error.message : '未知错误',
        code: 'internal_server_error'
      },
      { status: 500 }
    )
  }
}

// 处理OPTIONS请求（CORS预检）
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400'
    }
  })
}

// 处理GET请求（连接测试）
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Dify API连接测试')
    
    // 测试获取应用参数
    const response = await fetch(`${DIFY_BASE_URL}/parameters?user=connection_test`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${DIFY_API_KEY}`,
        'Accept': 'application/json'
      }
    })

    if (response.ok) {
      console.log('✅ Dify API连接测试成功')
      return NextResponse.json({
        status: 'success',
        message: 'Dify API连接正常',
        timestamp: new Date().toISOString()
      })
    } else {
      const errorText = await response.text()
      console.warn('⚠️ Dify API连接测试失败:', response.status, errorText)
      
      return NextResponse.json({
        status: 'error',
        message: 'Dify API连接失败',
        details: `${response.status} - ${errorText}`,
        timestamp: new Date().toISOString()
      }, { status: response.status })
    }

  } catch (error) {
    console.error('❌ Dify API连接测试错误:', error)
    
    return NextResponse.json({
      status: 'error',
      message: '连接测试失败',
      details: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
