import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { DifyApiService, DifyConfig, DEFAULT_AGENTS, createDifyService } from '@/services/difyApi'

export interface Agent {
  id: string
  name: string
  description: string
  appType: 'chatflow' | 'workflow' | 'completion'
  category: string
  apiKey: string
  enabled: boolean
  lastUsed?: Date
  totalCalls?: number
  avgResponseTime?: number
  successRate?: number
}

export interface AgentConversation {
  id: string
  agentId: string
  conversationId?: string
  messages: Array<{
    id: string
    type: 'user' | 'assistant'
    content: string
    timestamp: Date
    messageId?: string
  }>
  createdAt: Date
  updatedAt: Date
}

export const useAgentStore = defineStore('agent', () => {
  // 状态
  const agents = ref<Agent[]>([...DEFAULT_AGENTS])
  const conversations = ref<AgentConversation[]>([])
  const currentConversation = ref<AgentConversation | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // Dify 基础配置
  const difyBaseUrl = ref('https://ai.hongtai-idi.com/v1')
  const currentUser = ref('finance-user-001')

  // 计算属性
  const enabledAgents = computed(() => 
    agents.value.filter(agent => agent.enabled && agent.apiKey)
  )

  const agentsByCategory = computed(() => {
    const grouped: Record<string, Agent[]> = {}
    enabledAgents.value.forEach(agent => {
      if (!grouped[agent.category]) {
        grouped[agent.category] = []
      }
      grouped[agent.category].push(agent)
    })
    return grouped
  })

  const getAgentById = computed(() => (id: string) => 
    agents.value.find(agent => agent.id === id)
  )

  // 方法
  const updateAgent = (agentId: string, updates: Partial<Agent>) => {
    const index = agents.value.findIndex(agent => agent.id === agentId)
    if (index !== -1) {
      agents.value[index] = { ...agents.value[index], ...updates }
      saveAgentsToStorage()
    }
  }

  const enableAgent = (agentId: string, apiKey: string) => {
    updateAgent(agentId, { apiKey, enabled: true })
  }

  const disableAgent = (agentId: string) => {
    updateAgent(agentId, { enabled: false })
  }

  const createDifyServiceForAgent = (agent: Agent): DifyApiService => {
    const config: DifyConfig = {
      baseUrl: difyBaseUrl.value,
      apiKey: agent.apiKey,
      appType: agent.appType
    }
    return createDifyService(config)
  }

  // 发送消息到智能体
  const sendMessageToAgent = async (agentId: string, message: string, files?: File[]) => {
    const agent = getAgentById.value(agentId)
    if (!agent || !agent.enabled) {
      throw new Error('智能体未启用或不存在')
    }

    isLoading.value = true
    error.value = null

    try {
      const difyService = createDifyServiceForAgent(agent)
      
      // 获取或创建对话
      let conversation = conversations.value.find(c => c.agentId === agentId && !c.conversationId)
      if (!conversation) {
        conversation = {
          id: Date.now().toString(),
          agentId,
          messages: [],
          createdAt: new Date(),
          updatedAt: new Date()
        }
        conversations.value.push(conversation)
      }

      // 添加用户消息
      const userMessage = {
        id: Date.now().toString(),
        type: 'user' as const,
        content: message,
        timestamp: new Date()
      }
      conversation.messages.push(userMessage)
      currentConversation.value = conversation

      // 处理文件上传
      let uploadedFiles: any[] = []
      if (files && files.length > 0) {
        for (const file of files) {
          const uploadResult = await difyService.uploadFile(file, currentUser.value)
          uploadedFiles.push({
            type: 'image',
            transfer_method: 'local_file',
            upload_file_id: uploadResult.id
          })
        }
      }

      // 根据智能体类型调用不同API
      let response: any
      const startTime = Date.now()

      if (agent.appType === 'chatflow') {
        response = await difyService.sendChatMessage({
          inputs: {},
          query: message,
          response_mode: 'streaming',
          conversation_id: conversation.conversationId,
          user: currentUser.value,
          files: uploadedFiles.length > 0 ? uploadedFiles : undefined
        })
      } else if (agent.appType === 'workflow') {
        response = await difyService.runWorkflow({
          inputs: { query: message },
          response_mode: 'streaming',
          user: currentUser.value,
          files: uploadedFiles.length > 0 ? uploadedFiles : undefined
        })
      } else if (agent.appType === 'completion') {
        response = await difyService.generateCompletion({
          inputs: { text: message },
          response_mode: 'streaming',
          user: currentUser.value
        })
      }

      // 处理流式响应
      if (response instanceof ReadableStream) {
        let assistantMessage = {
          id: (Date.now() + 1).toString(),
          type: 'assistant' as const,
          content: '',
          timestamp: new Date(),
          messageId: ''
        }
        conversation.messages.push(assistantMessage)

        for await (const chunk of difyService.parseStreamResponse(response)) {
          if (chunk.event === 'message') {
            assistantMessage.content += chunk.answer || ''
          } else if (chunk.event === 'message_end') {
            assistantMessage.messageId = chunk.message_id
            if (chunk.conversation_id && !conversation.conversationId) {
              conversation.conversationId = chunk.conversation_id
            }
          } else if (chunk.event === 'workflow_finished') {
            // 工作流完成
            if (chunk.data?.outputs) {
              assistantMessage.content = chunk.data.outputs.summary || 
                                       chunk.data.outputs.result || 
                                       JSON.stringify(chunk.data.outputs)
            }
          }
        }

        // 更新统计信息
        const responseTime = Date.now() - startTime
        updateAgentStats(agentId, responseTime, true)
      }

      conversation.updatedAt = new Date()
      saveConversationsToStorage()

    } catch (err: any) {
      error.value = err.message || '发送消息失败'
      updateAgentStats(agentId, 0, false)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 更新智能体统计信息
  const updateAgentStats = (agentId: string, responseTime: number, success: boolean) => {
    const agent = getAgentById.value(agentId)
    if (!agent) return

    const totalCalls = (agent.totalCalls || 0) + 1
    const avgResponseTime = agent.avgResponseTime 
      ? (agent.avgResponseTime + responseTime) / 2 
      : responseTime
    const successRate = agent.successRate 
      ? (agent.successRate * (totalCalls - 1) + (success ? 1 : 0)) / totalCalls
      : success ? 1 : 0

    updateAgent(agentId, {
      lastUsed: new Date(),
      totalCalls,
      avgResponseTime,
      successRate
    })
  }

  // 创建新对话
  const createNewConversation = (agentId: string) => {
    const conversation: AgentConversation = {
      id: Date.now().toString(),
      agentId,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }
    conversations.value.push(conversation)
    currentConversation.value = conversation
    return conversation
  }

  // 删除对话
  const deleteConversation = (conversationId: string) => {
    const index = conversations.value.findIndex(c => c.id === conversationId)
    if (index !== -1) {
      conversations.value.splice(index, 1)
      if (currentConversation.value?.id === conversationId) {
        currentConversation.value = null
      }
      saveConversationsToStorage()
    }
  }

  // 测试智能体连接
  const testAgentConnection = async (agentId: string): Promise<boolean> => {
    const agent = getAgentById.value(agentId)
    if (!agent || !agent.apiKey) return false

    try {
      const difyService = createDifyServiceForAgent(agent)
      
      // 发送测试消息
      const testMessage = '你好，这是一个连接测试。'
      
      if (agent.appType === 'chatflow') {
        await difyService.sendChatMessage({
          inputs: {},
          query: testMessage,
          response_mode: 'blocking',
          user: currentUser.value
        })
      } else if (agent.appType === 'workflow') {
        await difyService.runWorkflow({
          inputs: { query: testMessage },
          response_mode: 'blocking',
          user: currentUser.value
        })
      } else if (agent.appType === 'completion') {
        await difyService.generateCompletion({
          inputs: { text: testMessage },
          response_mode: 'blocking',
          user: currentUser.value
        })
      }

      return true
    } catch (err) {
      console.error('智能体连接测试失败:', err)
      return false
    }
  }

  // 存储管理
  const saveAgentsToStorage = () => {
    localStorage.setItem('finance_ai_agents', JSON.stringify(agents.value))
  }

  const loadAgentsFromStorage = () => {
    const stored = localStorage.getItem('finance_ai_agents')
    if (stored) {
      try {
        const parsed = JSON.parse(stored)
        // 合并默认智能体和存储的配置
        agents.value = DEFAULT_AGENTS.map(defaultAgent => {
          const storedAgent = parsed.find((a: Agent) => a.id === defaultAgent.id)
          return storedAgent ? { ...defaultAgent, ...storedAgent } : defaultAgent
        })
      } catch (err) {
        console.error('加载智能体配置失败:', err)
      }
    }
  }

  const saveConversationsToStorage = () => {
    localStorage.setItem('finance_ai_conversations', JSON.stringify(conversations.value))
  }

  const loadConversationsFromStorage = () => {
    const stored = localStorage.getItem('finance_ai_conversations')
    if (stored) {
      try {
        const parsed = JSON.parse(stored)
        conversations.value = parsed.map((c: any) => ({
          ...c,
          createdAt: new Date(c.createdAt),
          updatedAt: new Date(c.updatedAt),
          messages: c.messages.map((m: any) => ({
            ...m,
            timestamp: new Date(m.timestamp)
          }))
        }))
      } catch (err) {
        console.error('加载对话历史失败:', err)
      }
    }
  }

  // 初始化
  const initialize = () => {
    loadAgentsFromStorage()
    loadConversationsFromStorage()
  }

  return {
    // 状态
    agents,
    conversations,
    currentConversation,
    isLoading,
    error,
    difyBaseUrl,
    currentUser,
    
    // 计算属性
    enabledAgents,
    agentsByCategory,
    getAgentById,
    
    // 方法
    updateAgent,
    enableAgent,
    disableAgent,
    sendMessageToAgent,
    createNewConversation,
    deleteConversation,
    testAgentConnection,
    initialize,
    saveAgentsToStorage,
    saveConversationsToStorage
  }
})
